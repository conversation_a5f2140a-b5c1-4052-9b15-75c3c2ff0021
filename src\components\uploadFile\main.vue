<template>
  <el-upload 
    :action="action"
    :accept="accept" 
    :file-list="fileLists"
    :multiple="multiple"
    :show-file-list="showFileList" 
    :on-success="fileSuccess" 
    :before-upload="beforeUpload" 
    :on-error="fileError"
    :on-preview="filePreview" 
    :on-exceed="fileExceed"
    :before-remove="beforeRemove" 
    :headers="importHeaders" 
    :list-type="listType"
    :limit="limit"
    :auto-upload="autoUpload">
    <slot></slot>
  </el-upload>
</template>

<script>
import { getToken } from "@/utils/auth";
let baseUrl = process.env.VUE_APP_BASE_API+'/user-server/file/uploadFile'
export default { 
  props: {
    rowData:[String,Object,Number],

    // 上传的地址
    action:{
      type:String,
      default(){
        return baseUrl
      }
    },

    // 是否默认上传，默认是
    autoUpload:{
      type:Boolean,
      default(){
        return true
      }
    },

    // 文件列表的类型
    listType:{
      type:String,
      default(){
        return 'text'
      }
    },

    // 是否支持多文件上传
    multiple:{
      type:Bo<PERSON>an,
      default(){
        return false
      }
    },

    // 是否显示上传文件列表 true 显示  false 不显示
    showFileList: {
      type: Boolean,
      default() {
        return false
      }
    },

    // file文件列表
    fileList: {
      type: Array,
      default() {
        return []
      }
    },

    // 上传文件类型
    accept: {
      type: String,
      default() {
        return ''
      }
    },

    // 上传文件个数
    limit: {
      type: Number,
      default() {
        return 10
      }
    }
  },
  computed: {
    fileLists(){
      return this.fileList
    }
  },

  data() {
    return {
      importHeaders: {  "X-Token": "Bearer " + getToken() },
    }
  },

  methods: {
    // 上传文件前
    beforeUpload(file) {
      let rowData = this.rowData
      let state = true
      this.$emit('before-upload', file, val => {
        state = val
      },rowData)
      return state
    },

    // 上传文件成功
    fileSuccess(response, file,fileList) {
      let rowData = this.rowData
      switch(response.code){
        case 1:
          this.$emit('file-success', response, file,fileList,rowData)
          break;
        default:
          let message = response.msg || '请求失败'
          this.$message({
              type:'error',
              message,
              duration:1500
          })
          this.$emit('file-error', response,rowData)
          break;
      }
    },

    // 文件上传失败
    fileError(error) {
      let rowData = this.rowData
      this.$emit('file-error', error,rowData)
    },

    // 删除已上传文件前
    beforeRemove(file, fileList) {
      let rowData = this.rowData
      this.$emit('before-remove', file, fileList,rowData)
    },

    // 点击下载文件
    filePreview() {
      let rowData = this.rowData
      this.$emit('file-preview',)
    },

    // 文件超出最大数限制
    fileExceed(files,fileList){
      let rowData = this.rowData
      this.$emit('file-exceed',files,fileList,rowData)
    }

    
  },


}

</script>

<style scoped lang='less'></style>