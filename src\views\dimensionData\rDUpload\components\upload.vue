<template>
  <div>
    <!-- 隐藏的文件输入框，用于手动触发文件选择 -->
    <input
      type="file"
      ref="fileInput"
      style="display: none"
      @change="handleFileChange"
      :disabled="mode === 'view'"
    />

    <!-- 显示区域 -->
    <div
      class="upload-trigger"
      :class="{ 'is-disabled': mode === 'view' }"
      @click="openFileSelector"
    >
      <!-- 未上传时显示+ -->
      <i v-if="fileList.length == 0" class="el-icon-plus"></i>
      <!-- 如果为图片，显示图片 -->
      <img
        v-else-if="
          fileList.length > 0 &&
          (fileList[0].filePath.includes('.jpg') ||
            fileList[0].filePath.includes('.JPG') ||
            fileList[0].filePath.includes('.png') ||
            fileList[0].filePath.includes('.PNG'))
        "
        :src="imgUrl + fileList[0].filePath"
        alt=""
        style="width: 100%; height: 100%"
      />
      <!-- 否则显示一个文件夹icon -->
      <i v-else class="el-icon-document"></i>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  props: {
    mode: {
      type: String,
      default: "view",
    },
    index: {
      type: Number,
      default: 0,
    },
    file: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 上传地址
      uploadUrl: process.env.VUE_APP_BASE_API + "/user-server/file/uploadFile",
      imgUrl: process.env.VUE_APP_FILE_API,
      // 设置上传请求头
      headers: {
        "X-Token": "Bearer " + getToken(),
      },
      fileList: [],
    };
  },
  watch: {
    file: {
      immediate: true,
      handler(newVal) {
        this.fileList = [...newVal];
      },
    },
  },
  methods: {
    // 打开文件选择器
    openFileSelector() {
      if (this.mode !== "view") {
        this.$refs.fileInput.click();
      }
    },
    // 处理文件选择
    handleFileChange(e) {
      const file = e.target.files[0];
      if (!file) return;

      // 创建FormData对象上传文件
      const formData = new FormData();
      formData.append("file", file);

      // 设置请求头
      const headers = {
        "X-Token": "Bearer " + getToken(),
      };

      // 使用fetch API上传文件
      fetch(this.uploadUrl, {
        method: "POST",
        headers: headers,
        body: formData,
      })
        .then((response) => response.json())
        .then((result) => {
          if (result.data) {
            // 上传成功
            this.fileList = [result.data];
            // 通知父组件
            this.$emit("FileSuccess", result.data, this.index);
          } else {
            // 上传失败
            this.$message.error(result.msg || "上传失败");
          }
        })
        .catch((error) => {
          console.error("上传出错:", error);
          this.$message.error("上传出错");
        })
        .finally(() => {
          // 清空文件输入框，以便下次选择同一文件时也能触发change事件
          this.$refs.fileInput.value = "";
        });
    },
  },
};
</script>

<style lang="less" scoped>
.upload-trigger {
  width: 148px;
  height: 148px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;

  &:hover {
    border-color: #409eff;
  }

  .el-icon-plus,
  .el-icon-document {
    font-size: 28px;
    color: #c0c4cc;
  }

  &.is-disabled {
    cursor: not-allowed;
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
  }
}

.upload-label {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}
</style>
