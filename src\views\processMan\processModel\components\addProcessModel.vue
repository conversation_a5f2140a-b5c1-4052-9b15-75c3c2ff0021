<template>
    <el-dialog
    :title="rowData ? '新增' : '修改'"
    :visible.sync="dialogVisible"
    width="40%"
    >
        <div class="dialog_con" v-loading="loading">
            <el-form :model="form" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm" >
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="模型标识" prop="name">
                            <el-input size="small" v-model="form.modelKey" :disabled="true" placeholder="模型标识" clearable></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="模型名称" prop="modelName">
                            <el-input size="small" v-model="form.modelName" :disabled="rowData ? true : false" placeholder="模型名称" clearable></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="流程分类" prop="category">
                            <el-select v-model="form.category" size="small" placeholder="流程分类" clearable>
                                <el-option
                                v-for="item in typeOptions"
                                :key="item.id"
                                :label="item.categoryName"
                                :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="描述" >
                            <el-input
                                clearable
                                type="textarea"
                                :rows="5"
                                resize="none"
                                placeholder="描述"
                                v-model="form.description" >
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" @click="dialogVisible = false">取 消</el-button>
            <el-button size="small" :loading="btnLoading" type="primary" @click="confim()">确 定</el-button>
        </div>
    </el-dialog>
</template>
<script>
import {addModel,editModel} from '@/api/processMan/processModel.js'
import {processClassListAll} from '@/api/processMan/processClass.js'
export default {
    data(){
        return {
            btnLoading:false,
            dialogVisible:false,
            rowData:null,
            loading:false,
            typeOptions:[],
            form:{
                modelKey:'',
                modelName:'',
                category:'',
                description:''
            },
            rules:{
                modelKey: [
                    { required: true, message: '请输入模型标识', trigger: 'blur' }
                ],
                modelName: [
                    { required: true, message: '请输入模型名称', trigger: 'blur' },
                ],
                category: [
                    { required: true, message: '请输入流程分类', trigger: 'blur' }
                ]
            }
        }
    },
    methods:{
        async init(row){
            this.dialogVisible = true
            await this.getProcessClassList()
            this.rowData = row
            const dateTime = new Date().getTime();
            this.form.modelKey =  `Process_${dateTime}`
            this.$nextTick(()=>{
                this.$refs['form'].resetFields();
                if(row){
                    this.form = JSON.parse(JSON.stringify(row))
                    this.form.category = row.category
                    this.form.modelName = row.modelName
                }else{
                    this.form.category = ''
                    this.form.modelName =  `业务流程_${dateTime}`
                    this.form.description = ''
                }
            })
        },

        // 获取流程分类
        async getProcessClassList(){
            this.loading = true
            await processClassListAll().then((res)=>{
                let resData = res.data
                this.typeOptions = resData.map((item)=>{
                    // return {categoryName:`${item.moduleName}-${item.categoryName}`,id:item.id}
                    return {categoryName:`${item.categoryName}`,id:item.id}
                })
                this.loading = false
            }).catch(()=>{
                this.loading = false
            })
        },

        confim(){
            this.$refs['form'].validate((valid) => {
                if(valid){
                    this.btnLoading = true
                    let params = {
                        ...this.form
                    }
                    if(this.rowData){
                        editModel(params).then((res)=>{
                            this.$message({
                                type:'success',
                                message:'操作成功',
                                duration:1500
                            })
                            this.dialogVisible = false
                            this.btnLoading = false
                            this.$emit('hideDialog')
                        }).catch(()=>{
                            this.btnLoading = false
                        })
                    }else{
                        addModel(params).then((res)=>{
                            this.$message({
                                type:'success',
                                message:'操作成功',
                                duration:1500
                            })
                            this.dialogVisible = false
                            this.btnLoading = false
                            this.$emit('hideDialog')
                        }).catch(()=>{
                            this.btnLoading = false
                        })
                    }
                }
            })
        }
    }
}
</script>
<style lang="less" scoped>
.demo-ruleForm {
    /deep/.el-form-item {
        .el-form-item__content {
            .el-select {
                width:100%;
            }
        }
    }
    .avatar-uploader {
        display:inline-block;
        width:50px;
        height:50px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        /deep/.el-upload {
            display:flex;
            align-items:center;
            justify-content: center;
            width:100%;
            height:100%;
            .avatar {
                width:100%;
                height:100%;
            }
        }
        /deep/.el-loading-mask {
            .el-loading-spinner {
                margin-top:-17px;
            }
        }
    }
    .avtar_hint {
        display:inline-block;
        font-size:12px;
        line-height:20px;
        color:#F56C6C;
        margin-left:10px;
    }
}
</style>
