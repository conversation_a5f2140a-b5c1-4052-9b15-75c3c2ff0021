<template>
    <div class="rich_text">
        <div style="border: 1px solid #ccc;height:100%">
            <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
            <Editor class="editor" :style="{'height': workReport ?height+'%' : height+'px', 'overflow-y': 'hidden'}" v-model="content"
                :defaultConfig="editorConfig " :mode="mode" @onCreated="onCreated" />
        </div>
    </div>
</template>
<script>
import { Editor, Toolbar, } from "@wangeditor/editor-for-vue";
export default {
    components: { Editor, Toolbar },
    props: {
        value: String,
        hbcApp: {
            type: <PERSON><PERSON><PERSON>,
            default() {
                return false
            }
        },
        height: {
            type: Number,
            default() {
                return 200
            }
        },
        disabled: {
            type: Boolean,
            default() {
                return false
            }
        },
        workReport:{
            type: <PERSON>olean,
            default() {
                return false
            }
        },
        excludeKeys:{
            type: Array,
            default() {
                return [
                    'group-video',  // 视屏
                    'insertTable',  // 表格
                    'codeBlock', // 代码块
                    'emotion',  // 表情
                ]
            }
        },
    },
    data() {
        const server = this.hbcApp ? `/appFile/file` : `/file/minio/upload/public/${this.moduleId}`
        const baseImg = process.env.VUE_APP_BASE_IMG
        const hbcApp = this.hbcApp
        return {
            content: '',
            editor: null,
            toolbarConfig: {  // 配置菜单功能，具体看工具栏配置  https://www.wangeditor.com/v5/toolbar-config.html#toolbarkeys
                excludeKeys: this.excludeKeys   // 去掉某些配置

            },
            editorConfig: {
                withCredentials: true,
                MENU_CONF: {
                    uploadImage: {
                        server: server,  // 图片上传地址
                        maxFileSize: 4 * 1024 * 1024, // 1M
                        // 最多可上传几个文件，默认为 100
                        maxNumberOfFiles: ['image/*'],
                        // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
                        allowedFileTypes: [],
                        // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
                        fieldName: "file",  // 文件上传参数名
                        meta: {
                            //官网中把token放到了这里，但是请求的时候会看不到token
                        },
                        headers: {
                            //所以token放这里
                            Authorization: 'Bearer ' + sessionStorage.getItem('token'),
                        },
                        // 将 meta 拼接到 url 参数中，默认 false
                        metaWithUrl: false,
                        // 跨域是否传递 cookie ，默认为 false
                        withCredentials: true,
                        // 超时时间，默认为 10 秒
                        timeout: 5 * 1000, // 5 秒
                        customInsert(res, insertFn) {
                            // JS 语法
                            // res 即服务端的返回结果
                            // 从 res 中找到 url alt href ，然后插图图片

                            let imgUrl = hbcApp ? `${baseImg}/${res.data}` : res.data.url
                            insertFn(imgUrl);
                        },
                    },
                },
                placeholder: "请输入内容...",
            },
            mode: "default", // or 'simple'
        }
    },
    methods: {
        onCreated(editor) {
            this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
            if (this.disabled) {
                editor.disable()
            }
        },
    },
    watch: {
        value: {
            handler(newVal, oldVal) {
                this.content = newVal
            },
            deep: true,
            immediate: true
        },
        content(newVal, oldVal) {
            this.$emit('input', newVal)
        }
    },
    beforeDestroy() {
        const editor = this.editor;
        if (editor == null) return;
        editor.destroy(); // 组件销毁时，及时销毁编辑器
    },
}
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style lang="less" scoped>
.rich_text{
    height: 100%;
}
.editor {
    /deep/.w-e-scroll::-webkit-scrollbar-thumb {
        // 更改内部滚动条样式
        width: 5px;
        background-color: #C1C1C1;
        border-radius: 10px;
    }

    /deep/.w-e-scroll::-webkit-scrollbar {
        // 更改外部滚动条样式
        width: 5px;
        height: 2px;
        background: none;
        border-radius: 10px;
    }
}
</style>