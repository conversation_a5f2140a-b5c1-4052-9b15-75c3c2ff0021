<template>
  <el-dialog title="评级维护" :visible.sync="dialogVisible" width="750px" :before-close="handleClose" v-loading="loading">
    <div class="rating-form">
      <!-- 区间组 -->
      <div v-for="(item, index) in ratingRanges" :key="index" class="range-group">
        <div class="range-row">
          <span class="prefix-text">区间{{ index + 1 }}</span>
          <el-input v-model="item.startScore" placeholder="请输入" class="range-input" v-number-only
            @input="handleNumberInput($event, item, 'startScore')">
            <template slot="appendScore">分</template>
          </el-input>
          <span class="separator">—</span>
          <el-input v-model="item.endScore" placeholder="请输入" class="range-input" v-number-only
            @input="handleNumberInput($event, item, 'endScore')">
            <template slot="appendScore">分</template>
          </el-input>
          <span class="prefix-text">为</span>
          <span class="name-text">{{ item.name }}</span>
          <el-button type="text" icon="el-icon-delete" class="delete-btn" @click="confirmDelete(index)"
            v-if="ratingRanges.length > 1" />
        </div>
      </div>

      <!-- 添加按钮 -->
      <el-button type="primary" plain size="small" icon="el-icon-plus" class="add-button" @click="addRange">
        新增等级
      </el-button>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { apiGetRatingRanges, apiUpdateRatingRanges } from '@/api/appraiseManager/supplySideAppraise'

export default {
  name: 'RatingMaintenanceDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      ratingRanges: []
    }
  },
  watch: {
    visible: {
      async handler(val) {
        this.dialogVisible = val
        if (val) {
          // 当弹窗打开时获取数据
          await this.fetchRatingRanges()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
        this.ratingRanges = []
      }
    }
  },
  methods: {
    async fetchRatingRanges() {
      try {
        this.loading = true
        // 模拟接口请求
        const res = await apiGetRatingRanges()
        if (res.data && res.data.length > 0) {
          this.ratingRanges = res.data
        }
        this.reorderLevels()
      } catch (error) {
        this.$message.error('获取评级范围失败')
        console.error('获取评级范围失败:', error)
      } finally {
        this.loading = false
      }
    },
    async handleConfirm() {
      try {
        // 表单验证
        if (!this.validateRanges()) {
          return
        }

        this.loading = true
        // 模拟保存接口
        await apiUpdateRatingRanges(this.ratingRanges)
        this.$message.success('保存成功')
        this.dialogVisible = false
        this.$emit('refresh') // 通知父组件刷新数据
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存失败:', error)
      } finally {
        this.loading = false
      }
    },
    validateRanges() {
      for (const range of this.ratingRanges) {
        if (range.startScore === '' || range.endScore === '') {
          this.$message.warning('请填写完整的分数范围')
          return false
        }
        if (parseFloat(range.startScore) >= parseFloat(range.endScore)) {
          this.$message.warning('起始分数必须小于结束分数')
          return false
        }
      }
      return true
    },
    handleClose() {
      this.dialogVisible = false
    },
    addRange() {
      const nextLevel = this.getNextLevel(this.ratingRanges.length + 1)
      this.ratingRanges.push({ startScore: '', endScore: '', name: nextLevel })
    },
    getNextLevel(index) {
      const levels = ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X']
      return (levels[index - 1] || `第${index}`) + '级供应商'
    },
    confirmDelete(index) {
      this.$confirm('确定要删除这个区间吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handleDelete(index)
      }).catch(() => { })
    },
    handleDelete(index) {
      if (this.ratingRanges.length > 1) {
        this.ratingRanges.splice(index, 1)
        this.reorderLevels()
      }
    },
    reorderLevels() {
      this.ratingRanges.forEach((item, index) => {
        item.name = this.getNextLevel(index + 1)
      })
    },
    handleNumberInput(value, item, field) {
      const num = parseFloat(value)
      if (isNaN(num)) {
        item[field] = ''
      } else {
        item[field] = Math.min(Math.max(num, 0), 100)
      }
    }
  },
  directives: {
    // 自定义指令：只允许输入数字
    numberOnly: {
      bind(el) {
        const input = el.querySelector('input')
        input.addEventListener('input', (e) => {
          let value = e.target.value
          value = value.replace(/[^\d]/g, '') // 只保留数字
          e.target.value = value
          input.dispatchEvent(new Event('input')) // 触发 v-model 更新
        })
      }
    }
  }
}
</script>

<style scoped>
.rating-form {
  /* padding: 30px; */
}

.range-group {
  margin-bottom: 20px;
}

.range-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.prefix-text {
  color: #606266;
  font-size: 14px;
  min-width: 50px;
}

.range-input {
  width: 160px;
}

.range-input :deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
  padding: 0 8px;
}

.range-input :deep(.el-input-group__appendScore) {
  padding: 0 8px;
  background-color: #F5F7FA;
  color: #909399;
  border-color: #DCDFE6;
}

.separator {
  color: #606266;
  margin: 0 4px;
}

.name-text {
  color: #606266;
  font-size: 14px;
  min-width: 100px;
}

.delete-btn {
  padding: 8px;
  color: #F56C6C;
  margin-left: 8px;
}

.delete-btn:hover {
  color: #f78989;
}

.add-button {
  margin-top: 10px;
  padding: 8px 16px;
  font-size: 12px;
  border-radius: 4px;
  background-color: #fff;
}

.add-button:hover {
  background-color: #ecf5ff;
}

:deep(.el-dialog__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #EBEEF5;
}

/* 移除输入框的上下箭头 */
:deep(.el-input__inner) {
  -webkit-appearance: none;
  -moz-appearance: textfield;
}
</style>
