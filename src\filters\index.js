import Vue from 'vue'

// 时间格式   yyyy-mm
Vue.filter("dateYearMonth", function (originVal) {
    if (!originVal) return '';
    const dt = new Date(originVal);
    const y = dt.getFullYear();
    const m = (dt.getMonth() + 1 + "").padStart(2, "0");
    return `${y}-${m}`;
});

// 时间格式  yyyy-mm-dd hh:mm:ss
Vue.filter("dateTimeFormat", function (originVal) {
    if (!originVal) return '';
    const dt = new Date(originVal);

    const y = dt.getFullYear();
    const m = (dt.getMonth() + 1 + "").padStart(2, "0");
    const d = (dt.getDate() + "").padStart(2, "0");

    const hh = (dt.getHours() + "").padStart(2, "0");
    const mm = (dt.getMinutes() + "").padStart(2, "0");
    const ss = (dt.getSeconds() + "").padStart(2, "0");
    return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
});


// 时间格式   yyyy-mm-dd
Vue.filter("dateFormat", function (originVal) {
    if (!originVal) return '';
    const dt = new Date(originVal);
    const y = dt.getFullYear();
    const m = (dt.getMonth() + 1 + "").padStart(2, "0");
    const d = (dt.getDate() + "").padStart(2, "0");
    return `${y}-${m}-${d}`;
});