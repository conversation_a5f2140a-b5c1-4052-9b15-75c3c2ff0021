<template>
    <el-dialog
    title="版本管理"
    :visible.sync="dialogVisible"
    width="70%"
    >
        <div class="dialog_con">
            <div class="table_box" v-loading="loading">
                <el-table
                :data="tableData"
                row-key="id"
                style="width: 100%">
                    <el-table-column
                        type="index"
                        label="序号"
                        width="50"
                        align="center">
                    </el-table-column>


                    <el-table-column
                        v-for="(item,index) in tableColumn"
                        :key="index"
                        :prop="item.prop"
                        :label="item.label"
                        align="center"
                        :width="item.width">
                        <template slot-scope="{row}">
                            <el-tag v-if="item.tabStatus" :type="row.suspended ? 'danger':'success'">{{row[item.prop]}}</el-tag>
                            <el-link v-else-if="item.linkStatus" type="primary"  @click="handleProcessView(row)">{{row[item.prop]}}</el-link>
                            <el-tag v-else-if="item.versionStatus" type="default">v{{ row[item.prop] }}</el-tag>
                            <div v-else-if="item.checkTime">
                                <i class="el-icon-time"></i>
                                {{ row[item.prop] | dateTimeFormat }}
                            </div>
                            <div v-else>{{row[item.prop]}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        fixed="right"
                        label="操作"
                        align="center"
                        width="150">
                        <template slot-scope="{row}">
                            <div class="handle_btn">
                                <el-popconfirm
                                v-if="!row.suspended"
                                title="是否确定挂起？"
                                @confirm="handleStatus(row,'suspended')"
                                >
                                    <el-button slot="reference" type="text" size="small" icon="el-icon-video-pause">挂起</el-button>
                                </el-popconfirm>

                                <el-popconfirm
                                v-if="row.suspended"
                                title="是否确定激活？"
                                @confirm="handleStatus(row,'active')"
                                >
                                    <el-button slot="reference" type="text" size="small" icon="el-icon-video-play">激活</el-button>
                                </el-popconfirm>

                                <el-popconfirm
                                title="是否确定删除？"
                                @confirm="handleDel(row)"
                                >
                                    <el-button slot="reference" class="del" type="text" size="small" icon="el-icon-delete">删除</el-button>
                                </el-popconfirm>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="page_box">
                    <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentPage"
                    @prev-click="handlePage"
                    @next-click="handlePage"
                    :current-page="page.pageNum"
                    :pager-count="5"
                    :page-sizes="[10, 20,30,40]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total">
                    </el-pagination>
                </div>
            </div>
        </div>
        <flow-chart ref="flowChart"></flow-chart>
    </el-dialog>

</template>
<script>
import {publishList,changeStatus,delModel } from '@/api/processMan/deployMan'
import flowChart from './flowChart.vue'
export default {
    components:{flowChart},
    data(){
        return {
            dialogVisible:false,
            loading:false,
            rowData:null,
            tableData:[],
            page: {
                pageSize: 10,
                pageNum: 1,
                total: 0,
            },
            tableColumn:[
                {
                    prop:'processKey',
                    label:'流程标识',
                    width:'200'
                },
                {
                    prop:'processName',
                    label:'流程名称',
                    linkStatus:true,
                    width:'300'
                },
                {
                    prop:'version',
                    label:'流程版本',
                    versionStatus:true,
                    width:''
                },
                {
                    prop:'suspendedVal',
                    label:'状态',
                    tabStatus:true,
                    width:'100'
                },
            ]
        }
    },
    methods:{
        init(row){
            this.dialogVisible = true
            this.rowData = row
            this.loadData()
        },

        // 流程图
        handleProcessView(row){
            this.$refs.flowChart.init(row)
        },

        // 挂起/激活
        handleStatus(row,state){
            this.loading = true
            let params = {
                definitionId:row.definitionId,
                state
            }
            changeStatus(params).then((res)=>{
                this.$message({
                    type:'success',
                    message:'操作成功',
                    duration:1500
                })
                this.loadData()
            }).catch(()=>{
                this.loading = false
            })
        },
        // 删除
        handleDel(row){
            this.loading = true
            delModel(row.deploymentId).then((res)=>{
                this.$message({
                    type:'success',
                    message:'操作成功',
                    duration:1500
                })
                this.loadData()
            }).catch(()=>{
                this.loading = false
            })
        },

        loadData(){
            this.loading = true
            let params = {
                ...this.page,
                processKey:this.rowData.processKey
            }
            publishList(params).then((res)=>{
                let resData = res.data
                resData.records.forEach((item)=>{
                    item.suspendedVal = item.suspended ? '挂起' : '激活'
                })
                this.tableData = resData.records
                this.page.total = resData.total
                this.loading = false
            }).catch(()=>{
                this.loading = false
            })
        },
        // 更改每页显示条数
        handleSizeChange(pageSize){
            this.page.pageSize = pageSize
            this.loadData()
        },

        // 选择页数
        handleCurrentPage(currentPage){
            this.page.pageNum = currentPage
            this.loadData()
        },

        // 点击上一页/下一页
        handlePage(currentPage){
            this.page.pageNum = currentPage
            this.loadData()
        }
    }
}
</script>
