<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" top="30px" width="700px" style="padding-bottom: 20px;"
    :before-close="handleClose">
    <div v-loading="loading" class="info-section">
      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">提交时间：</span>
            <span class="info-content">{{ detail.crtTime || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">招标编号：</span>
            <span class="info-content">{{ detail.bidNo || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">招标项目名称：</span>
            <span class="info-content">{{ detail.bidName || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">分类编号：</span>
            <span class="info-content">{{ detail.typeNo || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">供方编码：</span>
            <span class="info-content">{{ detail.supplierCode || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">供方编号：</span>
            <span class="info-content">{{ detail.supplierNo || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">供方名称：</span>
            <span class="info-content">{{ detail.supplierName || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">投标报价：</span>
            <span class="info-content">{{ detail.bidPrice || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">价格排名：</span>
            <span class="info-content">{{ detail.ranking || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">是否中标：</span>
            <span class="info-content">{{ detail.bidWin ? '是' : '否' || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <!-- <el-col :span="12">
          <div class="info-item">
            <span class="info-label">不参加/相应：</span>
            <span class="info-content">{{ detail.bidScore || '-' }}</span>
          </div>
        </el-col> -->
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">是否邀标：</span>
            <span class="info-content">{{ detail.bidInvite ? '是' : '否' || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">检查员：</span>
            <span class="info-content">{{ detail.inspector || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <!-- <el-col :span="12">
          <div class="info-item">
            <span class="info-label">参与招标得分：</span>
            <span class="info-content">{{ detail.participateScore }}</span>
          </div>
        </el-col> -->
      </el-row>
      <el-row>
        <!-- <el-col :span="12">
          <div class="info-item">
            <span class="info-label">标书响应：</span>
            <span class="info-content">{{ detail.bidScore }}</span>
          </div>
        </el-col> -->
      </el-row>
    </div>

    <template v-if="type === 'edit' || type === 'delete'">
      <div class="form-section">
        <div class="form-item">
          <span class="required-label">{{ { edit: '改分', confirm: '确认', batchConfirm: '批量确认', delete: '删除' }[type]
            }}理由：</span>
          <el-input type="textarea" v-model="form.updateReason" :rows="4" placeholder="请输入理由" />
        </div>

        <div class="form-item">
          <span class="required-label">证明材料：</span>
          <el-upload class="upload-demo" :action="uploadFileUrl" :before-upload="beforeUpload" :headers="headers"
            :on-success="handleUploadSuccess" :on-error="handleUploadError" :file-list="fileList">
            <el-button size="small" type="primary">添加文件</el-button>
          </el-upload>
        </div>

        <div class="form-item score-section" v-if="type != 'delete'">
          <span>不参标/不响应扣分：{{ detail.bidScore1 }}分</span>
          <span class="score-divider">变更为：</span>
          <el-input v-model="form.bidScore1" class="score-input" @input="handleScoreInput1" />
          <span class="score-range">(0-100)</span>
        </div>
        <div class="form-item score-section" v-if="type != 'delete'">
          <span>参与招标扣分：{{ detail.bidScore2 }}分</span>
          <span class="score-divider">变更为：</span>
          <el-input v-model="form.bidScore2" class="score-input" @input="handleScoreInput2" />
          <span class="score-range">(0-100)</span>
        </div>
        <div class="form-item score-section" v-if="type != 'delete'">
          <span>标书响应扣分：{{ detail.bidScore3 }}分</span>
          <span class="score-divider">变更为：</span>
          <el-input v-model="form.bidScore3" class="score-input" @input="handleScoreInput3" />
          <span class="score-range">(0-100)</span>
        </div>
      </div>
    </template>



    <template v-if="type === 'view'">
      <div class="form-section">
        <div>不参标/不响应扣分：{{ detail.bidScore1 }}分</div>
        <div>参与招标扣分：{{ detail.bidScore2 }}分</div>
        <div>标书响应扣分：{{ detail.bidScore3 }}分</div>
      </div>
    </template>

    <!-- <div class="tip-text">
      注：{{ getTipText }}
    </div> -->

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ type === 'view' ? '关闭' : '取消' }}</el-button>
      <el-button v-if="type !== 'view'" :type="type === 'delete' ? 'danger' : 'primary'" @click="handleSubmit"
        :loading="submitting">
        {{ type === 'delete' ? '删除' : '确定' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { QualitySecondaryCategory, getQualityTipText } from '@/enums/qualityIndicator'
import { getToken } from '@/utils/auth'
import { apiUpdate, apiDelete } from '@/api/dimensionData/loudScaleIndicator'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      default: 'view',
      validator: function (value) {
        return ['view', 'edit', 'delete'].indexOf(value) !== -1
      }
    }
  },
  data() {
    return {
      loading: false,
      submitting: false,
      detail: {},
      form: {
        updateReason: '',
        score: '',
        fileIds: []
      },
      fileList: [],
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/user-server/file/uploadFile", // 上传的图片服务器地址
      headers: {
        "X-Token": "Bearer " + getToken(),
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      const titles = {
        view: '查看',
        edit: '修改罚分',
        delete: '删除'
      }
      return titles[this.type]
    },
    getTipText() {
      return getQualityTipText(this.detail.secondType)
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getDetail()
      }
    }
  },
  methods: {
    async getDetail() {
      try {
        this.loading = true
        this.detail = this.row

        this.form.bidScore1 = this.row.bidScore1
        this.form.bidScore2 = this.row.bidScore2
        this.form.bidScore3 = this.row.bidScore3
      } catch (error) {
        this.$message.error('获取详情失败')
      } finally {
        this.loading = false
      }
    },
    handleScoreInput3(value) {
      value = value.replace(/[^\d]/g, '')
      let num = parseInt(value, 10)

      if (value === '') {
        this.form.bidScore3 = ''
        return
      }

      if (isNaN(num)) {
        num = 0
      } else if (num > 100) {
        num = 100
      }

      this.form.score = num.toString()
    },
    handleScoreInput2(value) {
      value = value.replace(/[^\d]/g, '')
      let num = parseInt(value, 10)

      if (value === '') {
        this.form.bidScore2 = ''
        return
      }

      if (isNaN(num)) {
        num = 0
      } else if (num > 100) {
        num = 100
      }

      this.form.score = num.toString()
    },
    handleScoreInput1(value) {
      value = value.replace(/[^\d]/g, '')
      let num = parseInt(value, 10)

      if (value === '') {
        this.form.bidScore1 = ''
        return
      }

      if (isNaN(num)) {
        num = 0
      } else if (num > 100) {
        num = 100
      }

      this.form.score = num.toString()
    },
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
        return false
      }
      return true
    },
    handleUploadSuccess(response) {
      this.form.fileIds.push(response.data.id)
      this.$message.success('上传成功')
    },
    handleUploadError() {
      this.$message.error('上传失败')
    },
    handleClose() {
      this.form = {
        updateReason: '',
        score: '',
        fileIds: []
      }
      this.fileList = []
      this.dialogVisible = false
    },
    async handleSubmit() {
      if (!this.form.updateReason) {
        this.$message.error(`请输入${this.type === 'delete' ? '删除' : '改分'}理由`)
        return
      }
      if (this.form.fileIds.length === 0) {
        this.$message.error('请上传证明材料')
        return
      }
      if (this.type === 'edit' && !this.form.score) {
        this.$message.error('请输入变更分数')
        return
      }

      try {
        this.submitting = true
        if (this.type === 'delete') {
          await apiDelete({
            id: this.row.id,
            ...this.form
          })
        } else {
          await apiUpdate({
            id: this.row.id,
            ...this.form
          })
        }
        this.$message.success(this.type === 'delete' ? '删除成功' : '修改成功')
        this.$emit('refresh')
        this.handleClose()
      } catch (error) {
        this.$message.error(this.type === 'delete' ? '删除失败' : '修改失败')
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.info-section {
  padding: 10px 20px 0;
}

.el-row {
  margin-bottom: 0 !important;
}

.info-item {
  display: flex;
  line-height: 30px;
}

.info-label {
  width: 160px;
  text-align: right;
  padding-right: 8px;
  color: #606266;
}

.info-label.nowrap {
  white-space: nowrap;
  width: auto;
}

.info-content {
  flex: 1;
  color: #303133;
}

.form-section {
  padding: 0 20px;
  margin-top: 15px;
}

.form-item {
  margin-bottom: 15px;
}

.required-label::before {
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}

.score-section {
  display: flex;
  align-items: center;
}

.score-divider {
  margin: 0 10px;
}

.score-input {
  width: 80px;
}

.score-range {
  margin-left: 10px;
  color: #909399;
}

.tip-text {
  color: #409EFF;
  font-size: 12px;
  margin: 15px 0;
  line-height: 1.4;
}

/* 覆盖 element-ui 的默认样式 */
:deep(.el-row) {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

:deep(.el-col) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

:deep(.score-input .el-input__inner) {
  padding-right: 8px !important;
}

:deep(.score-input .el-input__suffix) {
  display: none;
}
</style>
