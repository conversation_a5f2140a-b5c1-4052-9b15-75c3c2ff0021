import request from '@/utils/request'
// 分页
export function getSmUserPage(param) {
    return request({
        url: '/mes-server/sm/user/page',
        method: 'get',
        params: param,
    })
}

// 新增
export function addSmUser(param) {
    return request({
        url: '/mes-server/sm/user',
        method: 'post',
        data: param,
    })
}

// 修改
export function modifySmUser(param) {
    return request({
        url: '/mes-server/sm/user',
        method: 'put',
        data: param,
    })
}

// 删除
export function delSmUser(id) {
    return request({
        url: `/mes-server/sm/user/${id}`,
        method: 'delete',
    })
}

// 查询全部htc-qs人员
export function getHtcUserList(param) {
    return request({
        url: '/mes-server/sm/user/list',
        method: 'get',
        params: param,
    })
}