<template>
    <el-dialog
    title="详情"
    :visible.sync="dialogVisible"
    width="40%"
    >

    <div class="dialog_con_info" v-loading="loading">
        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">上级组织：</span>{{form.parentName}}
            </el-col>
            <el-col :span="12">
                <span class="txt">编码：</span>{{form.no}}

            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">名称：</span>{{form.name}}
            </el-col>
            <el-col :span="12">
                <span class="txt">负责人：</span>{{form.principalName}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">创建人：</span>{{form.creator}}
            </el-col>
            <el-col :span="12">
                <span class="txt">状态：</span>{{form.status | checkDic('origanizationStatus')}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">排序：</span>{{form.indexSort}}
            </el-col>

            <el-col :span="12">
                <span class="txt">创建时间：</span>{{form.createTime | dateTimeFormat}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">更新时间：</span>{{form.updateTime | dateTimeFormat}}
            </el-col>
        </el-row>
        <el-row type="flex">
            <el-col :span="24">
                <span class="txt">备注：</span>{{form.remark}}
            </el-col>
        </el-row>
    </div>

    </el-dialog>
</template>
<script>
export default {
    data(){
        return {
            isAdd:false,
            isInfo:null,
            dialogVisible:false,
            loading:false,
            userOptions:[], // 直属领导
            treeOptions:[],   // 所属机构
            form:{
                parentName:'',
                no:'',
                name:'',
                principalName:'',
                creator:'',
                status:'',
                indexSort:'',
                createTime:'',
                updateTime:'',
                remark:''
            }
        }
    },
    methods:{
        init(row){
            this.dialogVisible = true
            this.form = row
        },
    }
}
</script>
<style lang="less" scoped>
.dialog_con {
    height:30vh;
    line-height:30px;
}

</style>
