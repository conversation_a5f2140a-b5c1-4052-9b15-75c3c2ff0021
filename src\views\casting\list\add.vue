<template>
  <!-- 添加或修改岗位对话框 -->
  <el-dialog title="新增" class="loading-box" :visible.sync="addVisible" width="600px" append-to-body>
    <el-form ref="form" :model="form" label-width="60px">
      <el-row>
        <el-col :span="12">
          <div class="time-box">
            <span>规定完成时间:{{ form.planTime }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item class="record" label="备注" prop="remarks">
            <el-input v-model="form.record" type="textarea" :rows="2" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="图片" prop="postName">
            <el-upload :action="uploadUrl" :headers="headers" :on-remove="handleRemove" :on-success="handleFileSuccess"
              :on-preview="handlePictureCardPreview" :file-list="form.imageList" list-type="picture-card"
              accept=".jpeg,.png,.jpg,.bmp,.gif" :limit="9" multiple>
              <i slot="default" class="el-icon-plus icon_pic"></i>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible" append-to-body>
              <img width="100%" :src="dialogImageUrl" alt="" />
            </el-dialog>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { addNodeRecord } from "@/api/castingForging/contract";
import { getToken } from "@/utils/auth";
export default {
  name: "Add",
  props: {
    addVisible: {
      type: Boolean,
      default: false,
    },
    row: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    addVisible(newVal) {
      if (newVal) {
        this.form = { ...this.row };
      }
    },
  },
  data() {
    return {
      form: {},
      title: "新增",
      uploadUrl: process.env.VUE_APP_BASE_API + "/user-server/file/upload",
      headers: { "X-Token": "Bearer " + getToken() },
      dialogImageUrl: "",
      dialogVisible: false,
    };
  },
  methods: {
    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 取消按钮
    cancel() {
      this.addVisible = false;
    },
    //图片上传
    handleFileSuccess(response, file, fileList) {
      this.form.imageList = fileList;
    },
    //删除图片
    handleRemove(file, fileList) {
      this.form.imageList = fileList;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          if (this.form.castingId != undefined) {
            let list = this.form.imageList.map((item) => {
              return {
                ...item.response.data,
              };
            });
            addNodeRecord({ ...this.form, imageList: list }).then(
              (response) => {
                this.$modal.msgSuccess("修改成功");
                this.addVisible = false;
                this.$emit("refresh", this.form.castingId);
              }
            );
          }
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.time-box {
  margin-left: 20px;
  margin-bottom: 16px;
}

::v-deep .el-dialog__body {
  line-height: 36px;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}

::v-deep .el-row--flex {
  margin-left: 22px;
}

.dialog-content {
  height: 60vh;
  display: flex;
  justify-content: space-between;

  .content {
    width: 30%;
  }

  .block {
    flex: 1;
    padding: 8px;
    overflow-y: scroll;
  }
}

.timeline-item-top {
  display: flex;
  justify-content: space-between;
  background-color: #e3f0ff;
  padding: 4px 12px;
  border-radius: 4px;

  .top-right {
    font-size: 12px;
    color: #666;
    text-align: right;
  }

  .top-title {
    font-size: 16px;
    color: #1890ff;
    font-weight: 700;
  }

  .top-content {
    font-size: 12px;
    color: #666;

    >p {
      padding: 0;
      margin: 0;
      line-height: 18px;
    }

    // :nth-child(2) {
    // margin-left: 24px;
    // }
  }
}

.timeline-item-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  background-color: #f6f7f9;
  padding: 4px 12px;
  border-radius: 4px;

  .card-left {
    display: flex;
    flex-direction: column;

    .card-address {
      font-size: 12px;
      color: #85929b;
    }

    .img-box {
      width: 100%;
      display: flex;

      img {
        width: 100px;
        height: 100px;
        margin-right: 12px;
      }
    }
  }
}

.loading-box {

  ::v-deep .el-upload-list__item,
  ::v-deep .el-upload--picture-card {
    width: 80px;
    height: 80px;
    position: relative;
  }

  ::v-deep .icon_pic {
    position: absolute;
    top: 25px;
    left: 25px;
  }
}
</style>
