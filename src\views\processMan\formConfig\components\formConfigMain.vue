<template>
    <div class="form_config">
        <div class="search_form">
            <el-row type="flex">
                <el-col :span="4">
                    <el-input
                    v-model="searchForm.formName"
                    placeholder="请输入表单名称"
                    clearable
                    ></el-input>
                </el-col>

                <el-col :span="6">
                    <div class="btn_box">
                    <el-button
                        @click="search()"
                        class="btn search_btn"
                        icon="el-icon-search"
                        type="primary"
                        >搜索</el-button
                    >
                    <el-button
                        @click="search(true)"
                        class="btn reset_btn"
                        icon="el-icon-refresh"
                        >重置</el-button
                    >
                    </div>
                </el-col>
            </el-row>
        </div>
        <div class="operation_btn">
            <el-button
            class="btn add_btn"
            icon="el-icon-plus"
            type="primary"
            @click="handleAdd()"
            >新增</el-button
            >
        </div>

      <div class="table_box" v-loading="loading">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column type="index" label="序号" width="80" align="center">
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableColumn"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            align="center"
            :width="item.width"
          >
            <template slot-scope="{ row }">
              <div v-if="item.imgStatus" class="avatar_box">
                <img v-if="row[item.prop]" class="img" :src="row[item.prop]" />
              </div>
              <div v-else-if="item.checkTime">
                <i class="el-icon-time"></i>
                {{ row[item.prop] | dateTimeFormat }}
              </div>
              <el-tag
                v-else-if="item.tabStatus"
                :type="row.status === 0 ? 'danger' : 'default'"
                >{{ row[item.prop] }}</el-tag
              >
              <div v-else>{{ row[item.prop] }}</div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="200">
            <template slot-scope="{ row }">
              <div class="handle_btn">
                <el-button
                  type="text"
                  size="small"
                  icon="el-icon-edit"
                  @click="handleInfo(row)"
                  >详情</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  icon="el-icon-edit"
                  @click="handleAdd(row)"
                  >修改</el-button
                >
                <el-popconfirm title="是否确定删除？" @confirm="handleDelete(row)">
                  <el-button
                    slot="reference"
                    class="del"
                    type="text"
                    size="small"
                    icon="el-icon-delete"
                    >删除</el-button
                  >
                </el-popconfirm>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="page_box">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentPage"
            :pager-count="5"
            :current-page="page.pageNum"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="10"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
          >
          </el-pagination>
        </div>
      </div>



      <!-- 添加或修改流程表单对话框 -->


      <!--表单配置详情-->
      <form-config-info ref="formConfigInfo"></form-config-info>
    </div>
  </template>

  <script>
import {formConfigList,delForm} from '@/api/processMan/formConfig'
import formConfigInfo from './formConfigInfo.vue'
export default {
  name: "Form",
  components: {
    formConfigInfo
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      searchForm:{
        formName:''
      },
      tableData:[],
      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      tableColumn:[
        {
          prop:'formId',
          label:'表单主键',
          width:'200'
        },
        {
          prop:'formName',
          label:'表单名称',
          width:'200'
        },
        {
          prop:'remark',
          label:'备注',
          width:''
        },
      ],

      // 弹出层标题
      title: "",
      formConf: {}, // 默认表单数据
      formConfOpen: false,
      formTitle: "",
      // 是否显示弹出层
      open: false,
      // 查询参数

      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.loadData();
  },
  activated() {
    this.loadData();
  },
  methods: {
    // 查询、重置
    search(reset) {
      if (reset) {
        this.searchForm = {};
      }
      this.page.pageNum = 1;
      this.loadData();
    },
    /** 查询流程表单列表 */
    loadData() {
      this.loading = true;
      let params = {
        ...this.searchForm,
        ...this.page
      }
      formConfigList(params).then(res => {
        let resData = res.data
        this.tableData = resData.records;
        this.page.total = resData.total;
        this.loading = false
      }).catch(()=>{
        this.loading = false
      });
    },
    // 详情
    handleInfo(row){
      this.$refs.formConfigInfo.init(row)
    },

    // 新增
    handleAdd(row) {
      this.$parent.showFormConfig = false
      this.$nextTick(()=>{
        this.$parent.$refs.tool.init(row)
      })
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.loading = true
      delForm(row.formId).then((res)=>{
        this.$message({
          type:'success',
          message:'操作成功',
          duration:1500
        })
        this.loadData()
      }).catch(()=>{
        this.loading = false
      })
    },


    // 更改每页显示条数
    handleSizeChange(pageSize){
        this.page.pageSize = pageSize
        this.loadData()
    },

    // 选择页数/点击上一页/下一页
    handleCurrentPage(currentPage){
        this.page.pageNum = currentPage
        this.loadData()
    }
  }
};
</script>

<style lang="less" scoped>
.test-form {
  margin: 15px auto;
  width: 800px;
  padding: 15px;
}
.form_config {
  padding: 16px 12px 0;
}
</style>
