import request from "@/utils/request";
// 分页查询
export function apiGetPage(data) {
  return request({
    url: "/sd-server/tech/data",
    method: "get",
    params: data,
  });
}
//获取二级分类
export function apiGetSecondCategory(data) {
  return request({
    url: "/sd-server/seven/dimension/type",
    method: "get",
    params: data,
  });
}

// 新增
export function apiAdd(data) {
  return request({
    url: "/sd-server/tech/data",
    method: "post",
    data,
  });
}
// 修改
export function apiUpdate(data) {
  return request({
    url: "/sd-server/tech/data",
    method: "put",
    data,
  });
}
// 删除
export function apiDelete(data) {
  return request({
    url: "/sd-server/tech/data",
    method: "delete",
    params: data,
  });
}
export function apiGetOne(data) {
  return request({
    url: "/sd-server/tech/data/one",
    method: "get",
    params: data,
  });
}
// 查询全部的分类编号
export function apiGetAllTypeNo() {
  return request({
    url: "/sd-server/type/all",
    method: "get",
  });
}
// 获取历史记录
export function apiGetHistory(data) {
  return request({
    url: `/sd-server/operation/record/${data.id}`,
    method: "get",
  });
}
// 导出
export function exportData(data) {
  return request({
    url: "/sd-server/quality/export",
    method: "get",
    params: data,
    responseType: "blob",
  });
}
