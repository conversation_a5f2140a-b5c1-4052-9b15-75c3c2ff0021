<template>
  <div class="app-container">
    <el-card class="qa-card">
      <!-- <div slot="header" class="clearfix">
        <span class="card-title">智能问答助手</span>
      </div> -->
      {{ chatHistory3 }}
      <!-- 聊天记录区域 -->
      <div class="chat-container" ref="chatContainer">
        <div v-if="chatHistory.length === 0" class="empty-chat">
          <el-empty description="暂无对话，开始提问吧！">
            <!-- <el-button type="primary" @click="focusInput">开始提问</el-button> -->
          </el-empty>
        </div>
        <div v-else>
          <div
            v-for="(item, index) in chatHistory"
            :key="index"
            class="message-item"
            :class="{
              'user-message': item.type === 'user',
              'ai-message': item.type === 'ai',
            }"
          >
            <!-- <div class="message-avatar">
              <i v-if="item.type === 'user'" class="el-icon-user-solid"></i>
              <i v-else class="el-icon-s-opportunity"></i>
            </div> -->
            <div class="message-content">
              <!-- <div class="message-sender">
                {{ item.type === "user" ? "我" : "AI助手" }}
              </div> -->
              <!-- 使用组件方式展示消息内容 -->
              <div class="message-text">
                <!-- 用户消息直接显示文本 -->
                <template v-if="item.type === 'user'">
                  {{ item.content }}
                </template>
                <!-- AI消息显示思考过程和答案 -->
                <template v-else>
                  <!-- 思考过程部分 -->
                  <div
                    v-if="item.reasoningPart"
                    class="ai-thinking-container"
                    :class="{ 'thinking-collapsed': item.isThinkingCollapsed }"
                  >
                    <div
                      class="ai-thinking-header"
                      @click="toggleThinking(item)"
                      :title="item.isThinkingCollapsed ? '点击展开思考过程' : '点击收起思考过程'"
                    >
                      <div class="thinking-icon">
                        <i class="el-icon-s-opportunity"></i>
                      </div>
                      <div class="thinking-time">
                        已深度思考
                        <span v-if="item.thinkingTime"
                          >（用时 {{ item.thinkingTime }} 秒）</span
                        >
                      </div>
                      <i class="el-icon-arrow-down thinking-toggle-icon"></i>
                    </div>
                    <div class="ai-thinking-content" @click="skipReasoningTypeEffect(item)" :title="item.isReasoningTyping ? '点击跳过思考过程的打字效果' : ''">
                      <div v-if="item.isReasoningTyping">
                        <span>{{ item.displayReasoningText }}</span>
                        <span class="typing-cursor reasoning-cursor">|</span>
                      </div>
                      <div v-else>
                        <span>{{ item.reasoningPart }}</span>
                      </div>
                    </div>
                  </div>
                  <!-- 答案部分 -->
                  <div class="ai-answer" @click="skipTypeEffect(item)" :title="item.isTyping ? '点击跳过打字效果' : ''">
                    <div v-if="item.isTyping">
                      <span>{{ item.displayText }}</span>
                      <span class="typing-cursor">|</span>
                    </div>
                    <div v-else>
                      <span>{{ item.answerPart || item.content }}</span>
                    </div>
                  </div>
                </template>
              </div>
              <div class="message-time">{{ item.time }}</div>
            </div>
          </div>
        </div>
        <!-- 加载中状态 -->
        <div v-if="loading && !isGenerating" class="loading-message">
          <div class="message-avatar">
            <i class="el-icon-s-opportunity"></i>
          </div>
          <div class="message-content">
            <!-- <div class="message-sender">AI助手</div> -->
            <div class="message-text">
              <el-skeleton :rows="3" animated />
            </div>
          </div>
        </div>

        <!-- 停止回答按钮（单独显示） -->
        <div v-if="isGenerating" class="stop-generating-container">
          <el-button size="small" type="danger" @click="stopGenerating">
            <i class="el-icon-close"></i> 停止回答
          </el-button>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-container">
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入您的问题..."
          v-model="userInput"
          @keyup.ctrl.enter.native="sendMessage"
          ref="inputArea"
        >
        </el-input>
        <div class="button-container">
          <!-- <el-tooltip content="清空对话" placement="top">
            <el-button
              type="info"
              icon="el-icon-delete"
              circle
              @click="clearChat"
              :disabled="chatHistory.length === 0"
            ></el-button>
          </el-tooltip> -->
          <el-tooltip content="按Ctrl+Enter发送" placement="top">
            <el-button
              type="primary"
              @click="sendMessage"
              :disabled="userInput.trim() === '' || loading"
            >
              发送
              <i class="el-icon-s-promotion el-icon--right"></i>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  createQuestion,
  askQuestion,
  stopAskQuestion,
  generateToken,
  getStoredToken,
} from "@/api/aiChat";

export default {
  name: "ExperienceQA",
  data() {
    return {
      chatHistory3: "",
      userInput: "",
      chatHistory: [],
      loading: false,
      // 是否正在生成回答
      isGenerating: false,
      // AbortController实例，用于取消fetch请求
      abortController: null,
      // 常见问题列表
      commonQuestions: [],
      // 会话ID，用于连续对话
      mappingId: "",
      // 当前问题的映射ID，用于停止生成
      currentMappingId: "",
      // 打字效果相关
      typingSpeed: 20, // 每个字的打字速度（毫秒）
      // 思考开始时间
      thinkingStartTime: null,
    };
  },
  mounted() {
    // 从本地存储加载聊天历史
    // const savedHistory = localStorage.getItem("aiChatHistory");
    // if (savedHistory) {
    //   try {
    //     this.chatHistory = JSON.parse(savedHistory);
    //   } catch (e) {
    //     console.error("解析聊天历史失败", e);
    //   }
    // }

    // 确保Token存在
    if (!getStoredToken()) {
      // 如果没有Token，获取新Token
      this.getToken();
    }

    this.scrollToBottom();
  },

  // 定时检查Token是否过期
  created() {
    this.getToken();
  },

  beforeDestroy() {
    // 清除定时器
    if (this.tokenRefreshTimer) {
      clearInterval(this.tokenRefreshTimer);
    }
  },
  methods: {
    // 获取Token
    async getToken() {
      try {
        const res = await generateToken();
        return res.code === "0000";
      } catch (error) {
        console.error("获取Token失败：", error);
        return false;
      }
    },

    // 发送消息
    async sendMessage() {
      if (!this.userInput.trim()) return;

      // 添加用户消息到聊天历史
      this.chatHistory.push({
        type: "user",
        content: this.userInput,
        time: this.formatTime(new Date()),
      });

      // 记录思考开始时间
      this.thinkingStartTime = Date.now();

      // 创建一个初始的AI回复，确保在开始时就有一个空的AI回答
      this.chatHistory.push({
        type: "ai",
        content: "", // 初始内容显示"正在思考中..."
        time: this.formatTime(new Date()),
      });

      const question = this.userInput;
      this.userInput = "";

      // 设置加载状态
      this.loading = true;
      this.scrollToBottom();
      this.saveHistory();

      try {
        // 确保Token存在
        if (!getStoredToken()) {
          const tokenRes = await this.getToken();
          if (!tokenRes) {
            throw new Error("获取Token失败");
          }
        }

        // 每次提问都创建新会话
        const createRes = await this.createNewSession(question);
        if (!createRes) {
          throw new Error("创建会话失败");
        }

        // 发送问题并获取回答
        await this.getAIResponse(question);
      } catch (error) {
        console.error("AI回答出错：", error);
        // 更新最后一条AI回答为错误信息
        if (
          this.chatHistory.length > 0 &&
          this.chatHistory[this.chatHistory.length - 1].type === "ai"
        ) {
          this.chatHistory[this.chatHistory.length - 1].content =
            "抱歉，系统暂时无法回答您的问题，请稍后再试。";
        } else {
          this.receiveAIResponse(
            "抱歉，系统暂时无法回答您的问题，请稍后再试。"
          );
        }
      }
    },

    // 创建新会话
    async createNewSession(question) {
      try {
        const res = await createQuestion({
          mode: "open", //提问模式：非必传（open、weaver）
          question, //问题
          channel: "writer", //入口:AI智能体（固定值）
          functionName: "writingAid", //功能名称：AI智能体辅助（固定值）
          curWriterServiceInfo: {
            //当前AI智能体服务信息；通过智能体列表接口，获取能够使用的智能体
            serviceId: "1921723646622679042", //AI智能体服务id
            serviceCode: "CW00005", //AI智能体服务编号
            serviceMark: "baaaac-CW00005", //AI智能体服务标识
            serviceName: "经验反馈SCM集成测试", //AI智能体服务名称
            serviceType: 1, //自定义AI智能体标识，必填
          },
          assist: {
            documentList: [
              //临时关联文件，每次新的提问都需要携带这个参数，上一次的参数，24小时之内可以继续使用
              // {
              //   fileName: "示例文件1.docx", //必填
              //   fileId: "1831210577077645314", //必填 使用的是临时文件的processId，不是FileId
              //   fileSize: "", // 非必填
              //   fileLength: 200780, //单位是字节，非必填
              //   uploadTime: "2024-09-06 13:59:58", //必填
              // },
            ],
          },
        });

        if (res.data.code == "0000" && res.data) {
          this.mappingId = res.data.data.mappingId || "";

          return true;
        }
        return false;
      } catch (error) {
        console.error("创建会话失败：", error);
        return false;
      }
    },

    // 获取AI回答
    async getAIResponse(question) {
      try {
        // 初始化答案内容
        let fullAnswer = "";

        // 设置正在生成回答的状态
        this.isGenerating = true;

        // 确保已记录思考开始时间
        if (!this.thinkingStartTime) {
          this.thinkingStartTime = Date.now();
        }

        // 调用askQuestion并传入回调函数
        this.abortController = askQuestion(
          {
            mappingId: this.mappingId,
            question: question,
          },
          // 消息处理回调
          (eventData) => {
            // 如果有data字段且包含回答
            if (eventData && eventData.data) {
              const data = eventData.data;

              // 如果有answerId，则更新当前映射ID
              if (data.questionId) {
                this.currentMappingId = data.questionId;
              }
              console.log(data.answerList, "status:" + data.status);
              // 如果有回答内容，则更新答案
              if (data.answerList) {
                // 更新显示的答案
                this.updateAIResponse(data.answerList);
              } else {
                console.log("当前数据包中没有answerList字段，继续等待后续数据");
                // 即使没有answerList，也继续处理后续数据
                // 不需要创建新的AI回答，因为我们已经在开始时创建了一个
              }
              //   // 更新显示的答案
              //   this.updateAIResponse3(fullAnswer);
              // }
              // 如果是结束消息
              if (data.status == 1) {
                // 计算思考时间（秒）
                if (this.thinkingStartTime) {
                  const thinkingTimeMs = Date.now() - this.thinkingStartTime;
                  const thinkingTimeSec = Math.round(thinkingTimeMs / 1000);

                  // 更新最后一条AI消息的思考时间
                  if (
                    this.chatHistory.length > 0 &&
                    this.chatHistory[this.chatHistory.length - 1].type === "ai"
                  ) {
                    this.$set(
                      this.chatHistory[this.chatHistory.length - 1],
                      "thinkingTime",
                      thinkingTimeSec
                    );
                  }

                  // 重置思考开始时间
                  this.thinkingStartTime = null;
                }

                this.isGenerating = false;
                this.abortController = null;
              }
            }
          },
          // 错误处理回调
          (error) => {
            console.error("获取AI回答失败：", error);
            this.isGenerating = false;
            this.abortController = null;

            // // 如果还没有任何回答，则显示错误信息
            // if (!fullAnswer) {
            //   this.receiveAIResponse("抱歉，获取回答时出现错误，请稍后再试。");
            // }
          }
        );
      } catch (error) {
        console.error("初始化AI回答请求失败：", error);
        this.isGenerating = false;
        this.abortController = null;
        this.receiveAIResponse("抱歉，获取回答时出现错误，请稍后再试。");
      } finally {
        this.loading = false;
      }
    },

    // 停止生成回答
    async stopGenerating() {
      if (!this.isGenerating) return;

      try {
        // 如果有AbortController实例，先取消请求
        if (this.abortController) {
          this.abortController.abort();
          this.abortController = null;
        }

        // 如果有当前映射ID，则调用停止API
        if (this.currentMappingId) {
          await stopAskQuestion({
            questionId: this.currentMappingId,
          });
        }

        // 停止生成状态
        this.isGenerating = false;

        // 停止打字效果，直接显示完整内容
        if (this.chatHistory.length > 0) {
          const lastMessage = this.chatHistory[this.chatHistory.length - 1];
          if (lastMessage && lastMessage.type === 'ai') {
            // 停止所有的打字效果，确保完整显示内容
            this.stopAllTypingEffects(lastMessage);
          }
        }

        // 保存聊天历史
        this.saveHistory();
      } catch (error) {
        console.error("停止生成失败：", error);
      }
    },

    // 停止所有打字效果
    stopAllTypingEffects(message) {
      // 停止答案部分的打字效果
      if (message.isTyping) {
        this.skipTypeEffect(message);
      }

      // 停止思考部分的打字效果
      if (message.isReasoningTyping) {
        this.skipReasoningTypeEffect(message);
      }
    },

    // 接收AI回答
    receiveAIResponse(answer) {
      const aiMessage = {
        type: "ai",
        content: answer,
        time: this.formatTime(new Date()),
      };
      this.chatHistory.push(aiMessage);
      this.loading = false;
      this.saveHistory();
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 更新AI回答（用于流式响应）
    updateAIResponse(answerList) {
      // 处理answerList数组
      if (!answerList || !Array.isArray(answerList)) {
        console.warn("回答数据格式不正确", answerList);
        // 即使数据格式不正确，也确保创建一个初始的AI回答
        if (
          this.chatHistory.length === 0 ||
          this.chatHistory[this.chatHistory.length - 1].type !== "ai"
        ) {
          this.chatHistory.push({
            type: "ai",
            content: "", // 初始内容为空
            time: this.formatTime(new Date()),
            reasoningPart: "", // 思考部分
            answerPart: "", // 答案部分
          });
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
        return;
      }

      // 分别提取思考部分和答案部分
      let reasoningContent = "";
      let answerContent = "";

      // 检查answerList[0]是否存在且有reasoningPart和answerPart字段
      if (answerList[0]) {
        // 提取思考部分
        if (answerList[0].reasoningPart) {
          reasoningContent = answerList[0].reasoningPart;
        }

        // 提取答案部分
        if (answerList[0].answerPart) {
          answerContent = answerList[0].answerPart;
        }

        // 如果两个字段都没有，尝试使用整个对象作为答案
        if (!answerList[0].reasoningPart && !answerList[0].answerPart) {
          // 如果有target_language字段，使用它作为答案
          if (answerList[0].target_language) {
            answerContent = answerList[0].target_language;
          }
          // 如果没有target_language字段，尝试使用其他字段
          else if (
            answerList[0].llmModelInfo &&
            answerList[0].llmModelInfo.target_language
          ) {
            answerContent = answerList[0].llmModelInfo.target_language;
          }
        }
      }

      // 更新最后一条AI回答
      if (
        this.chatHistory.length > 0 &&
        this.chatHistory[this.chatHistory.length - 1].type === "ai"
      ) {
        // 如果已经有AI回答，则更新内容
        const lastMessage = this.chatHistory[this.chatHistory.length - 1];

        // 如果之前没有reasoningPart或answerPart属性，添加这些属性
        if (lastMessage.reasoningPart === undefined) {
          lastMessage.reasoningPart = "";
        }
        if (lastMessage.answerPart === undefined) {
          lastMessage.answerPart = "";
        }

        let contentChanged = false;

        // 只有当收到的内容不为空时才更新
        if (reasoningContent) {
          // 使用追加方式更新思考部分
          const newReasoningContent = lastMessage.reasoningPart + reasoningContent;

          // 保存当前打字状态和显示进度
          const wasTyping = lastMessage.isReasoningTyping === true;
          const currentDisplayLength = lastMessage.displayReasoningText ? lastMessage.displayReasoningText.length : 0;

          // 更新完整内容
          this.$set(lastMessage, "reasoningPart", newReasoningContent);
          contentChanged = true;

          // 添加思考部分打字效果相关属性
          if (!lastMessage.hasOwnProperty("isReasoningTyping")) {
            this.$set(lastMessage, "isReasoningTyping", true);
            this.$set(lastMessage, "displayReasoningText", "");

            // 首次添加内容时启动打字效果
            this.$nextTick(() => {
              this.updateReasoningTypeEffect(lastMessage);
            });
          } else {
            // 已经有打字效果，根据情况决定是否重启
            const isFarBehind = currentDisplayLength < (newReasoningContent.length - 50);

            // 如果正在进行打字效果且没有严重落后，不重新设置
            if (wasTyping && !isFarBehind) {
              // 不做任何改变，让现有的打字效果继续进行
            } else {
              // 如果没有进行打字效果或严重落后，重新开始
              if (newReasoningContent.length > 300 && (!wasTyping || currentDisplayLength < 20)) {
                // 对于非常长的内容，直接显示前面的部分，只对最后一部分使用打字效果
                const skipLength = Math.floor(newReasoningContent.length * 0.7); // 跳过70%
                this.$set(lastMessage, "displayReasoningText", newReasoningContent.substring(0, skipLength));
              }

              // 无论哪种情况，确保打字状态为true
              this.$set(lastMessage, "isReasoningTyping", true);

              // 确保启动或重启打字效果
              this.$nextTick(() => {
                // 先清除可能存在的定时器
                if (lastMessage.reasoningTypeTimer) {
                  clearTimeout(lastMessage.reasoningTypeTimer);
                }
                this.updateReasoningTypeEffect(lastMessage);
              });
            }
          }
        }

        if (answerContent) {
          // 保存当前打字状态和显示进度
          const wasTyping = lastMessage.isTyping === true;
          const currentDisplayLength = lastMessage.displayText ? lastMessage.displayText.length : 0;

          // 使用追加方式更新答案部分
          const newAnswerContent = lastMessage.answerPart + answerContent;
          this.$set(lastMessage, "answerPart", newAnswerContent);
          contentChanged = true;

          // 添加打字效果相关属性
          if (!lastMessage.hasOwnProperty("isTyping")) {
            this.$set(lastMessage, "isTyping", true);
            this.$set(lastMessage, "displayText", "");

            // 首次添加内容时启动打字效果
            this.$nextTick(() => {
              this.updateTypeEffect(lastMessage);
            });
          } else {
            // 已经有打字效果，根据情况决定是否重启
            const isFarBehind = currentDisplayLength < (newAnswerContent.length - 50);

            // 如果正在进行打字效果且没有严重落后，不重新设置
            if (wasTyping && !isFarBehind) {
              // 不做任何改变，让现有的打字效果继续进行
            } else {
              // 如果没有进行打字效果或严重落后，重新开始
              if (newAnswerContent.length > 200 && (!wasTyping || currentDisplayLength < 20)) {
                // 对于非常长的内容，直接显示前面的部分，只对最后一部分使用打字效果
                const skipLength = Math.floor(newAnswerContent.length * 0.7); // 跳过70%
                this.$set(lastMessage, "displayText", newAnswerContent.substring(0, skipLength));
              }

              // 无论哪种情况，确保打字状态为true
              this.$set(lastMessage, "isTyping", true);

              // 确保启动或重启打字效果
              this.$nextTick(() => {
                // 先清除可能存在的定时器
                if (lastMessage.typeTimer) {
                  clearTimeout(lastMessage.typeTimer);
                }
                this.updateTypeEffect(lastMessage);
              });
            }
          }
        }

        // 添加折叠状态属性，默认不折叠
        if (!lastMessage.hasOwnProperty("isThinkingCollapsed")) {
          this.$set(lastMessage, "isThinkingCollapsed", false);
        }

        // 只有在内容变化时才滚动到底部
        if (contentChanged) {
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      } else {
        // 如果没有AI回答，则添加一条
        const newMessage = {
          type: "ai",
          time: this.formatTime(new Date()),
          reasoningPart: reasoningContent,
          answerPart: answerContent,
          content: "",
          isThinkingCollapsed: false,
          isTyping: true,
          displayText: "",
          isReasoningTyping: true,
          displayReasoningText: ""
        };

        this.chatHistory.push(newMessage);

        // 启动打字效果
        this.$nextTick(() => {
          if (reasoningContent) {
            this.updateReasoningTypeEffect(newMessage);
          }
          if (answerContent) {
            this.updateTypeEffect(newMessage);
          }
          this.scrollToBottom();
        });
      }
    },

    // 更新打字效果，使用防抖动方式减少频繁刷新
    updateTypeEffect(message) {
      // 清除可能存在的定时器
      if (message.typeTimer) {
        clearTimeout(message.typeTimer);
        message.typeTimer = null;
      }

      // 计算应该显示的字符数
      const totalLength = message.answerPart.length;

      // 当前已显示的字符数
      const currentLength = message.displayText ? message.displayText.length : 0;

      // 如果已经显示了全部内容，不再继续
      if (currentLength >= totalLength) {
        message.isTyping = false;
        return;
      }

      // 确保打字状态为true
      message.isTyping = true;

      // 每次显示1-3个字符，模拟自然打字速度
      const charsToAdd = Math.min(1 + Math.floor(Math.random() * 2), totalLength - currentLength);
      const nextLength = currentLength + charsToAdd;

      // 更新显示文本
      this.$set(message, "displayText", message.answerPart.substring(0, nextLength));

      // 计算下一次更新的延迟时间（根据字符数随机变化，模拟真实打字）
      const nextDelay = 30 + Math.floor(Math.random() * 70); // 30-100ms的随机延迟

      // 如果还没显示完全部内容，继续更新
      if (nextLength < totalLength) {
        // 继续更新，保存定时器引用以便后续清除
        message.typeTimer = setTimeout(() => {
          this.updateTypeEffect(message);
        }, nextDelay);
      } else {
        // 已显示完全部内容
        message.typeTimer = setTimeout(() => {
          message.isTyping = false;
          message.typeTimer = null;
        }, 300); // 保持光标显示一小段时间后消失
      }
    },

    // 更新思考部分的打字效果
    updateReasoningTypeEffect(message) {
      // 清除可能存在的定时器
      if (message.reasoningTypeTimer) {
        clearTimeout(message.reasoningTypeTimer);
        message.reasoningTypeTimer = null;
      }

      // 计算应该显示的字符数
      const totalLength = message.reasoningPart ? message.reasoningPart.length : 0;

      // 如果没有内容，直接返回
      if (totalLength === 0) {
        message.isReasoningTyping = false;
        return;
      }

      // 当前已显示的字符数
      const currentLength = message.displayReasoningText ? message.displayReasoningText.length : 0;

      // 如果已经显示了全部内容，不再继续
      if (currentLength >= totalLength) {
        message.isReasoningTyping = false;
        return;
      }

      // 确保打字状态为true
      message.isReasoningTyping = true;

      // 每次显示1-3个字符，与答案部分保持一致
      const charsToAdd = Math.min(1 + Math.floor(Math.random() * 2), totalLength - currentLength);
      const nextLength = currentLength + charsToAdd;

      // 更新显示文本
      this.$set(message, "displayReasoningText", message.reasoningPart.substring(0, nextLength));

      // 确保不折叠，这样用户能看到打字效果
      if (message.isThinkingCollapsed) {
        this.$set(message, "isThinkingCollapsed", false);
      }

      // 计算下一次更新的延迟时间（与答案部分保持一致的随机延迟）
      const nextDelay = 30 + Math.floor(Math.random() * 70); // 30-100ms的随机延迟

      // 如果还没显示完全部内容，继续更新
      if (nextLength < totalLength) {
        // 继续更新，保存定时器引用以便后续清除
        message.reasoningTypeTimer = setTimeout(() => {
          this.updateReasoningTypeEffect(message);
        }, nextDelay);
      } else {
        // 已显示完全部内容
        message.reasoningTypeTimer = setTimeout(() => {
          message.isReasoningTyping = false;
          message.reasoningTypeTimer = null;
        }, 300); // 保持光标显示一小段时间后消失
      }
    },

    // 格式化消息内容，支持简单HTML
    formatMessage(message) {
      return message;
    },

    // 切换思考过程的折叠状态
    toggleThinking(item) {
      // 使用Vue的响应式特性来切换折叠状态
      this.$set(item, "isThinkingCollapsed", !item.isThinkingCollapsed);

      // 如果展开并且有打字效果，确保打字效果正常进行
      if (!item.isThinkingCollapsed && item.reasoningPart && !item.displayReasoningText) {
        // 如果还没有初始化打字效果
        this.$set(item, "isReasoningTyping", true);
        this.$set(item, "displayReasoningText", "");
        this.updateReasoningTypeEffect(item);
      }
    },

    // 格式化时间
    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      return `${hours}:${minutes}`;
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.chatContainer) {
          this.$refs.chatContainer.scrollTop =
            this.$refs.chatContainer.scrollHeight;
        }
      });
    },

    // 清空聊天记录
    clearChat() {
      this.$confirm("确定要清空所有聊天记录吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.chatHistory = [];
          localStorage.removeItem("aiChatHistory");
          this.$message({
            type: "success",
            message: "聊天记录已清空",
          });
        })
        .catch(() => {});
    },

    // 保存聊天历史到本地存储
    saveHistory() {
      localStorage.setItem("aiChatHistory", JSON.stringify(this.chatHistory));
    },

    // 聚焦输入框
    focusInput() {
      this.$refs.inputArea.focus();
    },

    // 使用常见问题
    useCommonQuestion(question) {
      this.userInput = question;
      this.sendMessage();
    },

    // 跳过打字效果，直接显示完整内容
    skipTypeEffect(message) {
      if (!message.isTyping) return;

      // 清除打字效果定时器
      if (message.typeTimer) {
        clearTimeout(message.typeTimer);
        message.typeTimer = null;
      }

      // 直接显示完整内容
      this.$set(message, "displayText", message.answerPart || message.content);
      message.isTyping = false;

      // 通知用户已跳过打字效果
      this.$message({
        message: '已跳过打字效果',
        type: 'info',
        duration: 1000,
        showClose: false
      });
    },

    // 跳过思考过程的打字效果
    skipReasoningTypeEffect(item) {
      if (!item.isReasoningTyping) return;

      // 清除打字效果定时器
      if (item.reasoningTypeTimer) {
        clearTimeout(item.reasoningTypeTimer);
        item.reasoningTypeTimer = null;
      }

      // 直接显示完整思考过程
      this.$set(item, "displayReasoningText", item.reasoningPart);
      item.isReasoningTyping = false;

      // 通知用户已跳过思考过程的打字效果
      this.$message({
        message: '已跳过思考过程的打字效果',
        type: 'info',
        duration: 1000,
        showClose: false
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.qa-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.chat-container {
  height: calc(100vh - 290px);
  min-height: 400px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  // margin-bottom: 20px;
}

.empty-chat {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.message-item {
  display: flex;
  margin-bottom: 20px;
  position: relative;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 10px;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background-color: #67c23a;
}

.message-content {
  max-width: 70%;
  border-radius: 4px;
  padding: 10px;
  background-color: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.message-sender {
  font-weight: bold;
  margin-bottom: 5px;
  color: #409eff;
}

.user-message .message-sender {
  color: #67c23a;
}

.message-text {
  line-height: 1.5;
  word-break: break-word;
  font-size: 14px;
}

/* AI思考容器样式 */
.chat-ai-container {
  display: flex;
  flex-direction: column;
}

.ai-thinking-container {
  margin-bottom: 10px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8f9fa;
}

.ai-thinking-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  color: #409eff;
  background-color: #f0f7ff;
  border-radius: 8px;
}

.thinking-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
}

.thinking-time {
  flex: 1;
  font-size: 14px;
  color: #409eff;
  margin-right: 8px;
}

.thinking-title {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.thinking-toggle-icon {
  transition: transform 0.3s ease;
}

.ai-thinking-content {
  padding: 12px;
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  max-height: 500px;
  overflow-y: auto;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
  border-top: 1px solid #ebeef5;
  cursor: pointer;
}

/* 折叠状态 */
.thinking-collapsed .ai-thinking-content {
  max-height: 0;
  padding: 0;
  border-top: none;
  overflow: hidden;
}

.thinking-collapsed .thinking-toggle-icon {
  transform: rotate(180deg);
}

/* 打字光标样式 */
.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 18px;
  background-color: #409eff;
  margin-left: 2px;
  animation: blink 0.7s infinite;
  vertical-align: middle;
  position: relative;
  top: -1px;
  box-shadow: 0 0 3px #409eff;
  border-radius: 1px;
}

/* 思考部分的光标样式 */
.reasoning-cursor {
  background-color: #67c23a;
  box-shadow: 0 0 3px #67c23a;
  height: 18px;
  animation: blink 0.7s infinite;
}

/* 防止文本闪烁 */
.ai-thinking-content span {
  display: inline;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

/* 答案部分样式 */
.ai-answer {
  padding: 5px 0;
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
  cursor: pointer;
}

/* 兼容原来的样式 */
.answer-part {
  padding: 10px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border-left: 3px solid #67c23a;
}

.answer-part {
  padding: 10px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border-left: 3px solid #67c23a;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  text-align: right;
}

.loading-message {
  display: flex;
  margin-bottom: 20px;
}

.input-container {
  padding: 10px;
  border-top: 1px solid #ebeef5;
  display: flex;
  align-items: flex-end;
}

.button-container {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
}

.button-container .el-button {
  margin-bottom: 10px;
}

/* 停止生成按钮样式 */
.stop-generating-container {
  margin: 10px 0;
  display: flex;
  justify-content: center;
  width: 100%;
}

.stop-generating-container .el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.common-questions {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.common-question-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.common-question-tag:hover {
  transform: scale(1.05);
}

::v-deep .el-textarea__inner {
  resize: none;
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 14px;
  line-height: 1.5;
}

@media screen and (max-width: 768px) {
  .qa-card {
    margin-bottom: 10px;
  }

  .chat-container {
    height: calc(100vh - 250px);
  }

  .message-content {
    max-width: 85%;
  }
}

/* 确保内容区域的样式一致 */
.ai-thinking-content div {
  line-height: 1.6;
}
</style>
