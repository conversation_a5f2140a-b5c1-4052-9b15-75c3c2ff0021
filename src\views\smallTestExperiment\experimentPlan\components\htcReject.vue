<template>
    <div>
      <el-dialog
        title="驳回"
        :visible.sync="dialogVisible"
        append-to-body
        width="600px"
        @close="cancel"
      >
      <el-form ref="form" :model="form" :rules="rules">
              <el-form-item
              label="驳回原因"
              prop="rejectReason"
              >
                <el-input
                type="textarea"
                :rows="5"
                style="width: 80%"
                v-model="form.rejectReason"
                placeholder="请输入"
                ></el-input>
              </el-form-item>
  
              <el-form-item
              label="补充文件"
              >
                  <upload-file
                  :show-file-list="true"
                  :file-list="fileList"
                  multiple
                  @file-success="fileSuccess"
                  @before-upload="beforeUpload"
                  @before-remove="beforeRemove"
                  @file-error="fileError"
                  >
                      <el-button type="primary" size="small">上传文件</el-button>
                  </upload-file>
              </el-form-item>
  
          </el-form>
  
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="confirm" :loading="btnLoading">确定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  <script>
  import { submitLastHtc } from "@/api/smallTestExperiment/experimentPlan.js";
  import uploadFile from '@/components/uploadFile/main'
  export default {
    components:{uploadFile},
    data() {
      return {
        title: "",
        btnLoading: false,
        dialogVisible: false,
        fileList:[],
        userId:null,
        form: {
          rejectReason: '',
          fileList:[]
        },
        rowData: {},
        htcStatus: null,
        rules:{
          rejectReason:{ required: true, message: '请输入驳回原因', trigger: 'blur' },   
        }
      };
    },
    methods: {
      //初始化
      init(val) {
        this.dialogVisible = true;
        this.rowData = val;
      },

  
      // 确定
      confirm() {
        this.$refs["form"].validate((valid) => {
          if (valid) {
            this.btnLoading = true;
            let params = {
              htcStatus:0,
              id:this.rowData.id,
              nodeId:this.rowData.nodeId,
              planId:this.rowData.planId,
              fileList:this.form.fileList,
              rejectReason:this.form.rejectReason
            }
            submitLastHtc(params).then((res)=>{
              this.btnLoading = false
              this.$message({
                duration:1500,
                message:'操作成功',
                type:'success'
              })
              this.$emit('getDetail')
              this.dialogVisible = false
            }).catch(()=>{
              this.btnLoading = false;
            })
          }
        });
      },
  
      // 上传文件前
      beforeUpload(file,callBack){
          this.btnLoading = true
      },
  
      // 上传成功
      fileSuccess(response,file){
          this.form.fileList.push(response.data)
          this.btnLoading = false
      },
  
      // 文件上传失败
      fileError(error){
          this.btnLoading = false
      },
  
      // 删除文件前
      beforeRemove(file,fileList){
          let id = file.response.data.id
          let index = this.form.fileList.findIndex((item)=>{
              return item.id === id
          })
          this.form.fileList.splice(index,1)
      },
  
      cancel(){
        this.$refs.form.clearValidate();
        this.dialogVisible = false
      }
    },
  };
  </script>
  <style scoped lang="scss">
  .btn-box {
    margin-bottom: 8px;
    padding-left: 36px;
    display: flex;
    align-items: center;
    .btn-label {
      width: 85px;
    }
  }
  </style>
  