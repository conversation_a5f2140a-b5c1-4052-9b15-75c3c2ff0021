<template>
    <div class="pending_task">
      <div class="search_form">
        <el-row type="flex" :gutter="6">
          <el-col :span="4">
            <el-input
                class="format_option"
                v-model="searchForm.processName"
                placeholder="请输入流程名称"
                clearable
            ></el-input>
          </el-col>

          <el-col :span="5">
            <el-date-picker
                v-model="time"
                class="format_option"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
            </el-date-picker>
          </el-col>

          <el-col :span="6">
            <div class="btn_box">
              <el-button
                @click="search()"
                class="btn search_btn"
                icon="el-icon-search"
                type="primary"
                >搜索</el-button
              >
              <el-button
                @click="search(true)"
                class="btn reset_btn"
                icon="el-icon-refresh"
                >重置</el-button
              >
            </div>
          </el-col>
        </el-row>
      </div>
      <!-- <div class="operation_btn">
        <el-button
          class="btn add_btn"
          icon="el-icon-plus"
          type="primary"
          @click="add()"
          >新增</el-button
        >
      </div> -->

      <div class="table_box" v-loading="loading">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column type="index" label="序号" width="80" align="center">
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableColumn"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            align="center"
            :width="item.width"
          >
            <template slot-scope="{ row }">
              <div v-if="item.checkTime">
                <i class="el-icon-time"></i>
                {{ row[item.prop] | dateTimeFormat }}
              </div>
              <el-tag v-else-if="item.versionStatus" type="default">v{{ row[item.prop] }}</el-tag>
              <div v-else>{{ row[item.prop] }}</div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="200">
            <template slot-scope="{ row }">
              <div class="handle_btn">
                <el-popconfirm
                  title="是否确定签收？"
                  @confirm="handleSignIn(row)"
                >
                  <el-button
                    slot="reference"
                    type="text"
                    size="small"
                    icon="el-icon-s-claim"
                    >签收</el-button
                  >
                </el-popconfirm>

              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="page_box">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentPage"
            :pager-count="5"
            :current-page="page.pageNum"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="10"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </template>
  <script>
  import {claimListList,receipt} from '@/api/officeMan/pendingTask'
  export default {
    data() {
      return {
        loading: false,
        searchForm: {
          processName: "",
        },
        time:[],
        page: {
          pageSize: 10,
          pageNum: 1,
          total: 0,
        },
        tableData: [],
        tableColumn: [
          {
            prop: "procInsId",
            label: "任务编号",
            width: "300",
          },
          {
            prop: "procDefName",
            label: "任务名称",
            width: "200",
          },
          {
            prop: "taskName",
            label: "任务节点",
            width: "200",
          },
          {
            prop: "procDefVersion",
            label: "流程版本",
            versionStatus:true,
            width: "",
          },
          {
            prop: "startUserName",
            label: "流程发起人",
            width: "200",
          },
          {
            prop: "createTime",
            label: "接收时间",
            checkTime: true,
            width: "200",
          },
        ],
      };
    },
    methods: {
      hideDialog() {
        this.loadData();
      },
      // 签收
      handleSignIn(row){
        this.loading = true
        let params = {
          taskId:row.taskId
        }
        receipt(params).then((res)=>{
          this.$message({
            type:'success',
            message:'操作成功',
            duration:1500
          })
          this.loadData()
        }).catch(()=>{
          this.loading = false
        })

      },

      search(reset) {
        if (reset) {
          this.searchForm = {
            processName: "",
          };
        }
        this.page.pageNum = 1;
        this.loadData();
      },

      loadData() {
        this.loading = true
        this.searchForm.beginTime = this.time ? this.time[0] : ''
        this.searchForm.endTime = this.time ? this.time[1] : ''
        let params = {
          ...this.page,
          ...this.searchForm
        }
        claimListList(params).then((res)=>{
          let resData = res.data
          this.tableData = resData.records
          this.page.total = resData.total
          this.loading = false
        }).catch(()=>{
          this.loading = false
        })
      },

      // 更改每页显示条数
      handleSizeChange(pageSize) {
        this.page.pageSize = pageSize;
        this.loadData();
      },

      // 选择页数/点击上一页/下一页
      handleCurrentPage(currentPage) {
        this.page.pageNum = currentPage;
        this.loadData();
      },
    },
    created() {
      this.loadData();
    },
  };
  </script>
  <style lang="less" scoped>
  .pending_task {
    padding: 16px 12px 0;
  }
  </style>
