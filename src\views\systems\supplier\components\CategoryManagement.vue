<template>
  <div>
    <!-- 管理分类弹窗 -->
    <el-dialog
      :title="
        '管理分类 - ' + (currentSupplier ? currentSupplier.supplierName : '')
      "
      :visible.sync="categoryDialogVisible"
      width="800px"
      append-to-body
    >
      <div class="category-dialog-container">
        <div class="search-container">
          <el-input
            placeholder="分类编号/产品名称"
            v-model="categorySearch"
            class="input-with-select"
            clearable
            @keyup.enter.native="searchCategory"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="searchCategory"
            ></el-button>
          </el-input>
          <el-button
            type="primary"
            plain
            icon="el-icon-refresh"
            size="small"
            @click="resetCategorySearch"
            >重置</el-button
          >
        </div>

        <div class="category-action-container">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="small"
            @click="handleAddCategory"
            >新增</el-button
          >
          <!-- <el-button type="danger" plain icon="el-icon-delete" size="small" :disabled="!selectedCategories.length"
            @click="handleDeleteCategories">删除</el-button> -->
        </div>
        <el-table
          :data="categoryList"
          style="width: 100%"
          height="300"
          @selection-change="handleCategorySelectionChange"
        >
          <!-- <el-table-column type="selection" width="55">
          </el-table-column> -->
          <el-table-column label="序号" type="index" width="50">
            <template slot-scope="scope">
              {{
                (categoryPagination.pageNum - 1) * categoryPagination.pageSize +
                scope.$index +
                1
              }}
            </template>
          </el-table-column>
          <el-table-column prop="typeNo" label="分类编号" width="150">
          </el-table-column>
          <el-table-column
            prop="typeName"
            label="产品名称"
            width="150"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="deptName"
            label="责任部门"
            show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              {{
                scope.row.techDeptList.map((item) => item.deptName).join("、")
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="status"
            label="是否开启业绩评选开关"
            width="150"
            align="center"
          >
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.evaluationSwitch"
                :active-value="1"
                :inactive-value="0"
                @change="handleEvaluationSwitchChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="status" label="是否适用" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status ? 'success' : 'info'">{{ scope.row.status ? '是' : '否' }}</el-tag>
            </template>
          </el-table-column> -->
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDeleteCategory(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @size-change="handleCategorySizeChange"
            @current-change="handleCategoryCurrentChange"
            :current-page="categoryPagination.pageNum"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="categoryPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="categoryPagination.total"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>

    <!-- 新增分类弹窗 -->
    <el-dialog
      :title="'新增分类'"
      :visible.sync="addCategoryDialogVisible"
      top="30"
      height="300"
      width="800px"
      append-to-body
    >
      <div class="add-category-container">
        <div class="search-container">
          <el-input
            placeholder="分类编号/产品名称"
            v-model="addCategorySearch"
            class="input-with-select"
            clearable
            @keyup.enter.native="searchAddCategory"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="searchAddCategory"
            ></el-button>
          </el-input>
          <el-button
            type="primary"
            plain
            icon="el-icon-refresh"
            size="small"
            @click="resetAddCategorySearch"
            >重置</el-button
          >
        </div>

        <el-table
          :data="availableCategoryList"
          style="width: 100%"
          @selection-change="handleAddCategorySelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column label="序号" type="index" width="50">
            <template slot-scope="scope">
              {{
                (addCategoryPagination.pageNum - 1) *
                  addCategoryPagination.pageSize +
                scope.$index +
                1
              }}
            </template>
          </el-table-column>
          <el-table-column prop="typeNo" label="分类编号" width="150">
          </el-table-column>
          <el-table-column
            prop="typeName"
            label="产品分类"
            width="250"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="techDeptList"
            label="责任部门"
            width="250"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{
                scope.row.techDeptList
                  ? scope.row.techDeptList
                      .map((item) => item.deptName)
                      .join("、")
                  : ""
              }}
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @size-change="handleAddCategorySizeChange"
            @current-change="handleAddCategoryCurrentChange"
            :current-page="addCategoryPagination.pageNum"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="addCategoryPagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="addCategoryPagination.total"
          >
          </el-pagination>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addCategoryDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAddCategories">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getNoAddPage,
  getNoAdd,
  getNoPage,
  apiSupplierListDelete,
  apiSupplierListStatusEdit,
} from "@/api/system/supplier";
export default {
  name: "CategoryManagement",
  props: {
    // 是否显示管理分类弹窗
    visible: {
      type: Boolean,
      default: false,
    },
    // 当前选中的供应商
    supplier: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      // 管理分类相关数据
      categoryDialogVisible: false,
      addCategoryDialogVisible: false,
      currentSupplier: null,
      categorySearch: "",
      categoryList: [],
      selectedCategories: [],
      categoryPagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      // 新增分类相关数据
      addCategorySearch: "",
      availableCategoryList: [],
      selectedAddCategories: [],
      addCategoryPagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      categoryForm: {
        supplierId: "",
        categoryCode: "",
        productName: "",
        deptName: "",
        isUsed: true,
      },
      categoryRules: {
        categoryCode: [
          { required: true, message: "请输入分类编号", trigger: "blur" },
        ],
        productName: [
          { required: true, message: "请输入产品名称", trigger: "blur" },
        ],
        deptName: [
          { required: true, message: "请选择责任部门", trigger: "change" },
        ],
      },
      deptOptions: [
        { value: "工艺部", label: "工艺部" },
        { value: "生产部", label: "生产部" },
        { value: "采购部", label: "采购部" },
        { value: "质量部", label: "质量部" },
      ],
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.categoryDialogVisible = true;
        // 当弹窗打开时，如果已有供应商信息，则获取分类列表
        if (this.currentSupplier && this.currentSupplier.supplierCode) {
          this.getCategoryList();
        }
      } else {
        this.categoryDialogVisible = false;
      }
    },
    categoryDialogVisible(val) {
      if (!val) {
        this.$emit("update:visible", false);
      }
    },
    supplier: {
      handler(val) {
        if (val) {
          console.log("supplier变化", val);
          this.currentSupplier = val;
          this.categoryForm.supplierId = val.id;

          // 当供应商信息变化且弹窗已打开时，重新获取分类列表
          if (this.categoryDialogVisible && val.supplierCode) {
            this.getCategoryList();
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleEvaluationSwitchChange(value) {
      console.log("value", value);

      apiSupplierListStatusEdit({
        id: value.id,
        status: value.evaluationSwitch,
      })
        .then((res) => {
          this.$message.success("操作成功");
          this.getCategoryList();
        })
        .catch((err) => {
          this.$message.error("操作失败");
        });
    },
    // 关闭弹窗
    closeDialog() {
      this.categoryDialogVisible = false;
    },
    // 获取分类列表
    getCategoryList() {
      if (!this.currentSupplier || !this.currentSupplier.supplierCode) {
        console.error("缺少供应商信息，无法获取分类列表");
        return;
      }

      getNoPage({
        supplierCode: this.currentSupplier.supplierCode,
        pageNum: this.categoryPagination.pageNum,
        pageSize: this.categoryPagination.pageSize,
        query: this.categorySearch,
      })
        .then((res) => {
          console.log("分类列表结果", res);
          this.categoryList = res.data.records;
          this.categoryPagination.total = res.data.total;
        })
        .catch((err) => {
          console.error("获取分类列表失败", err);
        });
    },
    // 搜索分类
    searchCategory() {
      this.categoryPagination.pageNum = 1;
      this.getCategoryList();
    },
    // 重置分类搜索
    resetCategorySearch() {
      this.categorySearch = "";
      this.searchCategory();
    },
    // 分类表格选择变化
    handleCategorySelectionChange(selection) {
      this.selectedCategories = selection;
    },
    // 分类分页大小变化
    handleCategorySizeChange(size) {
      this.categoryPagination.pageSize = size;
      this.getCategoryList();
    },
    // 分类当前页变化
    handleCategoryCurrentChange(page) {
      this.categoryPagination.pageNum = page;
      this.getCategoryList();
    },
    // 新增分类
    handleAddCategory() {
      this.addCategorySearch = "";
      this.selectedAddCategories = [];
      this.addCategoryPagination = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
      this.getAvailableCategoryList();
      this.addCategoryDialogVisible = true;
    },
    // 获取可选的分类列表
    getAvailableCategoryList() {
      if (!this.currentSupplier || !this.currentSupplier.supplierCode) {
        console.error("缺少供应商信息，无法获取可选分类列表");
        return;
      }

      getNoAddPage({
        supplierCode: this.currentSupplier.supplierCode,
        pageNum: this.addCategoryPagination.pageNum,
        pageSize: this.addCategoryPagination.pageSize,
        query: this.addCategorySearch,
      })
        .then((res) => {
          this.availableCategoryList = res.data.records;
          this.addCategoryPagination.total = res.data.total;
        })
        .catch((err) => {
          console.error("获取可选分类列表失败", err);
        });
    },
    // 搜索可选分类
    searchAddCategory() {
      this.addCategoryPagination.pageNum = 1;
      this.getAvailableCategoryList();
    },
    // 重置分类搜索
    resetAddCategorySearch() {
      this.addCategorySearch = "";
      this.searchAddCategory();
    },
    // 分类表格选择变化
    handleAddCategorySelectionChange(selection) {
      this.selectedAddCategories = selection;
    },
    // 分类分页大小变化
    handleAddCategorySizeChange(size) {
      this.addCategoryPagination.pageSize = size;
      this.getAvailableCategoryList();
    },
    // 分类当前页变化
    handleAddCategoryCurrentChange(page) {
      this.addCategoryPagination.pageNum = page;
      this.getAvailableCategoryList();
    },
    // 提交新增分类
    submitAddCategories() {
      if (this.selectedAddCategories.length === 0) {
        this.$modal.msgWarning("请至少选择一个分类");
        return;
      }

      if (!this.currentSupplier || !this.currentSupplier.supplierCode) {
        this.$modal.msgError("缺少供应商信息，无法新增分类");
        return;
      }

      const categoryIds = this.selectedAddCategories.map((item) => item.id);
      getNoAdd({
        ...this.currentSupplier,
        typeList: this.selectedAddCategories,
      })
        .then((res) => {
          this.$modal.msgSuccess("新增成功");
          this.addCategoryDialogVisible = false;
          this.getCategoryList();
        })
        .catch((err) => {
          console.error("新增分类失败", err);
          this.$modal.msgError("新增失败");
        });
    },
    // 删除单个分类
    handleDeleteCategory(row) {
      this.$modal
        .confirm(`确定删除分类 ${row.typeNo} 吗？`)
        .then(() => {
          if (!this.currentSupplier || !this.currentSupplier.supplierCode) {
            this.$modal.msgError("缺少供应商信息，无法删除分类");
            return;
          }

          apiSupplierListDelete({ id: row.id })
            .then((res) => {
              this.$modal.msgSuccess("删除成功");
              this.getCategoryList();
            })
            .catch((err) => {
              console.error("删除分类失败", err);
              this.$modal.msgError("删除失败");
            });
        })
        .catch(() => {});
    },
    // 批量删除分类
    handleDeleteCategories() {
      const ids = this.selectedCategories.map((item) => item.id);
      this.$modal
        .confirm(`确定删除选中的 ${ids.length} 个分类吗？`)
        .then(() => {
          if (!this.currentSupplier || !this.currentSupplier.supplierCode) {
            this.$modal.msgError("缺少供应商信息，无法删除分类");
            return;
          }

          getNoAdd({
            supplierCode: this.currentSupplier.supplierCode,
            categoryIds: ids,
            type: "delete",
          })
            .then((res) => {
              this.$modal.msgSuccess("删除成功");
              this.getCategoryList();
            })
            .catch((err) => {
              console.error("批量删除分类失败", err);
              this.$modal.msgError("删除失败");
            });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.category-dialog-container,
.add-category-container {
  padding: 0 10px;
}

.search-container {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
}

.search-container .el-input {
  margin-right: 10px;
  width: 300px;
}

.category-action-container {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}
</style>
