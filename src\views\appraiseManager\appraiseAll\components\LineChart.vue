<template>
  <div class="chart-container">
    <div class="chart-header">
      <div class="chart-title">考核事件数量</div>
    </div>
    <date-range-picker class="chart-header" @date-change="handleDateChange" />
    <div ref="lineChart" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import DateRangePicker from './DateRangePicker.vue'
import { apiGetEventCountList } from '@/api/appraiseManager/appraiseAll'
export default {
  name: 'LineChart',
  components: {
    DateRangePicker
  },
  data() {
    return {
      chart: null,
      eventCountList: [],
      eventXAxisData: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', this.resizeChart)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.$refs.lineChart) return

      this.chart = echarts.init(this.$refs.lineChart)
      const option = {
        grid: {
          left: '3%',
          right: '4%',
          top: '10%',  // 增加上边距，给标签留出更多空间
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.eventXAxisData,
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          min: 0,
          splitNumber: 5
        },
        series: [{
          type: 'line',
          data: this.eventCountList,
          itemStyle: {
            color: '#409EFF'
          },
          smooth: false,
          label: {
            show: true,
            position: 'top',
            color: '#606266',
            padding: [5, 0, 0, 0],  // 添加上边距
            formatter: '{c}'  // 确保数值正确显示
          },
          symbol: 'circle',
          symbolSize: 8
        }]
      }
      this.chart.setOption(option)
    },
    resizeChart() {
      this.chart && this.chart.resize()
    },
    handleDateChange(date) {
      // 处理日期变化，可以在这里更新图表数据
      this.getEventCountList(date)
    },
    getEventCountList(date) {
      apiGetEventCountList({
        startTime: date.dateRange[0],
        endTime: date.dateRange[1],
        type: { custom: 1, month: 2, quarter: 3, year: 4 }[date.type]
      }).then((response) => {
        let list = [], data = []
        response.data.map(item => {
          list.push(item.num)
          data.push(item.time)
        })
        this.eventCountList = list
        this.eventXAxisData = data
        this.initChart()
      })
    }
  }
}
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.chart {
  width: 100%;
  height: 300px;
}
</style>
