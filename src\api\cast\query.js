import request from '@/utils/request'
// 分页查询节点记录列表
export function findAllPage(param) {
    return request({
        url: '/back-server/castingNode/findAllPage',
        method: 'get',
        params: param,
    })
}
// 分页查询节点记录-详情列表
export function getListPage(param) {
    return request({
        url: '/back-server/castingNodeRecord/getListPage',
        method: 'get',
        params: param,
    })
}
// 分页查询节点记录-详情-查看列表
export function getDetails(param) {
    return request({
        url: '/back-server/castingNodeRecord/getDetails',
        method: 'get',
        params: param,
    })
}