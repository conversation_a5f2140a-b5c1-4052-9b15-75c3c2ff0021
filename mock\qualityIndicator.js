import Mock from 'mockjs'

// 获取详情接口
Mock.mock(new RegExp('/api/quality/deduct/detail.*'), 'get', () => {
  return {
    code: 200,
    data: {
      eventDate: '2024-10-12 09:41',
      contractNo: 'HTC-04641',
      eventType: '质量',
      supplierCode: '30005789',
      secondCategory: 'delivery_time',
      supplierNo: 'HTCO45874',
      categoryNo: 'C1-01-11-01',
      supplierName: '哈尔滨电力设备有限公司',
      reason: '质量问题',
      subject: '关于华能大连电厂通道',
      ncrHandle: '*********',
      problemDesc: '2023年5月',
      customerName: '华能大连电',
      orderNo: '196103',
      drawingNo: 'CH02F.0902',
      archiveDate: '2024-11-19',
      responsibleDept: '采购部',
      responsibleSupplier: '采购部',
      responsiblePerson: '关旭',
      projectDept: '项目服务部',
      groupStandard: '一般质量问题',
      productSpec: '火电',
      score: 20
    },
    message: 'success'
  }
})

// 更新接口
Mock.mock('/api/quality/deduct/update', 'post', () => {
  return {
    code: 200,
    message: '修改成功'
  }
})

// 上传接口
Mock.mock('/api/quality/deduct/upload', 'post', () => {
  return {
    code: 200,
    data: {
      fileUrl: 'http://example.com/file.pdf',
      fileName: 'evidence.pdf'
    },
    message: '上传成功'
  }
})

// 删除接口
Mock.mock('/api/quality/deduct/delete', 'post', () => {
  return {
    code: 200,
    message: '删除成功'
  }
})

// 历史记录接口
Mock.mock(/\/api\/quality\/deduct\/history.*/, 'get', () => {
  return {
    code: 200,
    data: {
      detail: {
        eventDate: '2024-10-12 09:41',
        eventType: '交货',
        contractNo: 'HTC-04641',
        supplierCode: '30005789',
        secondCategory: '交货准时度',
        supplierNo: 'HTCO45874',
        categoryNo: 'C1-01-11-01',
        supplierName: '哈尔滨电力设备有限公司',
        reason: '延期交货3天'
      },
      history: [
        {
          operateTime: '2024-11-11 8:00',
          operator: '王强',
          status: '修改',
          deleteReason: '操作错误',
          file: '操作记录.pdf'
        },
        {
          operateTime: '2024-11-10 10:00',
          operator: '王强',
          status: '修改',
          score: '10',
          newScore: '20',
          reason: '操作错误',
          file: '操作记录.pdf'
        }
      ]
    },
    message: 'success'
  }
})
