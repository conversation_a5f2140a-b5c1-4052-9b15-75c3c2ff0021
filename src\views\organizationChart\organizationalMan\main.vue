<template>
  <div class="organizational_man" v-loading="loading">
    <div class="organizational_list">
      <div class="tree_list">
        <div class="title">
          <span>组织列表</span>
        </div>
        <div class="inp_box">
          <el-input
            v-model="organizationName"
            placeholder="请输入部门名称"
          ></el-input>
        </div>
        <div class="tree_box scroll_bar">
          <el-tree
            ref="nodeTree"
            :props="defaultProps"
            :data="treeData"
            node-key="id"
            :current-node-key="parentId"
            @node-click="nodeClick"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            accordion
          >
          </el-tree>
        </div>
      </div>
    </div>

    <div class="organizational_table">
      <div class="company_info">
        <div class="search_form">
          <el-row type="flex" :gutter="4">
            <!-- <el-col :span="4">
              <el-select
                size="small"
                v-model="searchForm.status"
                placeholder="请选择状态"
                clearable
                filterable
              >
                <el-option
                  v-for="item in dicList.origanizationStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-col> -->
            <el-col :span="6">
              <el-input
                size="small"
                v-model="searchForm.name"
                placeholder="请输入部门名称"
                clearable
              ></el-input>
            </el-col>

            <el-col :span="6">
              <div class="btn_box">
                <el-button
                  @click="search(false)"
                  size="small"
                  class="btn search_btn"
                  icon="el-icon-search"
                  type="primary"
                  >搜索</el-button
                >
                <el-button
                  @click="search(true)"
                  size="small"
                  class="btn reset_btn"
                  icon="el-icon-refresh"
                  >重置</el-button
                >
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="operation_btn">
          <el-button
            size="small"
            class="btn add_btn"
            icon="el-icon-plus"
            type="primary"
            @click="add()"
            >新增</el-button
          >

          <!-- <el-popconfirm
            title="是否批量导出？"
            class="el_prop"
            @confirm="allExp()"
          >
            <el-button
              slot="reference"
              size="small"
              class="btn add_btn"
              icon="el-icon-upload2"
              type="primary"
              >批量导出</el-button
            >
          </el-popconfirm> -->

          <!-- <el-popconfirm
            title="是否确定同步部门信息？"
            @confirm="syncDeptInfo()"
          >
            <el-button
              type="warning"
              slot="reference"
              size="small"
              icon="el-icon-refresh"
              >同步部门信息</el-button
            >
          </el-popconfirm> -->
        </div>

        <div class="table_box scroll_bar">
          <el-table
            :data="tableData"
            v-loading="tableLoading"
            row-key="id"
            height="100%"
            style="width: 100%"
          >
            <el-table-column
              v-for="(item, index) in tableColumn"
              :key="index"
              :prop="item.prop"
              :label="item.label"
              align="center"
              :width="item.width"
            >
              <template slot-scope="{ row }">
                <el-tag
                  v-if="item.tabStatus"
                  :type="row.status === 0 ? 'danger' : 'default'"
                  >{{ row[item.prop] | checkDic(item.optionsStatus) }}</el-tag
                >
                <div v-else-if="item.checkTime">
                  <i class="el-icon-time"></i>
                  {{ row[item.prop] | dateTimeFormat }}
                </div>
                <div v-else>{{ row[item.prop] }}</div>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              width="250"
            >
              <template slot-scope="{ row }">
                <div class="handle_btn">
                  <el-button
                    @click="info(row)"
                    type="text"
                    size="small"
                    icon="el-icon-view"
                    >详情</el-button
                  >
                  <el-button
                    @click="handlePropleInfo(row)"
                    type="text"
                    size="small"
                    icon="el-icon-view"
                    >查看人员</el-button
                  >
                  <el-button
                    @click="add(row)"
                    type="text"
                    size="small"
                    icon="el-icon-edit"
                    >修改</el-button
                  >
                  <el-popconfirm
                    title="是否确定删除？"
                    @confirm="handleDel(row)"
                  >
                    <el-button
                      slot="reference"
                      class="del"
                      type="text"
                      size="small"
                      icon="el-icon-delete"
                      >删除</el-button
                    >
                  </el-popconfirm>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="page_box">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentPage"
            :current-page="page.pageNum"
            :pager-count="5"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="10"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
          >
          </el-pagination>
        </div>

        <add-department
          ref="addDepartment"
          @hideDialog="hideDialog"
        ></add-department>
        <department-info ref="departmentInfo"></department-info>
        <personnel-table-info ref="personnelTableInfo"></personnel-table-info>
      </div>
    </div>
  </div>
</template>
<script>
import {
  deptTree,
  deptList,
  delDept,
  syncDeptInfo,
} from "@/api/organizationChart/organizationalMan.js";
import addDepartment from "./components/addDepartment.vue";
import departmentInfo from "./components/departmentInfo.vue";
import personnelTableInfo from "./components/personnelTableInfo.vue";
import store from "@/store/index";
export default {
  components: {
    addDepartment,
    departmentInfo,
    personnelTableInfo,
  },
  data() {
    return {
      loading: false,
      tableLoading: false,
      companyInfo: true,
      organizationName: "",
      parentId: "05",
      dicList: store.state.dicList,
      treeData: [],
      defaultProps: {
        children: "children",
        label: "name",
      },

      selectRow: [], // 存放选中的数据
      searchForm: {
        name: "",
        status: "",
      },
      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },

      tableData: [],

      tableColumn: [
        {
          prop: "no",
          label: "编码",
          width: "",
        },
        {
          prop: "name",
          label: "名称",
          width: "300",
        },
        {
          prop: "status",
          label: "状态",
          optionsStatus: "origanizationStatus",
          tabStatus: true,
          width: "",
        },
        {
          prop: "principalName",
          label: "负责人",
          width: "",
        },
        {
          prop: "createTime",
          label: "创建时间",
          checkTime: true,
          width: "200",
        },
      ],
    };
  },
  watch: {
    organizationName(val) {
      this.$refs.nodeTree.filter(val);
    },
  },
  methods: {
    hideDialog(dialog) {
      this[`${dialog}Visible`] = false;
      this.initLoad(true);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 初始化数据
    async initLoad(flag) {
      this.loading = true;
      await this.loadTree(flag);
      this.$refs.nodeTree.setCurrentKey(this.parentId);
      await this.loadTable(true);
      this.loading = false;
    },
    // 获取组织列表
    async loadTree(flag) {
      await deptTree()
        .then((res) => {

          let resData = res.data;
          this.treeData = resData;
          this.parentId = flag ? this.parentId : resData[0].id;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 点击树节点
    nodeClick(currentNode) {
      this.parentId = currentNode.id;
      this.loadTable();
    },

    // 搜索/重置
    search(reset) {
      if (reset) {
        this.searchForm = {
          name: "",
          status: "",
        };
      }
      this.page.pageNum = 1;
      this.loadTable();
    },
    // 新增/修改
    add(row) {
      this.$refs.addDepartment.init(row);
    },
    // 详情
    info(row) {
      this.$refs.departmentInfo.init(row);
    },
    // 删除
    handleDel(row) {
      this.loading = true;
      delDept(row.id)
        .then((res) => {
          this.$message({
            type: "success",
            message: "删除成功",
            duration: 1500,
          });
          this.loadTable();
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 同步部门信息
    syncDeptInfo() {
      this.searchForm = {
        name: "",
        status: "",
      };
      this.page.pageNum = 1;
      this.loading = true;
      syncDeptInfo()
        .then((res) => {
          this.$message({
            type: "success",
            message: "操作成功",
            duration: 1500,
          });
          this.loadTable();
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 获取表格数据
    async loadTable(flag) {
      if (!flag) {
        this.tableLoading = true;
      }
      let params = {
        parentId: this.parentId,
        ...this.searchForm,
        ...this.page,
      };
      await deptList(params)
        .then((res) => {
          let resData = res.data;
          resData.records.forEach((item) => {
            switch (item.status) {
              case 0:
                item.statusVal = "禁用";
                break;
              case 1:
                item.statusVal = "正常";
                break;
            }
          });
          this.tableData = resData.records;
          this.page.total = resData.total;

          this.tableLoading = false;
          this.loading = false;
        })
        .catch(() => {
          this.tableLoading = false;
          this.loading = false;
        });
    },

    // 查看人员
    handlePropleInfo(row) {
      this.$refs.personnelTableInfo.init(row);
    },

    // 更改每页显示条数
    handleSizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.loadTable();
    },

    // 选择页数/点击上一页/下一页
    handleCurrentPage(currentPage) {
      this.page.pageNum = currentPage;
      this.loadTable();
    },
  },
  created() {
    this.initLoad();
  },
};
</script>
<style lang="less" scoped>
.scroll_bar::-webkit-scrollbar {
  width: 5px;
  height: 2px;
  background: none;
  border-radius: 10px;
}
.scroll_bar::-webkit-scrollbar-thumb {
  width: 5px;
  background-color: #C1C1C1;
  border-radius: 10px;
}
.organizational_man {
  display: flex;
  height: 100%;
  .organizational_list {
    background: #eceff2;
    width: 300px;
    height: 100%;
    padding-right: 12px;
    box-sizing: border-box;
    .tree_list {
      height: 100%;
      border-radius: 4px;
      background: #fff;
      .title {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 50px;
      }
      .inp_box {
        padding: 0 10px 10px;
        margin: 0 auto;
      }
      .tree_box {
        height: calc(90vh - 92px);
        overflow-y: scroll;
      }
      /deep/.el-tree {
        background: none;
        height: 100%;

        // .el-tree-node:focus>.el-tree-node__content {
        //     background:none;
        // }
        // .el-tree-node__content:hover {
        //     background:none;
        // }
        .el-tree-node__content {
          height: 32px;
        }
        .is-current {
          > .el-tree-node__content {
            transition: all 0.15s ease;
            background: #bcd4ed;
            border-left: 2px solid #409eff;
            color: #2664d3;
          }
        }
      }
    }
  }

  .organizational_table {
    border-radius: 4px;
    // flex-grow:1;
    width: calc(100% - 300px);
    overflow-y: hidden;
    height: 100%;
    .company_info {
      height: 100%;
      .search_form {
        border-radius: 4px;
        padding: 0 12px;
        margin-top: 16px;
        margin-bottom: 0;
        box-sizing: border-box;
        .btn_box {
          padding-left: 10px;
          box-sizing: border-box;
          display: flex;

          .reset_btn {
            color: #666666;
          }
        }
      }

      .operation_btn {
        // display:flex;
        padding: 15px 12px 12px;
        box-sizing: border-box;
        .el_prop {
          margin-left: 10px;
        }
        .sync_btn {
          float: right;
        }
      }

      .table_box {
        height: calc(100% - 172px);
        overflow-y: scroll;
        .el-table {
          min-height: 620px;
          .handle_btn {
            display: flex;
            justify-content: space-around;
          }
        }
        .del {
          color: #e03030;
        }
      }
      .page_box {
        text-align: right;
      }
    }
  }
}
</style>
