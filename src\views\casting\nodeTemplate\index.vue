<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--用户数据-->
      <el-col :span="24" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
          <el-form-item label="搜索" prop="keyword">
            <el-input v-model="queryParams.keyword" placeholder="请输入" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
              @click="handleDelete">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="exportFile">导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" />
          <el-table-column label="序号" type="index" width="120" v-if="columns[0].visible" />
          <el-table-column label="编码" key="groupCode" prop="groupCode" v-if="columns[1].visible"
            :show-overflow-tooltip="true" />
          <el-table-column label="采购组" key="groupName" prop="groupName" v-if="columns[2].visible"
            :show-overflow-tooltip="true" />
          <el-table-column label="采购组类型" key="groupType" prop="groupType" align="center">
            <template slot-scope="scope">
              {{ ["普通", "铸件", "锻件"][scope.row.groupType] }}
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="enable">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.alarmStatus" :active-value="1" :inactive-value="0"
                @change="handleStatusChange(scope.row)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="节点数量" key="nodeNum" prop="nodeNum" v-if="columns[2].visible"
            :show-overflow-tooltip="true" />
          <el-table-column label="采购组总时间" key="days" prop="days" v-if="columns[2].visible"
            :show-overflow-tooltip="true" />
          <el-table-column label="备注" key="templateRemarks" prop="templateRemarks" v-if="columns[2].visible"
            :show-overflow-tooltip="true" />
          <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
            <template slot-scope="scope" v-if="scope.row.userId !== 1">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">修改</el-button>
              <el-button size="mini" type="text" icon="el-icon-view" @click="handleUpdate(scope.row)">查看</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.size"
          @pagination="getList" />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="400px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="采购组编号" prop="groupCode" style="width: 80%">
              <el-input v-model="form.groupCode" placeholder="请输入" maxlength="30" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="采购组名称" prop="groupCode" style="width: 80%">
              <el-input v-model="form.groupName" placeholder="请输入" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="备注" style="width: 90%">
              <el-input v-model="form.templateRemarks" type="textarea" resize="none" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
        :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
        :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 修改采购组类型对话框 -->
    <el-dialog :title="'修改采购组类型'" :visible.sync="editDialog" width="400px" append-to-body>
      <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="100px">
        <el-form-item label="采购组类型" prop="groupType">
          <el-select v-model="editForm.groupType" placeholder="请选择">
            <el-option label="普通" :value="0"></el-option>
            <el-option label="铸件" :value="1"></el-option>
            <el-option label="锻件" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="editDialog = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { updateUser, resetUserPwd } from "@/api/system/user";
import {
  selectPage,
  addSelectPage,
  deletes,
  exportData,
  exportExcelTemplate,
  updateAlarmStatus,
  updateGroupType,
} from "@/api/cast/nodeTemplate";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "User",
  // dicts: ["sys_normal_disable", "sys_user_sex"],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // app角色选项
      postOptions: [],
      // 后台角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { "X-Token": "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/back-server/nodeTemplate/excelUploadTemplate",
      },
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        keyword: undefined,
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `用户名称`, visible: true },
        { key: 2, label: `用户昵称`, visible: true },
        { key: 3, label: `部门`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建时间`, visible: true },
      ],
      // 表单校验
      rules: {
        groupCode: [
          { required: true, message: "采购组编号不能为空", trigger: "blur" },
        ],
        groupName: [
          { required: true, message: "采购组名称不能为空", trigger: "blur" },
        ],
      },
      sexs: [
        {
          label: "男",
          value: 1,
        },
        {
          label: "女",
          value: 0,
        },
      ],
      //状态
      studes: [
        {
          label: "正常",
          value: 1,
        },
        {
          label: "停用",
          value: 0,
        },
      ],
      editDialog: false, // 修改弹窗显示状态
      editForm: {
        cntId: undefined,
        groupType: undefined
      },
      editRules: {
        groupType: [
          { required: true, message: "请选择采购组类型", trigger: "change" }
        ]
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getList();
    // this.getConfigKey("sys.user.initPassword").then((response) => {
    //   this.initPassword = response.msg;
    // });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      selectPage(this.queryParams).then((response) => {
        this.userList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 采购组修改状态
    handleStatusChange(row) {
      let text = row.alarmStatus === 1 ? "开启" : "关闭";
      var modal = this.$modal
      this.$modal
        .confirm('确认要"' + text + '"报警吗？')
        .then(function () {
          updateAlarmStatus({ id: row.cntId, alarmStatus: row.alarmStatus }).then((res) => {
            modal.msgSuccess(text + "成功");
          });
        }).catch(function () {
          row.alarmStatus = row.alarmStatus === "0" ? "1" : "0";
        });
    },
    handleCruxStatusChange(row) {
      updateCruxStatus({ id: row.cntId, cruxStatus: row.cruxStatus }).then(res => {
        if (res.code == 1) {
          this.$modal.msgSuccess("修改成功");
        } else {
          this.$modal.msgError("修改失败");
          row.cruxStatus = row.cruxStatus === 1 ? 0 : 1;  // 切换回原来的状态
        }
      }).catch(() => {
        this.$modal.msgError("修改失败");
        row.cruxStatus = row.cruxStatus === 1 ? 0 : 1;  // 切换回原来的状态
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        groupCode: undefined,
        groupName: undefined,
        templateRemarks: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.cntId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleResetPwd":
          this.handleResetPwd(row);
          break;
        case "handleAuthRole":
          this.handleAuthRole(row);
          break;
        default:
          break;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.title = "新增";
      this.open = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$router.push({
        path: "/cast/cast/nodeTemplate/nodeTemplateDetail",
        query: {
          cntId: row.cntId,
        },
      });
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$modal
        .confirm('是否重置"' + row.username + '"的密码', "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          closeOnClickModal: false,
        })
        .then(() => {
          resetUserPwd(row.id).then((response) => {
            this.$modal.msgSuccess("重置成功");
          });
        })
        .catch(() => { });
    },
    /** 分配角色操作 */
    handleAuthRole: function (row) {
      const userId = row.userId;
      this.$router.push("/system/user-auth/role/" + userId);
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            updateUser(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSelectPage(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.cntId || this.ids;
      this.$modal
        .confirm('是否确认删除用户编号为"' + ids + '"的数据项？')
        .then(function () {
          return deletes(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    exportFile() {
      const _this = this;
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(function () {
        exportData({ ids: _this.ids, ..._this.queryParams }).then(
          (response) => {
            const url = window.URL.createObjectURL(new Blob([response]));
            const link = document.createElement("a");
            link.target = "_blank";
            link.href = url;
            link.setAttribute("download", "铸锻件节点信息.xlsx");
            document.body.appendChild(link);
            link.click();
          }
        );
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      exportExcelTemplate().then((response) => {
        const url = window.URL.createObjectURL(new Blob([response]));
        const link = document.createElement("a");
        link.target = "_blank";
        link.href = url;
        link.setAttribute("download", "铸锻件节点模板.xlsx");
        document.body.appendChild(link);
        link.click();
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 点击修改按钮
    handleEdit(row) {
      this.editForm.cntId = row.cntId
      this.editForm.groupType = row.groupType
      this.editDialog = true
    },
    // 提交修改
    submitEditForm() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          updateGroupType(this.editForm).then(res => {
            this.$modal.msgSuccess("修改成功")
            this.editDialog = false
            this.getList()
          })
        }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-input {
  width: 120%;
}
</style>
