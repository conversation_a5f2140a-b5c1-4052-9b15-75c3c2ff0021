<template>
  <div>
    <el-row :gutter="20">
      <!--用户数据-->
      <el-col :span="24" :xs="24">
        <el-button @click="back" size="small" class="back">返回</el-button>
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="搜索" prop="keyword">
            <el-input
              v-model="queryParams.keyword"
              placeholder="请输入"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              >新增</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="userList"
        >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="120"
          />
          <el-table-column
            align="center"
            label="节点名称"
            prop="nodeName"
          />
          <el-table-column
            align="center"
            label="节点时间"
            prop="nodeDays"
          />
          <el-table-column
            align="center"
            label="备注"
            prop="nodeRemarks"
          />
          <el-table-column
            align="center"
            label="操作"
            width="220"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                >修改</el-button
              >
              <el-popconfirm
                @confirm="handleDelete(scope.row)"
                :title="'是否确认删除节点名称为' + scope.row.nodeName + '的数据项？'"
              >
                <el-button
                    style="color:red"
                    slot="reference"
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    >删除</el-button
                >
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="400px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="节点名称" prop="nodeName" style="width: 80%">
              <el-input
                v-model="form.nodeName"
                placeholder="请输入"
                maxlength="30"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="节点天数" style="width: 80%">
              <el-input v-model="form.nodeDays" placeholder="请输入" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="排序" prop="nodeSort" style="width: 80%">
              <el-input v-model="form.nodeSort" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" style="width: 90%">
              <el-input
                v-model="form.nodeRemarks"
                type="textarea"
                resize="none"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { getDetailPage,addDetail,editDetail,deltDetailData } from "@/api/productionPlan/productionModules.js";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "User",
  props:{
    detailId:String,
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: '',
        amountId:'',
      },

      // 表单校验
      rules: {
        nodeName: [
          { required: true, message: "节点名称不能为空", trigger: "blur" },
        ],
        nodeDays: [
          { required: true, message: "节点时间不能为空", trigger: "blur" },
          { pattern: /^[1-9]\d*$/, message: "请输入数值" },
        ],
        nodeSort: [
          { required: true, message: "节点时间不能为空", trigger: "blur" },
          { pattern: /^[1-9]\d*$/, message: "请输入数值" },
        ],
      },
    };
  },
  created(){
    this.getList();
  },
  methods: {
    back(){
        this.$emit("resetQuery")
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      this.queryParams.amountId = this.detailId
      getDetailPage(this.queryParams).then((response) => {
        this.userList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        nodeDays: undefined,
        nodeName: undefined,
        nodeRemarks: undefined,
        nodeSort: undefined,
        amountId: '',
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.handleQuery();
    },
    // 更多操作触发
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.title = "新增";
      this.open = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = JSON.parse(JSON.stringify(row));
      this.open = true;
      this.title = "修改";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        this.form.amountId = this.detailId
        if (valid) {
          if (this.form.id != undefined) {
            editDetail(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDetail(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
        deltDetailData({id:row.id}).then(res=>{
            this.getList();
        })
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-input {
  width: 120%;
}
.back{
    float: right;
    margin-left: 10px;
    z-index: 1;
    position: relative;
}
</style>
