<template>
    <div v-loading="loading" class="pdf-content">
        <div class="wrap" ref="wrap">
            <div class="draw_code_content">
                <span class="draw_code">{{drawCode}}</span>
            </div>
            <div class="pdf-wrap" id="pdf-view-params-tab" :style="{'cursor': 'pointer', 'transform': `rotate(${rotate}deg)`,'width': `${scale}%`}"
            >
                <pdf
                :src="pdfSrc"
                ref="pdf"
                :page="currentPage"
                @num-pages="pageCount=$event"
                @page-loaded="currentPage=$event"
                @loaded="loadPdfHandler"
                ></pdf>
            </div>

            <!-- 操作栏 -->
            <div class="control-content">
                <div class="control">
                    <i
                    v-for="(item,index) in controlMenu"
                    :key="index"
                    @click="handle(item.index)"
                    :title="item.title"
                    :class="item.icon"
                    ></i>
                </div>
                <div class="page-content">{{currentPage}}/{{pageCount}}</div>
            </div>


            <div :class="{'toast':true,showToast,animate}">{{toastScale}}</div>
        </div>
    </div>
</template>

<script>
import pdf from "vue-pdf";
import { bind, unbind, objDrag } from "./js/util";
import {exportPDFUrl,exportFile} from '@/utils/gloabUtileExport.js'
export default {
    components:{pdf},
    data(){
        return {
            error:false,
            loading:false,
            params:null,
            drawCode:'',
            pdfSrc: null,
            initPosX: 0,
            initPosY: 0,
            currentPage: 0, // pdf文件页码
            pageCount: 0, // pdf文件总页数
            numPages: 1,
            activeIndex: 0,
            iWidth: document.documentElement.clientWidth,
            iHeight: document.documentElement.clientHeight,
            scale: 100,
            rotate: 0,
            scaleAdd: 10,
            rotateAdd: 90,
            showToast: false,
            animate: false,
            toastTimer1: null,
            toastTimer2: null,
            objDrag: null,
            controlMenu:[
                {icon:'el-icon-zoom-in',title:'放大',index:1},
                {icon:'el-icon-zoom-out',title:'缩小',index:2},
                {icon:'el-icon-c-scale-to-original',title:'还原',index:3},
                {icon:'el-icon-refresh-left',title:'向左旋转90°',index:4},
                {icon:'el-icon-refresh-right',title:'向右旋转90°',index:5},
                {icon:'el-icon-caret-left',title:'上一页',index:6},
                {icon:'el-icon-caret-right',title:'下一页',index:7},
            ]
        }
    },
    methods:{
        drag(obj) {
            this.objDrag = new objDrag(obj);
        },
        loadPdfHandler(e) {
            this.currentPage = 1; // 加载的时候先加载第一页
            this.drag("pdf-view-params-tab");
            this.initPos();
        },

        // 校验文件
        checkError(pdfSrc) {
            let loadingTask = pdf.createLoadingTask(pdfSrc);
            try{
                loadingTask.promise.catch(()=>{
                this.$message({
                    type:'error',
                    message:'文件预览错误！',
                    duration:2000
                })
                this.error = true
                })
            }catch(e){

            }

        },

        getFileUrl(){
            this.loading = true
            exportPDFUrl(`dms/draw/frock/file/pdf`,this.params).then((res)=>{
                this.pdfSrc = res
                this.checkError(this.pdfSrc)
                this.loading = false
            }).catch(()=>{
                this.loading = false
            })
        },

        showToastFn() {
            this.showToast = true;
            this.animate = false;
            clearTimeout(this.toastTimer1);
            clearTimeout(this.toastTimer2);
            this.toastTimer1 = setTimeout(() => {
                this.animate = true;
                this.toastTimer2 = setTimeout(() => {
                this.showToast = false;
                this.animate = false;
                }, 300);
            }, 300);
        },

        initPos() {
            let pdfWrap = document.getElementById("pdf-view-params-tab");
            this.iWidth = document.documentElement.clientWidth;
            this.iHeight = document.documentElement.clientHeight;
            this.initPosX = this.iWidth / 2;
            this.initPosY = this.iHeight / 2;
            this.scale = 100;
            this.rotate = 0;
            pdfWrap.style.left = 0 + "px";
            pdfWrap.style.top = 0 + 'px'
        },

        // 放大
        magnify() {
            let { scale } = this;
            if(scale>=500)return;
            scale += this.scaleAdd;
            // scale = scale > 500 ? 500 : scale;
            this.showToastFn();
            this.scale = scale;
        },

        // 缩小
        shrink() {
            let { scale } = this;
            if(scale<=10)return;
            scale -= this.scaleAdd;
            // scale = scale <= 10 ? 10 : scale;
            this.showToastFn();
            this.scale = scale;
        },

        changePdfPage(val) {
            if(val === 0){
                if(this.currentPage > 1){
                    this.currentPage--;
                }

            }else{
                if(this.currentPage < this.pageCount){
                    this.currentPage++;
                }
            }
        },

        handle(index) {
            let { scale, rotate } = this;
            switch (index) {
                case 1:
                this.magnify();
                break;
                case 2:
                this.shrink();
                break;
                case 3:
                this.initPos();
                break;
                case 4:
                this.rotate -= this.rotateAdd;
                break;
                case 5:
                this.rotate += this.rotateAdd;
                break;
                case 6:
                this.changePdfPage(0);
                break;
                case 7:
                this.changePdfPage(1);
                break;
            }
        },

    },
    computed: {
        toastScale() {
            let { scale } = this;
            return `${scale}%`;
        }
    },
    created(){
        this.params =  JSON.parse(decodeURIComponent(this.$route.query.params))
        this.drawCode = this.$route.query.drawCode
        this.getFileUrl()
    },
    mounted() {
        //图片加上拖拽
        bind(window, "resize", this.initPos);
    },
    beforeDestroy() {
        unbind(window, "resize", this.initPos);
    },
}
</script>
<style lang="less" scoped>
.pdf-content {
    height:100vh;
    width:100vw;
    overflow:hidden;
    .draw_code_content {
        padding:25px;
        position:relative;
        z-index:9999;
        box-sizing: border-box;
        .draw_code {
            font-size:25px;
            font-weight:500;
        }
    }
    .wrap {
        width: 100%;
        height: 100%;
        position: relative;
        .pdf-wrap {
            position: absolute;
        }
        .control-content {
            display:flex;
            width: 400px;
            align-items:center;
            position: absolute;
            left: 50%;
            margin-left: -150px;
            bottom: 20px;
            z-index:9999;
        }
        .page-content {
            width:89px;
            padding-left:10px;
            box-sizing: border-box;
            font-weight:bold;
        }
        .control {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex:1;
            padding: 10px 10px;
            box-sizing: border-box;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 25px;
            i {
                width: 20px;
                margin: 0 5px;
                height: 20px;
                font-size:20px;
                display:inline-block;
                cursor:pointer;
                background: no-repeat center center;
                background-size: 16px;
            }
        }
        .toast {
            width: 100px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            position: absolute;
            top: 30px;
            left: 50%;
            margin-left: -50px;
            z-index: 10;
            transition: opacity 0.3s ease-out;
            opacity: 1;
            display: none;
            &.showToast {
                display: block;
            }
            &.animate {
                opacity: 0;
            }
        }
    }
}
</style>
