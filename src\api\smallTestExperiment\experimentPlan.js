import request from '@/utils/request'
// 分页
export function getSmPlanPage(param) {
    return request({
        url: '/mes-server/sm/plan/app/page',
        method: 'get',
        params: param,
    })
}

// 新增
export function planOne(param) {
    return request({
        url: '/mes-server/sm/plan/one',
        method: 'post',
        data: param,
    })
}

// 修改
export function putSmPlan(param) {
    return request({
        url: '/mes-server/sm/plan',
        method: 'post',
        data: param,
    })
}

// 导入
export function planImport(query) {
    return request({
        url: '/mes-server/sm/plan/import',
        method: 'post',
        data: query,
    })
}

// 导入规范号
export function exportData(query) {
    return request({
        url: '/mes-server/sm/plan/import/excel',
        method: 'post',
        data: query,
        responseType: 'blob', // important
    })
}

// 批量新增
export function addSmPlanBatch(param) {
    return request({
        url: '/mes-server/sm/plan/batch',
        method: 'post',
        data: param,
    })
}

// htc设置qs
export function smPlanSetQs(param) {
    return request({
        url: `/mes-server/sm/plan/${param.planId}/htc/${param.qsId}`,
        method: 'post',
    })
}

// qc设置htc
export function smPlanSetHtc(param) {
    return request({
        url: `/mes-server/sm/plan/${param.planId}/qc/${param.htcId}`,
        method: 'post',
    })
}

// 详情
export function getSmPlanDetails(id) {
    return request({
        url: `/mes-server/sm/plan/app/details/${id}`,
        method: 'get',
    })
}

// htc审核qc
export function auditHtc(param) {
    return request({
        url: `/mes-server/sm/plan/htc`,
        method: 'post',
        data: param
    })
}


// 最后节点htc提交
export function submitLastHtc(param) {
    return request({
        url: `/mes-server/sm/plan/htc/last`,
        method: 'post',
        data: param
    })
}

// 最后节点管理员提交
export function submitLastManage(param) {
    return request({
        url: `/mes-server/sm/plan/manage/last`,
        method: 'post',
        data: param
    })
}

// 最后节点qc提交
export function submitLastQc(param) {
    return request({
        url: `/mes-server/sm/plan/qc/last`,
        method: 'post',
        data: param
    })
}

// 最后节点qs提交
export function submitLastQs(param) {
    return request({
        url: `/mes-server/sm/plan/qs/last`,
        method: 'post',
        data: param
    })
}

// QC提交节点数据
export function submitNodeQc(param) {
    return request({
        url: `/mes-server/sm/plan/qc`,
        method: 'post',
        data: param
    })
}

// 选择过程类型
export function putSmProcessType(param) {
    return request({
        url: `/mes-server/sm/plan/${param.planId}/processType/${param.processType}`,
        method: 'post',
        data: param,
    })
}

// 填写炉号
export function putSmHeatNo(param) {
    return request({
        url: `/mes-server/sm/plan/${param.planId}/heatNo/${param.heatNo}`,
        method: 'post',
        data: param
    })
}

// 基本信息填写
export function smPlanDetail(param) {
    return request({
        url: `/mes-server/sm/plan/detail`,
        method: 'post',
        data: param
    })
}



