import request from "@/utils/request";
// 分页
export function getBmPlanPage(param) {
  return request({
    url: "/mes-server/bm/plan/app/page",
    method: "get",
    params: param,
  });
}
// 分页
export function getBmPlanList(param) {
  return request({
    url: "/mes-server/bm/plan/app/list",
    method: "get",
    params: param,
  });
}

// 基本信息
export function bmPlanDetail(param) {
  return request({
    url: "/mes-server/bm/plan/detail",
    method: "post",
    data: param,
  });
}

// 导入
export function planImport(query) {
  return request({
    url: "/mes-server/bm/plan/import",
    method: "post",
    data: query,
  });
}

// 导入规范号
export function exportData(query) {
  return request({
    url: "/mes-server/bm/plan/import/excel",
    method: "post",
    data: query,
    responseType: "blob", // important
  });
}

// 新增
export function planOne(param) {
  return request({
    url: "/mes-server/bm/plan/one",
    method: "post",
    data: param,
  });
}

// 修改
export function putBmPlan(param) {
  return request({
    url: "/mes-server/bm/plan",
    method: "post",
    data: param,
  });
}
// 拆分
export function splitBmPlan(param) {
  return request({
    url: "/mes-server/bm/plan/split",
    method: "post",
    data: param,
  });
}

// 批量新增
export function addBmPlanBatch(param) {
  return request({
    url: "/mes-server/bm/plan/batch",
    method: "post",
    data: param,
  });
}

// htc设置qs
export function bmPlanSetQs(param) {
  return request({
    url: `/mes-server/bm/plan/${param.planId}/htc/${param.qsId}`,
    method: "post",
  });
}

// qc设置htc
export function bmPlanSetHtc(param) {
  return request({
    url: `/mes-server/bm/plan/${param.planId}/qc/${param.htcId}`,
    method: "post",
  });
}

// 详情
export function getBmPlanDetails(id) {
  return request({
    url: `/mes-server/bm/plan/app/details/${id}`,
    method: "get",
  });
}

// htc审核qc
export function auditHtc(param) {
  return request({
    url: `/mes-server/bm/plan/htc`,
    method: "post",
    data: param,
  });
}

// 最后节点htc提交
export function submitLastHtc(param) {
  return request({
    url: `/mes-server/bm/plan/htc/last`,
    method: "post",
    data: param,
  });
}

// 最后节点管理员提交
export function submitLastManage(param) {
  return request({
    url: `/mes-server/bm/plan/manage/last`,
    method: "post",
    data: param,
  });
}

// 最后节点qc提交
export function submitLastQc(param) {
  return request({
    url: `/mes-server/bm/plan/qc/last`,
    method: "post",
    data: param,
  });
}

// 最后节点qs提交
export function submitLastQs(param) {
  return request({
    url: `/mes-server/bm/plan/qs/last`,
    method: "post",
    data: param,
  });
}

// QC提交节点数据
export function submitNodeQc(param) {
  return request({
    url: `/mes-server/bm/plan/qc`,
    method: "post",
    data: param,
  });
}

// 选择过程类型
export function putBmProcessType(param) {
  return request({
    url: `/mes-server/bm/plan/${param.planId}/processType/${param.processType}`,
    method: "post",
    data: param,
  });
}

// 填写炉号
export function putBmHeatNo(param) {
  return request({
    url: `/mes-server/bm/plan/${param.planId}/heatNo/${param.heatNo}`,
    method: "post",
    data: param,
  });
}
