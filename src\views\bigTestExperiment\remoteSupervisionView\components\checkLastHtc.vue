<template>
  <div>
    <el-dialog
      title="确认通过"
      :visible.sync="dialogVisible"
      append-to-body
      width="600px"
      @close="cancel"
    >
    <el-form ref="form" :model="form" :rules="rules">
            <el-form-item
            label="NCR编码"
            >
                <el-input
                style="width: 80%"
                v-model="form.htcNcr"
                placeholder="请输入"
                ></el-input>
            </el-form-item>

            <el-form-item
            label="补充文件"
            >
                <upload-file
                :show-file-list="true"
                :file-list="fileList"
                multiple
                @file-success="fileSuccess"
                @before-upload="beforeUpload"
                @before-remove="beforeRemove"
                @file-error="fileError"
                >
                    <el-button type="primary" size="small">上传文件</el-button>
                </upload-file>
            </el-form-item>

            <el-form-item
            prop="id"
            label="QS"
            >
              <el-select class="qs-select" v-model="form.id" filterable placeholder="请选择" :disabled="true">
                <el-option
                  v-for="item in userList"
                  :key="item.name"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
        </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="confirm" :loading="btnLoading" plain size="small" icon="el-icon-success">上述R点文件已审阅，H/W点已见证</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getHtcUserList } from "@/api/smallTestExperiment/personnel.js";
import { submitLastQs,submitLastHtc,bmPlanSetQs } from "@/api/bigTestExperiment/remoteSupervision.js";
import uploadFile from '@/components/uploadFile/main'
export default {
  components:{uploadFile},
  data() {
    return {
      personShow: false,
      title: "",
      formLabelWidth: "120px",
      btnLoading: false,
      dialogVisible: false,
      userList: [],
      fileList:[],
      userId:null,
      form: {
        id: '',
        htcNcr:'',
        fileList:[]
      },
      rowData: {},
      htcStatus: null,
      rules:{
        id:{ required: true, message: '请输入用户名', trigger: 'blur' },   
      }
    };
  },
  methods: {

    //初始化
    init(val, htcStatus) {
      this.dialogVisible = true;
      this.btnLoading = false
      this.rowData = val;
      this.htcStatus = htcStatus;
      this.userId = JSON.parse(localStorage.getItem('userInfo')).appUser.id
      this.form.id = String(this.userId);
      this.form.fileList = []
      this.form.htcNcr = ''
      this.fileList = []
      this.getHtcUserList();
    },

    // 
    getHtcUserList() {
      getHtcUserList().then((res) => {
        this.userList = res.data;
      }).catch(() => {});
    },

    // 最后节点HTC提交
    submitLastHtc(){
      let params = {
        htcStatus:1,
        id:this.rowData.id,
        nodeId:this.rowData.nodeId,
        planId:this.rowData.planId,
        fileList:this.form.fileList,
        htcNcr:this.form.htcNcr
      }
      submitLastHtc(params).then((res)=>{
        this.submitLastQs()
      }).catch(()=>{
        this.btnLoading = false;
      })
    },

    // 最后节点QS提交
    submitLastQs(){
      let params = {
        qsStatus:1,
        id:this.rowData.id,
        nodeId:this.rowData.nodeId,
        planId:this.rowData.planId
      }
      submitLastQs(params).then((res)=>{
        this.$emit('getDetail')
        this.btnLoading = false
        this.dialogVisible = false
      }).catch(()=>{
        this.btnLoading = false;
      })
    },
    

    // 确定
    confirm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          bmPlanSetQs({
            planId: this.rowData.planId,
            qsId: this.form.id,
          }).then((res) => {
              this.submitLastHtc()
          }).catch(() => {
              this.btnLoading = false;
          });
        }
      });
    },

    // 上传文件前
    beforeUpload(file,callBack){
        this.btnLoading = true
    },

    // 上传成功
    fileSuccess(response,file){
        this.form.fileList.push(response.data)
        this.btnLoading = false
    },

    // 文件上传失败
    fileError(error){
        this.btnLoading = false
    },

    // 删除文件前
    beforeRemove(file,fileList){
        let id = file.response.data.id
        let index = this.form.fileList.findIndex((item)=>{
            return item.id === id
        })
        this.form.fileList.splice(index,1)
    },

    cancel(){
      this.$refs.form.clearValidate();
      this.btnLoading = false
      this.dialogVisible = false
    }
  },
};
</script>
<style scoped lang="scss">
.btn-box {
  margin-bottom: 8px;
  padding-left: 36px;
  display: flex;
  align-items: center;
  .btn-label {
    width: 85px;
  }
}
::v-deep.qs-select {
  color:#333 !important;
  .el-input{
    .el-input__inner {
      color:#333 !important;
    }
  } 
}
</style>
