import request from '@/utils/request'
// 人员列表
export function getDutyUserPage(param) {
  return request({
    url: '/mes-server/duty/user/page',
    method: 'get',
    params: param,
  })
}

// 新增
export function addDutyUser(param) {
  return request({
    url: '/mes-server/duty/user',
    method: 'post',
    data: param,
  })
}

// 删除
export function delDutyUser(param) {
  return request({
    url: '/mes-server/duty/user',
    method: 'delete',
    data: param,
  })
}

// 修改部门领导
export function leaderUser(param) {
  return request({
    url: '/mes-server/duty/user/leader/user',
    method: 'put',
    data: param,
  })
}

// 修改部门
export function deptName(param) {
  return request({
    url: '/mes-server/duty/user/dept/name',
    method: 'put',
    data: param,
  })
}