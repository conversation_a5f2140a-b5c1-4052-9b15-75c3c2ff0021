<template>
  <div class="app-container">
    <el-form :model="searchForm" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="状态">
        <el-select v-model="searchForm.alarmStatus" clearable placeholder="请选择">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="搜索">
        <el-input style="width:480px" v-model="searchForm.query" placeholder="机组名称/令号/部套名称/部套编码/图号/零件名称" clearable
          @keyup.enter.native="search(false)" />
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="search(false)">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="search(true)">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-upload2" size="mini" plain @click="handleDowload">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-upload class="margin-left" accept=".xlsx,.xls" action="#" :show-file-list="false"
          :http-request="fileSuccess" :before-upload="beforeUpload" :file-error="fileError">
          <el-button type="warning" icon="el-icon-download" size="mini" plain>导入</el-button>
        </el-upload>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="search"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column label="序号" type="index" />
      <el-table-column label="零件名称" prop="bismtName" />
      <el-table-column label="图号" prop="bismt" />
      <el-table-column label="当前节点" prop="nodeName" />
      <el-table-column label="记录数" prop="nodeRecordNum" />
      <el-table-column label="计划时间" prop="planDate" width="">
        <template slot-scope="{row}">
          <div v-if="row.planDate">{{ row.planDate | dateFormat }}</div>
        </template>
      </el-table-column>
      <el-table-column label="部套名称" prop="dwgName" width="200px" />
      <el-table-column label="部套编码" prop="dwgNo" />
      <el-table-column label="机组名称" prop="post1"></el-table-column>

      <el-table-column label="令号" prop="posid" />
      <el-table-column label="完成状态" prop="alarmStatus">
        <template slot-scope="{row}">
          <el-tag v-if="String(row.alarmStatus)" :type="row.alarmStatus | filterStatus(statusOptions, 'type')">
            {{ row.alarmStatus | filterStatus(statusOptions, 'label') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="180">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="hanldeInfo(row)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleDate(row)">修改节点</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="searchForm.pageNum" :limit.sync="searchForm.pageSize"
      @pagination="search(false)" />

    <plan-info ref="planInfo"></plan-info>
    <!-- 修改时间 -->
    <modify-date ref="modifyDate" @loadData="loadData"></modify-date>
  </div>
</template>

<script>
import uploadFile from '@/components/uploadFile/main'
import planInfo from './components/planInfo'
import modifyDate from './components/modifyDate'
import { supplierList, detailUploadFile, generate } from '@/api/suppilerProductionPlan/productionPlan'
import { exportFile } from '@/utils/gloabUtile'
export default {
  components: { uploadFile, planInfo, modifyDate },

  data() {
    return {
      showSearch: true,
      loading: false,
      searchForm: {
        query: '',
        alarmStatus: '',
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      statusOptions: [
        {
          label: '报警',
          value: 1,
          type: 'danger'
        },
        {
          label: '预警',
          value: 2,
          type: 'warning'
        },
        {
          label: '正常',
          value: 3,
          type: 'default'
        },
        {
          label: '完成',
          value: 4,
          type: 'success'
        }
      ],
      tableData: []
    }
  },

  created() {
    this.loadData()
  },

  filters: {
    filterStatus(value, statusOptions, params) {
      let option = statusOptions.find((item) => {
        return item.value === value
      })
      return option[params]
    }
  },

  methods: {
    handleAddChild(row) {
      generate(row.id).then((res) => {
        this.$message({
          type: 'success',
          message: '操作成功',
          duration: 1500
        })
        this.loadData()
      })
    },
    handleDowload() {
      exportFile('/mes-server/part/detail/export/supplier', this.searchForm)
    },

    search(flag) {
      if (flag) {
        this.searchForm.pageNum = 1
        this.searchForm.query = ''
      }
      this.loadData()
    },

    hanldeInfo(row) {
      this.$refs.planInfo.init(row)
    },
    handleDate(row) {
      this.$refs.modifyDate.init(row)
    },
    // 上传文件前
    beforeUpload(file, callBack) {
      this.loading = true
    },

    // 上传成功
    fileSuccess(response, file) {
      let formData = new FormData()
      formData.append('file', response.file)
      detailUploadFile(formData).then((res) => {
        this.$message({
          type: 'success',
          message: '操作成功',
          duration: 1500
        })
        this.loadData()
      }).catch(() => {
        this.loading = false
      })

    },

    // 文件上传失败
    fileError(error) {
      this.loading = false
    },

    loadData() {
      this.loading = true
      let params = {
        ...this.searchForm
      }
      supplierList(params).then((res) => {
        let resData = res.data || {}
        this.tableData = resData.records || []
        this.total = resData.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

  },

}

</script>

<style scoped lang='scss'></style>