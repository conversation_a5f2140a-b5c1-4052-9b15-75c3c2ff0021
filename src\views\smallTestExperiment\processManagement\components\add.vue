<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogFormVisible"
      append-to-body
      width="980px"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <el-row>
          <el-col :span="12">
            <el-form-item style="width: 460px" label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item style="width: 460px" label="产品编号" prop="productCode">
              <el-input v-model="form.productCode" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider></el-divider>
      </el-form>

      <div class="add-box">
        <el-button type="primary" @click="add()">新增试验项</el-button>
      </div>

      <div class="table-title">
        <div class="title">检验和试验项目</div>
        <div class="explain">
          (控制点说明：R——文件见证点、W——现场见证点、H——停工待检点、I——第三方检验点、V——影像记录点)
        </div>
      </div>

      <el-table
        :data="form.modelList"
        row-key="pkId"
        :tree-props="{ children: 'modelList' }"
      >
        <el-table-column label="序号">
          <template slot-scope="scope">
            {{
              scope.row.parentId == "0"
                ? scope.row.sortIndex + 1
                : scope.row.sortLetter
            }}
          </template>
        </el-table-column>
        <el-table-column label="检验和试验项目" prop="projectName" />
        <el-table-column label="QC" prop="qcControl" />
        <el-table-column label="HTC" prop="htcControl" />
        <el-table-column label="QS" prop="qsControl" />
        <el-table-column label="检验要求及注意事项" prop="tip" />
        <el-table-column
          width="160"
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.parentId == '0'"
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="add(scope.row)"
            ></el-button>
            <!-- <el-button size="mini" type="text" icon="el-icon-top"></el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-bottom"
            ></el-button> -->
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="delOption(scope.row)"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="dialogFormVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <add-option ref="addOption"></add-option>
  </div>
</template>
<script>
import {
  addProduct,
  putProduct,
} from "@/api/smallTestExperiment/processManagement.js";
import addOption from "./addOption.vue";
export default {
  components: {
    addOption,
  },
  data() {
    return {
      personShow: false,
      title: "新增",
      formLabelWidth: "120px",
      btnLoading: false,
      dialogFormVisible: false,
      list: [],
      form: {
        modelList: [],
        productCode:'',
        productName:''
      },
      // 表单校验
      rules: {
        productName: [
          { required: true, message: "产品名称不能为空", trigger: "blur" },
        ],
        productCode: [
          { required: true, message: "产品编号不能为空", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    //重置
    rest() {},
    //初始化
    init(row) {
      this.list = [];
      for (let index = 0; index < 26; index++) {
        // 获取26个字母
        this.list.push(String.fromCharCode(index + 97));
      }
      this.dialogFormVisible = true;
      if (row) {
        this.title = "修改";
        this.form = JSON.parse(JSON.stringify(row));
        this.getSort();
      } else {
        this.title = "新增";
        this.form = {
          modelList: [],
          productCode:'',
          productName:''
        };
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    add(row) {
      this.$refs.addOption.init(row);
    },

    getSort() {
      this.form.modelList.forEach((item, index) => {
        item.sortIndex = index;
        item.pkId = "A" + index;
        if (item.modelList) {
          item.modelList.forEach((children, chilIndex) => {
            children.sortIndex = chilIndex;
            children.pkId = "a" + chilIndex + "-" + index;
            children.sortLetter = this.list[chilIndex];
          });
        }
      });
    },

    addBase(data) {
      this.form.modelList.push({
        ...data,
        modelList: [],
        sortIndex: this.form.modelList.length,
        parentId: "0",
        pkId: "A" + this.form.modelList.length,
      });
    },

    addbranch(data, parentIndex) {
      this.form.modelList[parentIndex].modelList.push({
        ...data,
        projectName:
          // this.list[this.form.modelList[parentIndex].modelList.length] +
          // "." +
          data.projectName,
        modelList: [],
        sortIndex: this.form.modelList[parentIndex].modelList.length,
        sortLetter:
          this.list[this.form.modelList[parentIndex].modelList.length],
        parentIndex: parentIndex,
        parentId: "1",
        pkId:
          "a" +
          this.form.modelList[parentIndex].modelList.length +
          "-" +
          parentIndex,
      });
    },

    delOption(val) {
      if (val.parentId == "0") {
        this.form.modelList.splice(val.sortIndex, 1);
      } else {
        this.form.modelList.forEach((item) => {
          let index = item.modelList.findIndex((i) => i.pkId == val.pkId);
          if (index > -1) {
            item.modelList.splice(index, 1);
          }
        });
      }
      this.getSort();
    },

    // 确定
    confirm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          if (this.title == "新增") {
            addProduct(this.form)
              .then((res) => {
                this.$modal.msgSuccess("新增成功");
                this.btnLoading = false;
                this.dialogFormVisible = false;
                this.$parent.getList();
              })
              .catch(() => {
                this.btnLoading = false;
              });
          } else {
            putProduct(this.form)
              .then((res) => {
                this.$modal.msgSuccess("修改成功");
                this.btnLoading = false;
                this.dialogFormVisible = false;
                this.$parent.getList();
              })
              .catch(() => {
                this.btnLoading = false;
              });
          }
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.btn-box {
  margin-bottom: 8px;
  padding-left: 36px;
  display: flex;
  align-items: center;
  .btn-label {
    width: 85px;
  }
}
.add-box {
  text-align: end;
  margin-bottom: 20px;
}
.table-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  .title {
    color: #409eff;
    font-size: 20px;
  }
  .explain {
    font-size: 12px;
  }
}
</style>
