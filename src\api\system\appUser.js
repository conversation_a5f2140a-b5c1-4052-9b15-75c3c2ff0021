import request from '@/utils/request'
// app角色已授权列表
export function getUserListByAppRoleList(param) {
  return request({
    url: '/user-server/appUser/getUserListByAppRoleId',
    method: 'get',
    params: param,
  })
}
// app角色未授权列表
export function getUserListByAppRoleUnList(param) {
  return request({
    url: '/user-server/appUser/getUserListByAppRoleIdUn',
    method: 'get',
    params: param,
  })
}
// app角色添加用户
export function addAppAuth(roleId,appUserIds) {
  return request({
    url: `/user-server/appRole/addUserAuth?roleId=${roleId}&appUserIds=${appUserIds}`,
    method: 'get',
  })
}
// app角色取消授权
export function cancelAppAuth(roleId,appUserIds) {
  return request({
    url: `/user-server/appRole/cancelAuth?roleId=${roleId}&appUserIds=${appUserIds}`,
    method: 'get',
  })
}

// 后台角色修改用户状态
export function updateStatus(param) {
  return request({
    url: '/user-server/appUser/updateStatus',
    method: 'get',
    params: param,
  })
}




// 后台角色已授权列表
export function getUserListByBackRoleList(param) {
  return request({
    url: '/user-server/appUser/getUserListByBackRoleId',
    method: 'get',
    params: param,
  })
}
// 后台角色未授权列表
export function getUserListByBackRoleUnList(param) {
  return request({
    url: '/user-server/appUser/getUserListByBackRoleIdUn',
    method: 'get',
    params: param,
  })
}
// 后台角色添加用户
export function addUserAuth(roleId,appUserIds) {
  return request({
    url: `/user-server/backRole/addUserAuth?roleId=${roleId}&appUserIds=${appUserIds}`,
    method: 'get',
  })
}
// 后台角色取消授权
export function cancelAuth(param) {
  return request({
    url: '/user-server/backRole/cancelAuth',
    method: 'get',
    params: param,
  })
}