<template>
  <div class="iframe-content" v-loading="loading">
    <iframe ref="iframe" id="viewer" width="100%" height="100%" :src="pdfSrc+'#toolbar=0'" frameborder="0"></iframe>
    <div class="mask"></div>
  </div>
</template>

<script>
import { getFileUrl } from "@/api/publicReq.js";
import pdf from "vue-pdf";
export default {
  components:{},

  data(){
    return{
      fileId:'',
      drawCode:'',
      pdfSrc:'',
      loading:false
    }
  },

  created(){
    this.fileId = this.$route.query.fileId;
    this.drawCode = this.$route.query.drawCode || '';
    document.title = this.drawCode
    this.getFileUrl();
  },

  mounted(){
    this.$nextTick(()=>{
      function clickIE() { 
			 if (document.all) { 
			  return false; 
			 } 
			} 
			function clickNS(e) { 
			 if (document.layers || (document.getElementById && !document.all)) { 
			  if (e.which == 2 || e.which == 3) { 
			   return false; 
			  } 
			 } 
			} 
			if (document.layers) { 
			 document.captureEvents(Event.MOUSEDOWN); 
			 document.onmousedown = clickNS;
			} 
			else { 
			 document.onmouseup = clickNS; 
			 document.oncontextmenu = clickIE; 
			} 
			document.oncontextmenu = new Function("return false") 
    })
  },

  methods:{

    getFileUrl() {
      this.loading = true;
      getFileUrl(this.fileId)
        .then((res) => {
          this.pdfSrc = res.data;
          this.checkError(this.pdfSrc);
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    checkError(pdfSrc) {
      let loadingTask = pdf.createLoadingTask(pdfSrc);
      try {
        loadingTask.promise.catch((error) => {
          this.$message({
            type: "error",
            message: `文件预览错误！`,
            duration: 2000,
          });
          this.error = true;
        });
      } catch (e) {}
    },
  },

}

</script>

<style scoped lang='less'>
.iframe-content {
  position:relative;
  height:100vh;
  width:100vw;
}
.mask {
  position:absolute;
  top:0;
  left:0;
  z-index:1;
  width:calc(100vw - 15px);
  height:calc(100vh - 15px);
}
</style>