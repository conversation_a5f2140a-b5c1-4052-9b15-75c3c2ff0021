<template>
  <div class="file-upload">
    <el-upload 
      class="upload-demo"
      :action="uploadFileUrl"
      :headers="headers"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :file-list="fileList"
      :disabled="disabled"
      :multiple="multiple"
      :accept="accept"
      :limit="limit">
      <el-button size="small" type="primary" :disabled="disabled">
        {{ buttonText }}
      </el-button>
      <div class="tips" v-if="tips">
        {{ tips }}
      </div>
    </el-upload>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: 'FileUpload',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    accept: {
      type: String,
      default: '*'
    },
    limit: {
      type: Number,
      default: 9
    },
    buttonText: {
      type: String,
      default: '添加文件'
    },
    tips: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/user-server/file/uploadFile",
      headers: {
        "X-Token": "Bearer " + getToken()
      },
      fileList: [],
      fileIds: []
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.fileList = newVal;
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
        return false
      }
      return true
    },
    handleSuccess(response) {
      if (response.code === 200) {
        this.fileIds.push(response.data.id);
        this.$emit('input', this.fileList);
        this.$emit('update:fileIds', this.fileIds);
        this.$message.success("上传成功");
      } else {
        this.$message.error(response.msg);
      }
    },
    handleError() {
      this.$message.error('上传失败');
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
      // 从fileIds中移除对应的id
      const index = this.fileIds.indexOf(file.id);
      if (index > -1) {
        this.fileIds.splice(index, 1);
      }
      this.$emit('input', fileList);
      this.$emit('update:fileIds', this.fileIds);
    },
    handlePreview(file) {
      window.open(file.url);
    }
  }
}
</script>

<style lang="scss" scoped>
.file-upload {
  .tips {
    color: #909399;
    font-size: 12px;
    margin-top: 5px;
  }
}
</style>
