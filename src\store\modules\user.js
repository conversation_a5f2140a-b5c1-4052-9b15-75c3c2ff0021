import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import md5 from 'js-md5'
const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    userType: null
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },

    SET_USERTYPE: (state, userType) => {
      state.userType = userType
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = md5(userInfo.password)
      const verifyCode = userInfo.verifyCode
      const securityCode = userInfo.securityCode
      return new Promise((resolve, reject) => {
        login(username, password, verifyCode, securityCode).then(res => {
          let resData = res.data
          setToken(res.data.token)
          let userInfo = { appUser: resData.appUser, userType: resData.userType, updatePasswordStatus: resData.updatePasswordStatus }
          localStorage.setItem('userInfo', JSON.stringify(userInfo))
          commit('SET_TOKEN', res.data.token)
          commit('SET_USERTYPE', res.data.userType)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        let userInfo = JSON.parse(localStorage.getItem('userInfo'))
        getInfo(userInfo.userType).then(res => {
          const user = res.data
          const avatar = (user.imageUrl == "" || user.imageUrl == null) ? require("@/assets/images/profile.jpg") : process.env.VUE_APP_IMAGE_API + user.imageUrl;
          if (res.backMenuList && res.backMenuList.length > 0) { // 验证返回的backMenuList是否是一个非空数组
            commit('SET_ROLES', res.backMenuList)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', user.name)
          commit('SET_AVATAR', avatar)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          localStorage.clear()
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
