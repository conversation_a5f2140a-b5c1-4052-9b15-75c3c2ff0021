<template>
  <el-dialog
    title="查看"
    :visible.sync="dialogVisible"
    @close="cancel"
    width="40%"
  >
    <div class="container">
      <div class="def-row">
        <p>部套名称：{{ orderInfo.name }}</p>
        <p>部套编码：{{ orderInfo.partCode }}</p>
      </div>

      <div class="def-row">
        <p>技术准备日期：{{ orderInfo.ytMonth }}天</p>
        <p>定标周期：{{ orderInfo.bidMonth }}天</p>
      </div>

      <div class="def-row">
        <p>采购周期：{{ orderInfo.produceCycleMonth }}天</p>
        <p>生产周期：{{ orderInfo.produceMonth }}天</p>
      </div>

    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      orderInfo: {},
    };
  },
  computed: {
    contNum() {
      let orderInfo = this.orderInfo;
      let num =
        (orderInfo.bidMonth || 0) + 
        (orderInfo.produceCycleMonth || 0)+
        (orderInfo.produceMonth || 0) +
        (orderInfo.dressMonth || 0);
      return num;
    },
  },
  methods: {
    init(row) {
      this.dialogVisible = true;
      this.orderInfo = row;
    },
    cancel() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style scoped>
.title {
  color: #409eff;
  font-size: 15px;
}
.def-row {
  display: flex;
  margin: 10px 0;
}
.def-row > p {
  width: 50%;
  font-size: 14px;
  text-align: left;
}
.big-size {
  font-size: 18px;
  font-weight: 500;
}
</style>
