<template>
    <el-dialog
    title="关联厂商"
    :visible.sync="dialogVisible"
    width="30%"
    @close="cancel">
        <el-form :model="form" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm">
            <el-form-item label="原厂商">
                <el-input v-model="supplierName" :disabled="true"></el-input>
            </el-form-item>
            <el-form-item label="新厂商编号" prop="supplierNo">
                <el-input v-model="form.supplierNo"></el-input>
            </el-form-item>
            <el-form-item label="新厂商名称" prop="supplierName">
                <el-input v-model="form.supplierName"></el-input>
            </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { partDetailSupplier } from '@/api/productionPlan/productionPlan'
export default {
  components:{},

  data(){
    return{
        dialogVisible:false,
        btnLoading:false,
        form:{
            supplierName:'',
            supplierNo:''
        },
        rowData:{},
        rules:{
            supplierNo: [
                { required: true, message: '请输入新厂商编号', trigger: 'blur' }
            ],
            supplierName: [
                { required: true, message: '请输入新厂商名称', trigger: 'blur' }
            ]
        },
        supplierName:'',
    }
  },

  methods:{
    init(row){
        this.dialogVisible = true
        this.rowData = row
        this.supplierName = row.supplierName + '(' + row.supplierNo + ')'
        this.form.supplierName = ''
        this.form.supplierNo = ''
        this.btnLoading = false
    },

    confirm(){
        this.$refs['form'].validate((valid) => {
            if(valid){
                let params = {
                    ...this.form,
                    partId:this.rowData.id

                }
                this.btnLoading = true
                partDetailSupplier(params).then((res)=>{
                    this.$message({
                        type:'success',
                        message:'操作成功',
                        duration:1500
                    })
                    this.$emit('loadData')
                    this.btnLoading = false
                    this.dialogVisible = false
                }).catch(()=>{
                    this.btnLoading = false
                })
            }
        })
    },

    cancel(){
        this.$refs['form'].resetFields()
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped lang='scss'>
</style>
