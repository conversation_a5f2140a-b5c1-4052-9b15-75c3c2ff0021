<template>
  <!-- 系统管理-组织架构-客户管理 -->
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="包名"
        class="filter-item filter-item-wrapper"/>
      <el-button type="primary" icon="el-icon-search" class="filter-item" @click="handleSearch">搜索</el-button>
      <el-button type="primary" icon="el-icon-plus" class="filter-item" @click="handleAdd">添加</el-button>
      <el-button type="danger" class="filter-item" @click="handleDelete">删除</el-button>
    </div>
    <!-- 编辑弹窗 -->
    <el-dialog :visible.sync="dialogFormVisible" :title="title" width="30%" center>
      <el-form ref="formData" :model="formData" :rules="rules">

        <el-form-item :label-width="formLabelWidth" label="安装包路径" prop="">
          <el-input v-model="formData.url" autocomplete="off"/>
        </el-form-item>
        <el-form-item :label-width="formLabelWidth" label="版本名称" prop="">
          <el-input v-model="formData.versionNum" autocomplete="off"/>
        </el-form-item>
        <el-form-item :label-width="formLabelWidth" label="版本号" prop="">
          <el-input v-model="formData.versionCode" oninput="value=value.replace(/[^\d]/g,'')" autocomplete="off"/>
        </el-form-item>
        <el-form-item :label-width="formLabelWidth" label="是否强制更新" prop="">
          <!--          <el-input v-model="formData.forceUpdate" autocomplete="off"/>-->
          <el-select v-model="formData.forceUpdate" default-first-option placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label-width="formLabelWidth" label="版本类型" prop="">
          <!--          <el-input v-model="formData.type" autocomplete="off"/>-->
          <el-select v-model="formData.type" default-first-option placeholder="请选择">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label-width="formLabelWidth" label="更新说明" prop="">
          <el-input v-model="formData.updateInfo" type="textarea" :rows="3" autocomplete="off"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm()">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 表格 -->
    <el-table
      ref="multipleTable"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @selection-change="changeFun"
    >
      <el-table-column type="selection" width="40" @selection-change="changeFun"/>
      <!-- ID字段，必须使用，故隐藏 -->
      <el-table-column label="序号" :min-width="40" prop="userId" fixed>
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="false" label="模块ID" :min-width="100" prop="userId" fixed>
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="安装包路径" :min-width="100" prop="creator">
        <template slot-scope="scope">
          <span>{{ scope.row.url }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新说明" :min-width="100" prop="creator">
        <template slot-scope="scope">
          <span>{{ scope.row.updateInfo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="版本名称" :min-width="80" prop="name">
        <template slot-scope="scope">
          <span>{{ scope.row.versionNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="版本号" :min-width="100" prop="imToken">
        <template slot-scope="scope">
          <span>{{ scope.row.versionCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="版本类型" :min-width="100" prop="address">
        <template slot-scope="scope">
          <span>{{ scope.row.type == 0 ? 'IOS' : 'Android' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" :min-width="100" prop="tel">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否强制更新" :min-width="100" prop="imToken">
        <template slot-scope="scope">
          <span>{{ scope.row.forceUpdate == 1 ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否启用" :min-width="80" prop="code">
        <template slot-scope="scope">
          <!--          <el-switch v-model="scope.row.enable" active-value="0" inactive-value="1" @change="enableGtjFile(scope)"/>-->
          <el-switch
            v-model="scope.row.enable"
            :disabled="scope.row.enable===1"
            :active-value="1"
            :inactive-value="0"
            @change="enableVersion(scope)"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit"  @click="handleUpdateRow(scope)">编辑
                    </el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="deleteVersion(scope)">删除
                    </el-button>
          <!-- <el-button type="primary" size="mini" @click="handleUpdateRow(scope)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteVersion(scope)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.size"
        align="center"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import {
  getList,
  addVersion,
  deleteVersion,
  deletesVersion,
  updateVersion,
  enableVersion
} from '../../../api/mating/versionUpdating'
import Pagination from '@/components/Pagination'
// import { notify_success, notify_failure, confirm, pop_error } from '@/utils/popup'

export default {
  // name: 'gtjFile',
  components: { Pagination },
  data() {
    const validateName = (rule, value, callback) => { // 自定义验证
      if (!value) {
        callback(new Error('必填'))
      }
      callback()
    }
    return {
      tableKey: 0,
      total: 0,
      index: 0,
      roleTableData: null,
      dialogExcelUpload: false,
      list: null,
      options: [{ 'value': 0, 'label': '否' }, { 'value': 1, 'label': '是' }],
      typeOptions: [{ 'value': 0, 'label': 'IOS' }, { 'value': 1, 'label': 'Android' }],
      listQuery: {
        page: 1, // 当前页码
        size: 10, // 每页显示条数
        fileId: '', // 每页显示条数
        keyword: ''// 搜索关键字
      },
      listLoading: false,
      dialogFormVisible: false, // 编辑弹出框
      updateBoolean: true, // 编辑弹出框
      multipleSelection: [],
      backgourdfileList: [],
      enable: 0,
      formData: {
        id: '', // 文件ID
        updateInfo: '', // 路径
        versionNum: '', // 文件名
        createTime: '', // 上传时间
        enable: '', // MD5值
        forceUpdate: '', // 上传人
        url: '', // 上传人
        type: '', // 是否删除
        versionCode: '' // 删除时间
      },
      rules: {
        name: [{ required: true, validator: validateName, trigger: ['blur', 'change'] }],
        // name: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        phone: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        department: [{ required: true, message: '必填', trigger: ['blur', 'change'] }],
        loginNum: [{ required: true, message: '必填', trigger: ['blur', 'change'] }]
      },
      formLabelWidth: '100px',
      title: ''
    }
  },
  created() { //  钩子函数 初始化数据
    this.getList()
  },
  methods: {

    handleUpdateRow(scope) { // 修改
      this.index = scope.$index
      this.title = '编辑'
      this.dialogFormVisible = true
      this.updateBoolean = false
      this.formData.id = scope.row.id
      this.formData.updateInfo = scope.row.updateInfo
      this.formData.versionNum = scope.row.versionNum
      this.formData.createTime = scope.row.createTime
      this.formData.enable = scope.row.enable
      this.formData.forceUpdate = scope.row.forceUpdate
      this.formData.url = scope.row.url
      this.formData.type = scope.row.type
      this.formData.versionCode = scope.row.versionCode
    },
    enableVersion(scope) {
      enableVersion({ 'id': scope.row.id, 'type': scope.row.type, 'enable': scope.row.enable }).then(response => {
        this.listLoading = false
        if (response.code === 1) {
            this.$modal.msgSuccess("操作成功");
          this.getList()
        } else {
            this.$modal.msgError("操作失败"); 
        }
      }).catch((error) => {
        this.listLoading = false
      })
    },
    deleteVersion(scope) {
      if (scope.row.enable) {
        pop_error(this, '该版本正在启用中, 请勿删除!')
        return
      }
      deleteVersion({ 'id': scope.row.id }).then(response => {
        this.listLoading = false
        if (response.code === 1) {
            this.$modal.msgSuccess("删除成功");
  
          this.getList()
        } else {
            this.$modal.msgError("删除失败"); 
       
        }
      }).catch((error) => {
        this.listLoading = false
      })
    },
    handleDelete(scope) {
      if (this.multipleSelection.length <= 0) {
        this.$modal.alert('请选择要删除的数据！')
       
        return
      }
      if (this.enable) {
        this.$modal.alert('选择中的数据中有正在启用的版本数据, 请勿删除!')
      
        return
      }
      deletesVersion({ 'ids': this.multipleSelection }).then(response => {
        this.listLoading = false
        this.enable = 0
        if (response.code === 1) {
            this.$modal.msgSuccess("删除成功");
          this.getList()
        } else {
            this.$modal.msgError("删除失败"); 
        }
      }).catch((error) => {
        this.listLoading = false
      })
    },
    handleAdd() { // 添加
      this.title = '添加'
      this.dialogFormVisible = true
      this.updateBoolean = true
      this.formData.id = ''
      this.formData.updateInfo = ''
      this.formData.versionNum = ''
      this.formData.createTime = ''
      this.formData.enable = ''
      this.formData.forceUpdate = ''
      this.formData.type = ''
      this.formData.versionCode = ''
    },

    handleSizeChange(val) { // 改变每页显示的行数
      this.listQuery.size = val
      this.getList()
    },

    handleCurrentChange(val) { // 选择第几页
      this.listQuery.page = val
      this.getList()
    },
    handleSearch() { // 条件查询
      this.listQuery.page = 1
      this.getList()
    },
    changeFun(val) {
      var ids = ''
      this.enable = 0
      val.map((m) => {
        ids += m.fileId + ','
      })
      val.map((m) => {
        if (m.enable) {
          this.enable += m.enable
        }
      })
      this.multipleSelection = ids
    },
    submitForm() { // 提交添加
      this.listLoading = true
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.dialogFormVisible = false
          this.listLoading = true
          if (this.title === '添加') {
            addVersion(this.formData).then(response => {
              this.listLoading = false
              if (response.code === 1) {
                this.$modal.msgSuccess("保存成功");
                this.getList()
              } else {
                this.$modal.msgError("保存失败"); 
              }
            }).catch((error) => {
              this.listLoading = false
            })
          } else if (this.title === '编辑') {
            updateVersion(this.formData).then(response => {
              this.listLoading = false
              if (response.code === 1) {
                this.$modal.msgSuccess("编辑成功");
                this.getList()
              } else {
                this.$modal.msgError("编辑失败"); 
              }
            }).catch((error) => {
              this.listLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    getList() { // 查询所有
      this.listLoading = true
      getList(this.listQuery).then(response => {
        if (response.code === 1) {
          this.list = response.data
          this.total = response.total
        } else {
          this.list = []
          this.total = 0
          pop_error(this, response.data.msg)
        }
        this.listLoading = false
      }).catch((error) => {
        this.listLoading = false
      })
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      isJPG = file.type === 'png'
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isJPG) {
        this.$modal.alert('上传图片只能是 JPG/PNG 格式!')
        return false
      }
      if (!isLt2M) {
        this.$modal.alert('上传图片大小不能超过 5MB!')
        return false
      }
      return true
    },
    /**
     * 点击上传文件
     */
    handleChange(file, fileList) {
      this.fileList = fileList
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    backgourdhandleChange(file, fileList) {
      this.backgourdfileList = fileList
    },
    backgourdhandleRemove(file, fileList) {
      this.backgourdfileList = fileList
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-item-wrapper {
  width: 200px;
  margin-right: 10px;
}

.filter-container {
  margin: 0px 0px 30px 0px;
}

</style>
