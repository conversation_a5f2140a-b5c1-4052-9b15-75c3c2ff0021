<template>
    <el-dialog title="新增" :visible.sync="dialogVisible" width="760px" append-to-body>
        <div v-loading="loading">
            <el-form ref="form" :model="form" label-width="100px">
                <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item
                    label="责任方"
                    prop="dutyName"
                    :rules="[
                        { required: true, message: '请输入责任方', trigger: 'blur' },
                    ]"
                    >
                        <el-input v-model="form.dutyName" placeholder="请输入" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item
                    label="涉及产品"
                    prop="productName"
                    :rules="[
                        { required: true, message: '请输入涉及产品', trigger: 'blur' },
                    ]"
                    >
                        <el-input v-model="form.productName" placeholder="请输入" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item
                    label="风险等级"
                    prop="riskLevel"
                    :rules="[
                        { required: true, message: '请选择风险等级', trigger: 'blur' },
                    ]"
                    >
                        <el-select
                        v-model="form.riskLevel"
                        placeholder="请选择"
                        clearable
                        >
                            <el-option
                            v-for="dict in riskLevelList"
                            :key="dict.value"
                            :label="dict.text"
                            :value="dict.value"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                </el-row>
                <el-form-item
                label="风险点描述"
                prop="riskDesc"
                :rules="[
                    { required: true, message: '请输入风险点描述', trigger: 'blur' },
                ]"
                >
                <el-input
                    v-model="form.riskDesc"
                    type="textarea"
                    placeholder="请输入"
                />
                </el-form-item>
                <el-form-item
                label="风险原因"
                prop="riskReason"
                :rules="[
                    { required: true, message: '请输入风险原因', trigger: 'blur' },
                ]"
                >
                <el-input
                    v-model="form.riskReason"
                    type="textarea"
                    placeholder="请输入"
                />
                </el-form-item>
                <el-form-item
                label="保证措施"
                prop="measuresDesc"
                :rules="[
                    { required: true, message: '请输入保证措施', trigger: 'blur' },
                ]"
                >
                <el-input
                    v-model="form.measuresDesc"
                    type="textarea"
                    placeholder="请输入"
                />
                </el-form-item>
                <el-form-item
                prop="checkList"
                label="责任部门"
                :rules="[
                    {
                    type: 'array',
                    required: true,
                    message: '请选择部门人员',
                    trigger: 'blur',
                    },
                ]"
                >
                <el-checkbox-group v-model="form.checkList" :max="1">
                    <el-row :gutter="20" style="margin: 0">
                    <el-col
                        :span="8"
                        v-for="(item, index) in dutyUserList"
                        :key="index"
                    >
                        <el-checkbox :label="item.userId">{{
                        item.userName
                        }}</el-checkbox>
                    </el-col>
                    </el-row>
                </el-checkbox-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="confirm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import { getDutyUserList,addRisk } from "@/api/riskControl/projectRiskList";
export default {
  components:{},

  data(){
    return{
        dialogVisible:false,
        loading:false,
        form:{
            dutyName: '',
            productName: '',
            riskLevel: '',
            riskDesc: '',
            riskReason: '',
            measuresDesc: '',
            checkList: [],
        },
        dutyUserList:[],
        riskLevelList: [
            {
                text: "严重",
                value: 0,
            },
            {
                text: "一般",
                value: 1,
            },
            {
                text: "轻微",
                value: 2,
            },
        ],
    }
  },

  created(){},

  methods:{
    init(){
        this.dialogVisible = true
        this.form = {
            dutyName: '',
            productName: '',
            riskLevel: '',
            riskDesc: '',
            riskReason: '',
            measuresDesc: '',
            checkList: [],
        }
        this.loadData()
    },
    loadData(){
        this.loading = true
        getDutyUserList().then((res) => {
          this.dutyUserList = res.data;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    confirm(){
        this.$refs["form"].validate((valid) => {
        if (valid) {
            let params = {
                ...this.form, 
                dutyUserId: this.form.checkList[0]
            }
          addRisk(params).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.dialogVisible = false
              this.$emit('getList');
            }
          );
        }
      });
    },
    cancel(){
        this.$refs['form'].resetFields()
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped lang='less'>
</style>