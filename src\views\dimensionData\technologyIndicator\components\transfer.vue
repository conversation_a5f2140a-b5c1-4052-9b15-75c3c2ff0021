<template>
    <el-dialog :title="userData.title" top="30px" :visible.sync="userData.open" width="60%" append-to-body>
      <el-row type="flex" :gutter="20">
        <!--部门数据-->
        <el-col :span="7">
          <el-card shadow="never" style="height: 100%">
            <div slot="header">
              <span>部门列表</span>
            </div>
            <el-input v-model="deptName" placeholder="请输入部门名称" clearable size="small" prefix-icon="el-icon-search" />
            <div class="head-container">
              <el-tree v-loading="treeLoading" :data="deptOptions" :props="deptProps" :expand-on-click-node="false"
                :filter-node-method="filterNode" ref="nodeTree" @node-click="handleNodeClick" class="dept-tree" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="17">
          <el-row>
            <el-col :span="24">
              <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
                <el-form-item label="姓名" prop="keyword">
                  <el-input v-model="queryParams.keyword" placeholder="请输入" clearable style="width: 240px"
                    @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <el-table ref="userTable" :key="userData.type" height="500" v-loading="userLoading" :data="userList"
            highlight-current-row @current-change="changeCurrentUser" @selection-change="handleSelectionChange">
            <el-table-column v-if="userData.type === 'copy' || userData.type === 'next'" width="55" type="selection" />
            <el-table-column v-else width="30">
              <template slot-scope="scope">
                <el-radio :label="scope.row.id" v-model="currentUserId">{{ '' }}</el-radio>
              </template>
            </el-table-column>
            <el-table-column label="用户名" align="center" prop="username" />
            <el-table-column label="姓名" align="center" prop="name" />
            <el-table-column label="部门" align="center" prop="deptName" />
          </el-table>
          <div class="page_box">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentPage"
              @prev-click="handlePage" @next-click="handlePage" :current-page="page.pageNum" :pager-count="5"
              :page-sizes="[10, 20, 30, 40]" :page-size="10" layout="total, sizes, prev, pager, next, jumper"
              :total="page.total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="userData.open = false">取 消</el-button>
        <el-button type="primary" @click="submitUserData">确 定</el-button>
      </span>
    </el-dialog>
    <!-- </template> -->
    <!-- </parser>
      </div>
    </el-dialog> -->
  </template>
  <script>
  import Parser from '@/utils/generator/parser'
  import { getProcessForm, startProcess } from '@/api/officeMan/newProcess'
  import { detailInfo } from '@/api/officeMan/myProcess'
  import { deptTree } from '@/api/organizationChart/organizationalMan'
  import { listUser } from '@/api/authorityMan/userMan'
  import { approve, returnList, rejectTask } from '@/api/officeMan/toDoTasks'
  import { batchAdd } from '@/api/dimensionData/newProcess'
  import { apiChange, apiChangeBatch } from "@/api/dimensionData/nodeManagement.js";
  export default {
    components: { Parser },
    data() {
      return {
        selectedUsers: [], // 用于存储已选中的用户
  
        treeLoading: false,
        btnLoading: false,
        loading: false,
        dialogVisible: false,
        rowData: null,
        value1: '',
        options: [
          {
            value: 0,
            label: '禁用',
          },
          {
            value: 1,
            label: '正常'
          }
        ],
  
        formData: {},
        //审批人弹窗相关的
        dialogVisible: false,
        form: {},
        // 模型xml数据
        loadIndex: 0,
        xmlData: undefined,
        finishedInfo: {
          finishedSequenceFlowSet: [],
          finishedTaskSet: [],
          unfinishedTaskSet: [],
          rejectedTaskSet: []
        },
        historyProcNodeList: [],
        // 部门名称
        deptName: undefined,
        // 部门树选项
        deptOptions: undefined,
        userLoading: false,
        // 用户表格数据
        userList: null,
        deptProps: {
          children: 'children',
          label: 'name',
          value: 'id'
        },
        // 查询参数
        queryParams: {
          deptId: undefined
        },
        page: {
          pageSize: 10,
          pageNum: 1,
          total: 0
        },
        total: 0,
        // 遮罩层
        loading: false,
        taskForm: {
          comment: "", // 意见内容
          procInsId: "", // 流程实例编号
          deployId: "",  // 流程定义编号
          taskId: "",// 流程任务编号
          definitionId: "",  // 流程编号
          copyUserIds: "", // 抄送人Id
          vars: "",
          targetKey: ""
        },
        rules: {
          comment: [{ required: true, message: '请输入审批意见', trigger: 'blur' }],
        },
        currentUserId: null,
        variables: [], // 流程变量数据
        taskFormOpen: false,
        taskFormData: {}, // 流程变量数据
        processFormList: [], // 流程变量数据
        formOpen: false, // 是否加载流程变量数据
        returnTaskList: [],  // 回退列表数据
        finished: 'false',
        returnTitle: null,
        returnOpen: false,
        rejectOpen: false,
        rejectTitle: null,
        userData: {
          title: '',
          type: '',
          open: false,
        },
        copyUser: [],
        nextUser: [],
        userMultipleSelection: [],
        userDialogTitle: '',
        userOpen: false,
        // 转办入参
        query: {

        }
      }
    },
    methods: {
      async init(row) {
        this.rowData = row
        await this.getProcessForm()
  
      },
  
  
      getProcessForm() {
        let params = {
          definitionId: this.rowData.definitionId,
          deployId: this.rowData.deploymentId
        }
        getProcessForm(params).then((res) => {
          this.formData = res.data;
          this.dialogVisible = true
        }).catch(() => {
        })
      },
  
      /** 接收子组件传的值 */
      getData(data) {
        if (data) {
          const variables = [];
          data.fields.forEach(item => {
            let variableData = {};
            variableData.label = item.label
            // 表单值为多个选项时
            if (item.defaultValue instanceof Array) {
              const array = [];
              item.defaultValue.forEach(val => {
                array.push(val)
              })
              variableData.val = array;
            } else {
              variableData.val = item.defaultValue
            }
            variables.push(variableData)
          })
          this.variables = variables;
        }
      },
  
      async submit(data) {
        console.log('this.userMultipleSelection', this.userMultipleSelection);
  
        if (data && this.rowData) {
          let ortherMap = {
            userIds: this.userMultipleSelection.map(k => k.id).join(',')
          }
          await startProcess(JSON.stringify(data.valData), this.rowData.definitionId, ortherMap).then(() => {
            this.$emit('hidDialog')
            this.$message({
              type: 'success',
              message: '操作成功',
              duration: 1500
            })
            this.dialogVisible = false
          }).catch(() => {
  
          })
  
        }
      },
      //审批人弹窗相关
      /** 搜索按钮操作 */
      handleQuery() {
        this.page.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.page.pageNum = 1;
        this.queryParams.keyword = null;
        this.selectedUsers = []; // 清空所有选择
        this.userMultipleSelection = [];
        this.getList();
      },
      initData() {
        // 流程任务重获取变量表单
        if (this.taskForm.taskId) {
          this.getProcessDetails(this.taskForm.procInsId, this.taskForm.deployId, this.taskForm.taskId);
        }
        this.loadIndex = this.taskForm.procInsId;
      },
  
      // 获取表单信息
      getProcessDetails(procInsId, deployId, taskId) {
        this.loading = true
        const params = { procInsId: procInsId, deployId: deployId, taskId: taskId }
        detailInfo(params).then(res => {
          const data = res.data;
          this.xmlData = data.bpmnXml;
          this.processFormList = data.processFormList;
          this.taskFormOpen = data.existTaskForm;
          if (this.taskFormOpen) {
            this.taskFormData = data.taskFormData;
          }
          this.historyProcNodeList = data.historyProcNodeList;
          this.finishedInfo = data.flowViewer;
          this.formOpen = true
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      },
  
      /** 查询部门下拉树结构 */
      getTreeSelect() {
        this.treeLoading = true
        deptTree().then(response => {
          this.deptOptions = response.data;
          this.treeLoading = false
  
        });
      },
  
      /** 查询用户列表 */
      getList() {
        this.userLoading = true;
        listUser(Object.assign(this.queryParams, this.page)).then(response => {
          let resData = response.data
          this.userList = resData.records;
          this.page.total = resData.total;
  
          // 恢复选中状态
          this.$nextTick(() => {
            console.log('this.selectedUsers', this.selectedUsers.length);
  
            // 恢复当前页面中应该被选中的行
            this.userList.forEach(row => {
              if (this.selectedUsers.some(selected => selected.id === row.id)) {
                this.$refs.userTable.toggleRowSelection(row, true);
              }
            });
          });
  
          this.userLoading = false;
        });
      },
      // 筛选节点
      filterNode(value, data) {
        if (!value) return true;
        return data.name.indexOf(value) !== -1;
      },
      // 节点单击事件
      handleNodeClick(data) {
        this.queryParams.deptId = data.id;
        this.page.pageNum = 1;
  
        this.getList();
      },
      setIcon(val) {
        if (val) {
          return "el-icon-check";
        } else {
          return "el-icon-time";
        }
      },
      setColor(val) {
        if (val) {
          return "#2bc418";
        } else {
          return "#b3bdbb";
        }
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
  
        // 合并其他页面的选中状态和当前页面的选中状态
        const mergedSelection = [...this.selectedUsers, ...selection];
        this.selectedUsers = mergedSelection.filter((user, index) =>
          mergedSelection.findIndex(item => item.id === user.id) === index
        );
        this.userMultipleSelection = this.selectedUsers;
      },
  
      toggleSelection(selection) {
        if (selection && selection.length > 0) {
          this.$nextTick(() => {
            selection.forEach(item => {
              let row = this.userList.find(k => k.id === item.id);
              this.$refs.userTable.toggleRowSelection(row);
            })
          })
        } else {
          this.$nextTick(() => {
            this.$refs.userTable.clearSelection();
          });
        }
      },
      // 关闭标签
      handleClose(type, tag) {
        let userObj = this.userMultipleSelection.find(item => item.id === tag.id);
        this.userMultipleSelection.splice(this.userMultipleSelection.indexOf(userObj), 1);
        if (type === 'copy') {
          this.copyUser = this.userMultipleSelection;
          // 设置抄送人ID
          if (this.copyUser && this.copyUser.length > 0) {
            const val = this.copyUser.map(item => item.id);
            this.taskForm.copyUserIds = val instanceof Array ? val.join(',') : val;
          } else {
            this.taskForm.copyUserIds = '';
          }
        } else if (type === 'next') {
          this.nextUser = this.userMultipleSelection;
          // 设置抄送人ID
          if (this.nextUser && this.nextUser.length > 0) {
            const val = this.nextUser.map(item => item.id);
            this.taskForm.nextUserIds = val instanceof Array ? val.join(',') : val;
          } else {
            this.taskForm.nextUserIds = '';
          }
        }
      },
      /** 流程变量赋值 */
      handleCheckChange(val) {
        if (val instanceof Array) {
          this.taskForm.values = {
            "approval": val.join(',')
          }
        } else {
          this.taskForm.values = {
            "approval": val
          }
        }
      },
  
  
      onSelectCopyUsers() {
        this.userMultipleSelection = this.copyUser;
        this.onSelectUsers('添加抄送人', 'copy')
      },
      onSelectNextUsers() {
        this.userMultipleSelection = this.nextUser;
        this.onSelectUsers('指定审批人', 'next')
      },
      onSelectUsers(title, type) {
        this.userData.title = title;
        this.userData.type = type;
        this.getTreeSelect();
        this.getList()
        this.selectedUsers = []
        this.userMultipleSelection = [];
  
        this.userData.open = true;
      },
      // 通过
      handleComplete() {
        // 校验表单
        const taskFormRef = this.$refs.taskFormParser;
        const isExistTaskForm = taskFormRef !== undefined;
        // 若无任务表单，则 taskFormPromise 为 true，即不需要校验
        const taskFormPromise = !isExistTaskForm ? true : new Promise((resolve, reject) => {
          taskFormRef.$refs[taskFormRef.formConfCopy.formRef].validate(valid => {
            valid ? resolve() : reject()
          })
        });
        const approvalPromise = new Promise((resolve, reject) => {
          this.$refs['taskForm'].validate(valid => {
            valid ? resolve() : reject()
          })
        });
        Promise.all([taskFormPromise, approvalPromise]).then(() => {
          if (isExistTaskForm) {
            this.taskForm.variables = taskFormRef[taskFormRef.formConfCopy.formModel]
          }
          approve(this.taskForm).then(response => {
            this.$message({
              type: 'success',
              message: '操作成功',
              duration: 1500
            })
            this.dialogVisible = false
            this.$emit('hidDialog')
          });
        })
      },
      /** 委派任务 */
      handleDelegate() {
        this.$refs["taskForm"].validate(valid => {
          if (valid) {
            this.userData.type = 'delegate';
            this.userData.title = '委派任务'
            this.userData.open = true;
            this.getTreeSelect();
          }
        })
      },
      /** 转办任务 */
      handleTransfer() {
        this.$refs["taskForm"].validate(valid => {
          if (valid) {
            this.userData.type = 'transfer';
            this.userData.title = '转办任务';
            this.userData.open = true;
            this.getTreeSelect();
          }
        })
      },
      /** 拒绝任务 */
      handleReject() {
        this.$refs["taskForm"].validate(valid => {
          if (valid) {
            this.$confirm('拒绝审批单流程会终止，是否继续？', '提示', {
              type: 'warning'
            }).then(() => {
              rejectTask(this.taskForm).then((res) => {
                this.$message({
                  type: 'success',
                  message: '操作成功',
                  duration: 1500
                })
                this.dialogVisible = false
                this.$emit('hidDialog')
              })
            })
          }
        });
      },
      changeCurrentUser(val) {
        this.currentUserId = val.id;
        this.query.name = val.name;
        this.query.username = val.username;
        this.query.deptNo = val.deptNo;
        this.query.deptName = val.deptName;
      },
  
  
      /** 返回页面 */
      goBack() {
        // 关闭当前标签页并返回上个页面
        this.$tab.closePage(this.$route)
        this.$router.back()
      },
      /** 接收子组件传的值 */
      getData(data) {
        if (data) {
          const variables = [];
          data.fields.forEach(item => {
            let variableData = {};
            variableData.label = item.__config__.label
            // 表单值为多个选项时
            if (item.__config__.defaultValue instanceof Array) {
              const array = [];
              item.__config__.defaultValue.forEach(val => {
                array.push(val)
              })
              variableData.val = array;
            } else {
              variableData.val = item.__config__.defaultValue
            }
            variables.push(variableData)
          })
          this.variables = variables;
        }
      },
      async submitUserData() {
        this.userData.open = false
        let type = this.userData.type;
        if (type === 'copy' || type === 'next') {
          if (!this.userMultipleSelection || this.userMultipleSelection.length <= 0) {
            this.$modal.msgError("请选择用户");
            return false;
          }
          await batchAdd(this.userMultipleSelection).then(() => {
            this.$message({
              type: 'success',
              message: '操作成功',
              duration: 1500
            })
            this.userData.open = false;
            console.log('在 submitUserData 中触发 hidDialog 事件');
            this.$emit('hidDialog');
          })
        }
        // else {
        //   if (!this.taskForm.comment) {
        //     this.$modal.msgError("请输入审批意见");
        //     return false;
        //   }
        //   if (!this.currentUserId) {
        //     this.$modal.msgError("请选择用户");
        //     return false;
        //   }
        //   this.taskForm.id = this.currentUserId;
        //   // 委派
        //   if (type === 'delegate') {
        //     delegate(this.taskForm).then(res => {
        //       this.$message({
        //         type: 'success',
        //         message: '操作成功',
        //         duration: 1500
        //       })
        //       this.userData.open = false
        //       this.dialogVisible = false
        //       this.$emit('hidDialog')
        //     });
        //   }
        //   // 转办
        //   if (type === 'transfer') {
        //     transfer(this.taskForm).then(res => {
        //       this.$message({
        //         type: 'success',
        //         message: '操作成功',
        //         duration: 1500
        //       })
        //       this.userData.open = false
        //       this.dialogVisible = false
        //       this.$emit('hidDialog')
        //     });
        //   }
        // }

        if (type === 'transfer') {
          if (!this.currentUserId) {
            this.$modal.msgError("请选择用户");
            return false;
          }
          const api = this.query.ids ? apiChangeBatch : apiChange;
          await api(this.query).then(() => {
            this.$message({
              type: 'success',
              message: '操作成功',
              duration: 1500
            })
            this.userData.open = false;
            this.$emit('update');
          })
        }
      },
      /** 可退回任务列表 */
      handleReturn() {
        this.$refs['taskForm'].validate(valid => {
          if (valid) {
            this.returnTitle = "退回流程";
            returnList(this.taskForm).then(res => {
              this.returnTaskList = res.data;
              this.taskForm.values = null;
              this.returnOpen = true;
            })
          }
        });
  
      },
      // 退回
      submitReturn() {
        this.$refs["taskForm"].validate(valid => {
          if (valid) {
            if (!this.taskForm.targetKey) {
              this.$modal.msgError("请选择退回节点！");
            }
            returnTask(this.taskForm).then(res => {
              this.$modal.msgSuccess(res.msg);
              this.goBack()
            });
          }
        });
      },
  
      // 更改每页显示条数
      handleSizeChange(pageSize) {
        this.page.pageSize = pageSize
        this.getList()
      },
  
      // 选择页数
      handleCurrentPage(currentPage) {
        this.page.pageNum = currentPage
        this.getList()
      },
  
      // 点击上一页/下一页
      handlePage(currentPage) {
        this.page.pageNum = currentPage
        this.getList()
      }
    }
  }
  </script>
  <style lang="less" scoped>
  .demo-ruleForm {
    .status_row {
      width: 100%;
    }
  }
  
  .dialog-footer {
    display: block;
  }
  
  .head-container {
    margin-top: 10px;
    max-height: 500px;
    overflow-y: auto;
  }
  </style>
  