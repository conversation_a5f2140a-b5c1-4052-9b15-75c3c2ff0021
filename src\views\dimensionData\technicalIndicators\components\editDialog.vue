<template>
  <el-dialog :title="title" :visible.sync="visible" width="500px" append-to-body>
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="资料名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入资料名称" disabled />
      </el-form-item>
      <el-form-item label="是否必填" prop="requiredStatus">
        <el-switch v-model="formData.requiredStatus" :disabled="title === '查看'" :active-value="1" :inactive-value="0" />
      </el-form-item>
      <el-form-item label="资料说明" prop="remarks">
        <el-input type="textarea" v-model="formData.remarks" placeholder="请输入资料说明" disabled />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">{{ title === '查看' ? '关闭' : '取 消' }}</el-button>
      <el-button v-if="title !== '查看'" type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'EditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增'
    },
    row: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        name: '',
        required: false,
        description: ''
      },
      rules: {
        name: [{ required: true, message: '请输入资料名称', trigger: 'blur' }],
        description: [{ required: true, message: '请输入资料说明', trigger: 'blur' }]
      }
    }
  },
  watch: {
    row: {
      handler(val) {
        if (val) {
          this.formData = { ...val }
        }
      },
      immediate: true
    }
  },
  methods: {
    handleCancel() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.$emit('submit', this.formData)
          this.handleCancel()
        }
      })
    }
  }
}
</script>
