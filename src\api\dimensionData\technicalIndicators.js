// src/api/dimensionData/technicalIndicators.js
import request from "@/utils/request";

// 获取技术指标列表
export function getList(query) {
  return request({
    url: "/api/dimension/technical/list",
    method: "get",
    params: query,
  });
}
// 获取资料管理列表
export function getDataList(query) {
  return request({
    url: "/sd-server/tech/data",
    method: "get",
    params: query,
  });
}
// 新增技术指标
export function addTechnical(data) {
  return request({
    url: "/sd-server/data/dept/library",
    method: "post",
    data,
  });
}

// 修改技术指标
export function updateTechnical(data) {
  return request({
    url: "/sd-server/data/dept/library",
    method: "put",
    data,
  });
}

// 删除技术指标
export function deleteTechnical(params) {
  return request({
    url: `/sd-server/data/dept/library`,
    method: "delete",
    params,
  });
}

// 获取技术指标详情
export function getTechnicalDetail(params) {
  return request({
    url: `/sd-server/data/dept/library/one`,
    method: "get",
    params,
  });
}
