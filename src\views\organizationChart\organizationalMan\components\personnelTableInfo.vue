<template>
  <el-drawer title="查看人员" size="75%" :visible.sync="drawerDialog">
    <div class="department_Info">
      <div class="search_form">
        <el-row type="flex" :gutter="5">
          <el-col :span="7">
            <el-input size="small" v-model="searchForm.name" placeholder="请输入姓名" clearable></el-input>
          </el-col>
          <el-col :span="4">
            <el-select size="small" v-model="searchForm.dataScope" @change="changeSelect">
              <el-option v-for="item in dicList.rankList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="6">
            <div class="btn_box">
              <el-button @click="search(false)" size="small" class="btn search_btn" icon="el-icon-search"
                type="primary">搜索</el-button>
              <el-button @click="search(true)" size="small" class="btn reset_btn" icon="el-icon-refresh">重置</el-button>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="table_box" v-loading="loading">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="name" label="名称" align="center" width="">
          </el-table-column>

          <el-table-column prop="username" label="编码" align="center" width="">
          </el-table-column>

          <el-table-column prop="deptName" label="部门" align="center" width="">
          </el-table-column>

          <el-table-column prop="enable" label="状态" align="center" width="">
            <template slot-scope="{row}">
              <el-tag :type="row.enable === 0 ? 'danger' : 'default'">{{ row.enable | checkDic('peopleStatus') }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="creator" label="创建人" align="center" width="">
          </el-table-column>

          <el-table-column prop="createTime" label="创建时间" align="center" width="">
            <template slot-scope="{row}">
              <div v-if="row.createTime">
                <i class="el-icon-time"></i>
                {{ row.createTime | dateTimeFormat }}
              </div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="100">
            <template slot-scope="scope">
              <div class="handle_btn">
                <el-button @click="handleInfo(scope.row)" type="text" size="small" icon="el-icon-view">详情</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="page_box">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentPage"
          @prev-click="handlePage" @next-click="handlePage" :current-page="page.pageNum" :pager-count="5"
          :page-sizes="[10, 20, 30, 40]" :page-size="10" layout="total, sizes, prev, pager, next, jumper"
          :total="page.total">
        </el-pagination>
      </div>

      <personne-info v-if="personneInfoDialog" ref="personneInfo"></personne-info>
    </div>
  </el-drawer>
</template>
<script>
import personneInfo from './personneInfo.vue'
import { deptUser } from '@/api/organizationChart/organizationalMan'
import store from '@/store/index'
export default {
  components: { personneInfo },
  data() {
    return {
      loading: false,
      drawerDialog: false,
      personneInfoDialog: false,
      deptRow: {},
      dicList: store.state.dicList,
      tableData: [],
      searchForm: {
        name: '',
        dataScope: 0
      },
      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0
      },
    }
  },
  methods: {
    init(row) {
      this.deptRow = row
      this.drawerDialog = true
      this.page.pageNum = 1
      this.searchForm.dataScope = 0
      this.tableData = []
      this.loadData()
    },
    // 搜索/重置
    search(reset) {
      if (reset) {
        this.searchForm = {
          name: "",
          dataScope: 0
        }
      }
      this.page.pageNum = 1
      this.loadData()
    },

    changeSelect() {
      this.page.pageNum = 1
      this.loadData()
    },

    // 详情
    handleInfo(row) {
      this.personneInfoDialog = true
      this.$nextTick(() => {
        this.$refs.personneInfo.init(row)
      })
    },
    // 获取部门列表
    loadData() {
      this.loading = true
      let params = {
        deptId: this.deptRow.id,
        ...this.searchForm,
        ...this.page
      }
      deptUser(params).then((res) => {
        let resData = res.data || {}
        this.tableData = resData.records || []
        this.page.total = resData.total || 0
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 更改每页显示条数
    handleSizeChange(pageSize) {
      this.page.pageSize = pageSize
      this.loadData()
    },

    // 选择页数
    handleCurrentPage(currentPage) {
      this.page.pageNum = currentPage
      this.loadData()
    },

    // 点击上一页/下一页
    handlePage(currentPage) {
      this.page.pageNum = currentPage
      this.loadData()
    }
  }
}
</script>
<style lang="less" scoped>
.department_Info {
  .search_form {
    border-radius: 4px;
    padding: 0 12px;
    box-sizing: border-box;

    .btn_box {
      padding-left: 10px;
      box-sizing: border-box;
      display: flex;

      .reset_btn {
        color: #666666;
      }
    }
  }

  .table_box {
    margin-top: 10px;

    .el-table {
      min-height: 350px;

      .handle_btn {
        display: flex;
        justify-content: space-around;
      }
    }

    .del {
      color: #E03030;
    }
  }

  .page_box {
    text-align: right;
  }
}
</style>
