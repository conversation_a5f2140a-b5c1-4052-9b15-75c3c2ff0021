<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--用户数据-->
      <el-col :span="24" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
          <el-form-item label="搜索" prop="keyword">
            <el-input v-model="queryParams.keyword" placeholder="请输入" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="业绩评价" prop="status">
            <el-select v-model="queryParams.status">
              <el-option
                v-for="dict in [{ label: '全部', value: 0 }, { label: '已开启', value: 1 }, { label: '未开启', value: 2 }, { label: '无分类', value: 3 }]"
                :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
            <!-- <el-input v-model="queryParams.keyword" placeholder="请输入" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" /> -->
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
              @click="handleDeletes">删除</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table :data="listPage" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" />
          <el-table-column label="供应商编码" key="supplierCode" prop="supplierCode" v-if="columns[0].visible" />
          <el-table-column label="供应商名称" key="supplierName" prop="supplierName" v-if="columns[1].visible" />
          <el-table-column label="业绩评价" key="status" prop="status" align="center">
            <template slot-scope="scope">
              {{ ["已开启", "未开启", "无分类",][scope.row.status - 1] }}
            </template>
          </el-table-column>
          <el-table-column label="供应商电话" key="supplierTel" prop="supplierTel" v-if="columns[2].visible" width="120" />
          <el-table-column label="姓名" key="name" prop="name" v-if="columns[3].visible" />
          <el-table-column label="手机号" key="phone" prop="phone" v-if="columns[3].visible" width="120" />
          <el-table-column label="合同数" key="contractNum" prop="contractNum" v-if="columns[3].visible" />
          <el-table-column label="考察数" key="inspectNum" prop="inspectNum" v-if="columns[3].visible" />
          <el-table-column label="操作" align="center" width="400" class-name="small-padding fixed-width" fixed="right">
            <template slot-scope="scope" v-if="scope.row.userId !== 1">
              <el-button size="mini" type="text" icon="el-icon-setting"
                @click="handleManageCategory(scope.row)">管理分类</el-button>
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
              <el-button size="mini" type="text" icon="el-icon-setting" @click="handleUser(scope.row)">联系人管理</el-button>
              <el-button size="mini" type="text" icon="el-icon-setting"
                @click="handleAddChild(scope.row)">生成子账号</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.size"
          @pagination="getList" />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="供应商名称" prop="supplierName">
              <el-input v-model="form.supplierName" placeholder="请输入" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商编码" prop="supplierCode">
              <el-input v-model="form.supplierCode" placeholder="请输入" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商简称" prop="shortName">
              <el-input v-model="form.shortName" placeholder="请输入" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商电话" prop="supplierTel">
              <el-input v-model="form.supplierTel" placeholder="请输入" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入" maxlength="30" type="password" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addSupplier">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 联系人管理 -->
    <el-dialog :title="title" :visible.sync="opens" width="800px" append-to-body>
      <el-form :model="queryList" ref="queryForm" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="搜索" prop="keyword">
          <el-input v-model="queryList.keyword" placeholder="请输入" clearable style="width: 240px"
            @keyup.enter.native="handleQuery1" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery1">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery1">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="userAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
            @click="handleDeleteCon">删除</el-button>
        </el-col>
      </el-row>
      <el-table :data="contactList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" />
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column label="人员姓名" key="name" prop="name" v-if="columns[0].visible" />
        <el-table-column label="职位" key="position" prop="position" v-if="columns[1].visible"
          :show-overflow-tooltip="true" />
        <el-table-column label="电话" key="phone" prop="phone" v-if="columns[2].visible" :show-overflow-tooltip="true" />
      </el-table>
      <pagination v-show="suppliertotal > 0" :total="suppliertotal" :page.sync="queryParams.page"
        :limit.sync="queryParams.size" @pagination="getList" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="opens = false">确 定</el-button>
        <el-button @click="opens = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 联系人管理内部新增弹窗 -->
    <el-dialog :title="title" :visible.sync="userAdds" width="400px" append-to-body>
      <el-form :model="addqueryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
        label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="addqueryParams.name" placeholder="请输入" style="width: 240px" />
        </el-form-item>
        <el-form-item label="职位" prop="position">
          <el-input v-model="addqueryParams.position" placeholder="请输入" style="width: 240px" />
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="addqueryParams.phone" placeholder="请输入" style="width: 240px" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addContact">确 定</el-button>
        <el-button @click="userAdds = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 引入分类管理组件 -->
    <category-management :visible.sync="categoryDialogVisible" :supplier="currentSupplier"></category-management>
  </div>
</template>

<script>
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser,
  resetUserPwd,
  changeUserStatus,
} from "@/api/system/user";
import {
  addSupplier,
  updateSupplier,
  deleteSupplier,
  deletesSupplier,
  getListPage,
  addSupplierContact,
  deleteSupplierContact,
  getContactListPage,
  generateSupplier,
} from "@/api/system/supplier";
import { getToken } from "@/utils/auth";
import { treeselect } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import CategoryManagement from "./components/CategoryManagement";

export default {
  name: "User",
  components: { Treeselect, CategoryManagement },
  data() {
    return {
      //供应商列表
      listPage: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      suppliertotal: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 联系人管理是否显示弹出层
      opens: false,
      // 联系人管理内部新增是否显示弹出层
      userAdds: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {
        type: 1,
        supplierName: "", //供应商名称
        supplierCode: "", //供应商编码
        shortName: "", //供应商简称
        supplierTel: "", //供应商电话
        name: "", //姓名
        phone: "", //手机号
        password: "", //密码
        address: "", //地址
      },
      //供应商联系人查询
      queryList: {
        supplierId: 1,
        page: 1,
        size: 10,
        keyword: "",
      },
      //供应商联系人列表
      contactList: [],
      //新增供应商联系人
      addqueryParams: {
        name: "", //姓名
        phone: "", //电话
        position: "", //职位
        supplierId: "", //供应商id
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData",
      },
      // 查询参数
      queryParams: {
        status: 0,
        type: 1,
        page: 1,
        size: 10,
        keyword: "",
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `用户名称`, visible: true },
        { key: 2, label: `用户昵称`, visible: true },
        { key: 3, label: `部门`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建时间`, visible: true },
      ],
      // 表单校验
      // rules: {
      //   supplierCode: [
      //     { required: true, message: "供应商编码不能为空", trigger: "blur" },
      //     {
      //       min: 6,
      //       max: 32,
      //       message: "供应商编码长度必须介于 2 和 20 之间",
      //       trigger: "blur",
      //     },
      //   ],
      //   supplierName: [
      //     { required: true, message: "供应商名称不能为空", trigger: "blur" },
      //   ],
      //   password: [
      //     { required: true, message: "用户密码不能为空", trigger: "blur" },
      //     {
      //       min: 5,
      //       max: 20,
      //       message: "用户密码长度必须介于 5 和 20 之间",
      //       trigger: "blur",
      //     },
      //   ],
      //   phone: [
      //     {
      //       pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      //       message: "请输入正确的手机号码",
      //       trigger: "blur",
      //     },
      //   ],
      // },
      sexs: [
        {
          label: "女",
          value: 0,
        },
        {
          label: "男",
          value: 1,
        },
      ],
      //状态
      studes: [{
        label: '启动',
        value: 1
      }, {
        label: '停用',
        value: 0
      }],
      // 管理分类相关数据
      categoryDialogVisible: false,
      currentSupplier: null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleAddChild(row) {
      generateSupplier(row.id).then(res => {
        this.$modal.msgSuccess("生成成功");
      });
    },
    //查询供应商列表
    getList() {
      this.loading = true
      getListPage(this.queryParams).then(res => {
        this.listPage = res.data;
        this.total = res.total
        this.loading = false
      });

    },

    //新增供应商
    async addSupplier() {
      if (this.form.id != undefined) {
        updateSupplier(this.form).then((response) => {
          this.$modal.msgSuccess("修改成功");
          this.open = false;
          this.getList();
        });
      } else {
        addSupplier(this.form).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      }
    },

    /** 查询用户列表 */
    // getList() {
    //   this.loading = true;
    //   listUser(this.addDateRange(this.queryParams, this.dateRange)).then(
    //     (response) => {
    //       this.userList = response.rows;
    //       this.total = response.total;
    //       this.loading = false;
    //     }
    //   );
    // },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === 1 ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '""' + row.userName + '"用户吗？')
        .then(function () {
          return changeUserStatus(row.userId, row.status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },
    // 表单重置
    reset() {
      this.form = {
        type: 1,
        supplierName: undefined,
        supplierCode: undefined,
        shortName: undefined,
        supplierTel: undefined,
        name: undefined,
        phone: undefined,
        password: undefined,
        address: undefined,
      };
      this.resetForm("form");
    },
    queryForm() {
      this.addqueryParams.name = "";
      this.addqueryParams.phone = "";
      this.addqueryParams.position = "";
    },
    /** 搜索按钮操作 */
    //供应商
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    //联系人
    handleQuery1() {
      this.queryList.page = 1;
      this.handleUser();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.getList();
    },
    resetQuery1() {
      this.queryList.page = 1
      this.queryList.keyword = undefined
      this.handleUser();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleResetPwd":
          this.handleResetPwd(row);
          break;
        case "handleAuthRole":
          this.handleAuthRole(row);
          break;
        default:
          break;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = JSON.parse(JSON.stringify(row));
      this.open = true;
      this.title = "修改";
    },
    /**联系人管理 */
    handleUser(row) {
      this.title = "联系人管理";
      this.opens = true;
      if (row) {
        this.queryList.supplierId = row.id;
        this.addqueryParams.supplierId = row.id;
      }

      getContactListPage(this.queryList).then(res => {
        this.contactList = res.data;
        this.suppliertotal = res.total
      });

    },
    /** 联系人内部新增按钮*/
    userAdd() {
      this.queryForm();
      this.userAdds = true;
      this.title = "新增联系人";
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.userName + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: "用户密码长度必须介于 5 和 20 之间",
      })
        .then(({ value }) => {
          resetUserPwd(row.userId, value).then((response) => {
            this.$modal.msgSuccess("修改成功，新密码是：" + value);
          });
        })
        .catch(() => { });
    },
    /** 分配角色操作 */
    handleAuthRole: function (row) {
      const userId = row.userId;
      this.$router.push("/system/user-auth/role/" + userId);
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.userId != undefined) {
            updateUser(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUser(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let that = this;
      that
        .$confirm(`是否确认删除ID为${row.id}`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(async function () {
          await deleteSupplier({ id: row.id });
          that.getList();
        });
    },
    /** 删除按钮操作 */
    handleDeletes() {
      let that = this;

      that
        .$confirm(`是否确认删除ID为${that.ids}`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(async function () {
          await deletesSupplier(that.ids);
          that.getList();
        });
    },
    /** 删除按钮操作 */
    handleDeleteCon() {
      let that = this;
      that
        .$confirm(`是否确认删除ID为${that.ids}`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(async function () {
          await deleteSupplierContact(that.ids);
          that.handleUser();
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/user/export",
        {
          ...this.queryParams,
        },
        `user_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        "system/user/importTemplate",
        {},
        `user_template_${new Date().getTime()}.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    async addContact() {
      await addSupplierContact(this.addqueryParams);
      this.userAdds = false;
      this.handleUser();
    },
    // 管理分类相关方法
    handleManageCategory(row) {
      this.currentSupplier = row;
      this.categoryDialogVisible = true;
    },
  },
};
</script>

<style scoped>
/* 可以添加一些样式 */
</style>
