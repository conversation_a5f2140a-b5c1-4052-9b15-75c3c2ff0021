<template>
    <div class="app-container">
      <el-form
        :model="searchForm"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.alarmStatus"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="搜索">
          <el-input
            v-model="searchForm.query"
            placeholder="请输入关键字"
            clearable
            @keyup.enter.native="search(false)"
          />
        </el-form-item>


        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="search(false)"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="search(true)"
            >重置</el-button
          >
        </el-form-item>

        <el-form-item class="form-item">
          <el-button icon="el-icon-refresh" size="mini" @click="callBack()"
            >返回</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="search"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="tableData">
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column label="序号" type="index" />
        <el-table-column label="名称" prop="reinDate" />

        <el-table-column label="计划" prop="planDate">
          <template slot-scope="{row}">
            <div v-if="row.planDate">{{row.planDate | dateFormat}}</div>
          </template>
        </el-table-column>

        <el-table-column label="预计" prop="overDate">
          <template slot-scope="{row}">
            <div v-if="row.overDate">{{row.overDate | dateFormat}}</div>
          </template>
        </el-table-column>

        <el-table-column label="完成" prop="finishDate">
          <template slot-scope="{row}">
            <div v-if="row.finishDate">{{row.finishDate | dateFormat}}</div>
          </template>
        </el-table-column>

        <el-table-column label="合格" prop="certDate" width="">
          <template slot-scope="{row}">
            <div v-if="row.certDate">{{row.certDate | dateFormat}}</div>
          </template>
        </el-table-column>

        <el-table-column label="转序" prop="moveDate" width="">
          <template slot-scope="{row}">
            <div v-if="row.moveDate">{{row.moveDate | dateFormat}}</div>
          </template>
        </el-table-column>

        <el-table-column label="周期" prop="reinName" width="" />
        <el-table-column label="备注" prop="remark" width="" />
        <el-table-column label="供应商名称" prop="supplierName" width="" />
        <el-table-column label="供应商编号" prop="supplierNo" width="" />
        <el-table-column label="状态" prop="alarmStatus">
          <template slot-scope="{row}">
            <el-tag v-if="String(row.alarmStatus)" :type="row.alarmStatus | filterStatus(statusOptions,'type')">
              {{row.alarmStatus | filterStatus(statusOptions,'label')}}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="searchForm.pageNum"
        :limit.sync="searchForm.pageSize"
        @pagination="search(false)"
      />

      
    </div>
  </template>

  <script>
  import { partDetailPage } from '@/api/productionPlan/productionPlanInfo'
  export default {
    data(){
      return{
        showSearch:true,
        loading:false,
        searchForm: {
          pageNum: 1,
          pageSize: 10,
          query: '',
          alarmStatus: '',
        },
        rowId:null,
        total:0,
        statusOptions:[
          {
            label:'报警',
            value:1,
            type:'danger'
          },
          {
            label:'预警',
            value:2,
            type:'warning'
          },
          {
            label:'正常',
            value:3,
            type:'default'
          },
          {
            label:'完成',
            value:4,
            type:'success'
          }
        ],
        tableData:[],
        linkForm:{},
      }
    },

    beforeRouteLeave(to,from,next){
      let path = to.path
      switch(path){
        case '/productionPlan/productionPlan/productionPlan/index':
          to.query.searchForm = this.linkForm
          break;
      }
      next()
    },

    created(){
      this.linkForm = JSON.parse(decodeURIComponent(this.$route.query.searchForm))
      this.rowId = this.$route.query.rowId
      this.loadData()
    },

    filters:{
      filterStatus(value,statusOptions,params){
        let option = statusOptions.find((item)=>{
          return item.value === value
        })
        return option[params]
      }
    },

    methods:{
      search(flag){
        if(flag){
          this.searchForm.pageNum = 1
          this.searchForm.query = ''
          this.searchForm.alarmStatus = ''
          this.searchForm.type = ''
        }
        this.loadData()
      },


      callBack(){
        this.$route.query.pageNum = this.pageNum
        this.$router.back()
      },

      

      loadData(){
        this.loading = true
        let params = {
          ...this.searchForm,
          partId:this.rowId
        }
        partDetailPage(params).then((res)=>{
          let resData = res.data || {}
          this.tableData = resData.records || []
          this.total = resData.total
          this.loading = false
        }).catch(()=>{
          this.loading = false
        })
      },

    },

  }

  </script>

  <style scoped lang='scss'>
  .form-item {
    float:right;
  }
  </style>
