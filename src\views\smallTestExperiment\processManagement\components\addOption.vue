<template>
  <div>
    <el-dialog
      title="新增试验项"
      :visible.sync="dialogFormVisible"
      append-to-body
      width="900px"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="检验和试验项目名称" prop="projectName">
              <el-input v-model="form.projectName" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="QC控制点" prop="qcControl">
              <el-select
                style="width: 100%"
                v-model="form.qcControl"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in optionList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="HTC控制点" prop="htcControl">
              <el-select
                style="width: 100%"
                v-model="form.htcControl"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in optionList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="QS控制点" prop="qsControl">
              <el-select
                style="width: 100%"
                v-model="form.qsControl"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in optionList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检验要求及注意事项" prop="tip">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="form.tip"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="dialogFormVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      personShow: false,
      formLabelWidth: "120px",
      btnLoading: false,
      dialogFormVisible: false,
      dialogTableList: [{}],
      form: {},
      rowData: {},
      optionList: [
        { value: "R", label: "R" },
        { value: "W", label: "W" },
        { value: "H", label: "H" },
        { value: "I", label: "I" },
        { value: "V", label: "V" },
      ],
      parentIndex: null,
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: "产品名称不能为空", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    //重置
    rest() {},
    //初始化
    init(row) {
      this.dialogFormVisible = true;
      if (row) {
        this.parentIndex = row.sortIndex;
      } else {
        this.parentIndex = null;
      }
      this.form = {};
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    // 确定
    confirm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.parentIndex != null) {
            this.$parent.addbranch(this.form, this.parentIndex);
          } else {
            this.$parent.addBase(this.form);
          }
          this.dialogFormVisible = false;
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.btn-box {
  margin-bottom: 8px;
  padding-left: 36px;
  display: flex;
  align-items: center;
  .btn-label {
    width: 85px;
  }
}
.add-box {
  text-align: end;
  margin-bottom: 20px;
}
.table-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  .title {
    color: #409eff;
    font-size: 20px;
  }
  .explain {
    font-size: 12px;
  }
}
</style>
