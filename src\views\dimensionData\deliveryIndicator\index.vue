<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="交货" prop="secondType">
        <el-select style="width: 100%" v-model="queryParams.secondType" placeholder="请选择" clearable>
          <el-option v-for="item in secondCategoryList" :key="item" :label="item.label" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="搜索" prop="query">
        <el-input v-model="queryParams.query" style="width: 300px" placeholder="请输入" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="确认状态" prop="submitStatus">
        <el-select style="width: 100%" v-model="queryParams.submitStatus" placeholder="请选择" clearable>
          <el-option v-for="item in submitStatusList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="删除状态" prop="deletedStatus">
        <el-select style="width: 100%" v-model="queryParams.deletedStatus" placeholder="请选择" clearable>
          <el-option v-for="item in deletedStatusList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="责任部门" prop="deptName">
        <el-input v-model="queryParams.deptName" placeholder="请输入" clearable style="width: 100%" />
      </el-form-item>
      <el-form-item label="采购合同号" prop="contractNo" label-width="100px">
        <el-input v-model="queryParams.contractNo" placeholder="请输入" clearable style="width: 100%" />
      </el-form-item>
      <el-form-item label="责任人员" prop="inspector">
        <el-input v-model="queryParams.inspector" placeholder="请输入" clearable style="width: 240px" />
      </el-form-item>
      <el-form-item style="margin-left: 50px;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="mb8 justify-between">
      <el-row :gutter="10" class="item-center">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" size="mini" @click="exportData()">导出（已选{{ ids.length }}条）</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" size="mini" @click="exportDataAll()">导出全部</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-upload2" size="mini" @click="handleImp()">导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <!-- <el-button type="primary" icon="el-icon-upload2" size="mini"
            @click="handleEdit({}, 'batchConfirm')">批量确认</el-button> -->
        </el-col>
      </el-row>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </div>


    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange" max-height="600">
      <el-table-column type="selection" width="55" />
      <el-table-column label="序号" type="index" width="120">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="事件日期" prop="finishTime" width="120" />
      <!-- <el-table-column label="交货指标" prop="type" /> -->
      <el-table-column label="二级分类" prop="secondType" width="120" />
      <el-table-column label="分类编号" prop="typeNo" width="120" align="center">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.typeName" placement="top" v-if="scope.row.typeNo">
            <div>{{ scope.row.typeNo }}</div>
          </el-tooltip>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column label="采购合同号" prop="contractNo" width="130" align="center">
        <template slot-scope="scope">
          <div v-if="!scope.row.contractNo">-</div>
          <div v-else>{{ scope.row.contractNo }}</div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="NCR编号" prop="ncr" width="120" align="center">
        <template slot-scope="scope">
          <div v-if="!scope.row.ncr">-</div>
          <div v-else>{{ scope.row.ncr }}</div>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="项目名称" prop="projectName" show-overflow-tooltip width="200" align="center">
        <template slot-scope="scope">
          <div v-if="!scope.row.projectName">-</div>
          <div v-else>{{ scope.row.projectName }}</div>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="工作令号" prop="posid" show-overflow-tooltip width="120" align="center">
        <template slot-scope="scope">
          <div v-if="!scope.row.posid">-</div>
          <div v-else>{{ scope.row.posid }}</div>
        </template>
      </el-table-column> -->
      <el-table-column label="计划交货时间" prop="planTime" show-overflow-tooltip width="150" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.secondType !== '交货准时度'">-</div>
          <div v-else>{{ scope.row.planTime }}</div>
        </template>
      </el-table-column>
      <el-table-column label="实际交货时间" prop="finishTime" show-overflow-tooltip width="150" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.secondType !== '交货准时度'">-</div>
          <div v-else>{{ scope.row.finishTime }}</div>
        </template>
      </el-table-column>
      <el-table-column label="罚分" prop="score" />
      <el-table-column label="判罚原因" prop="reason" show-overflow-tooltip width="200" />
      <el-table-column label="确认状态" prop="submitStatus" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.secondType !== '交货准时度'">-</div>
          <el-tag :type="scope.row.submitStatus === 1 ? 'success' : 'danger'" v-else>
            {{ scope.row.submitStatus === 1 ? '已确认' : '未确认' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="删除状态" prop="deletedStatus">
        <template slot-scope="scope">
          <el-tag :type="scope.row.deletedStatus === 1 ? 'danger' : 'success'">
            {{ scope.row.deletedStatus === 1 ? '已删除' : '未删除' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="修改历史" prop="" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" v-if="scope.row.updateUserId"
            @click="historyHandle(scope.row)">有</el-button>
          <div v-else>-</div>
        </template>
      </el-table-column>

      <el-table-column label="QMS编码" prop="supplierNo" width="120" align="center">
        <template slot-scope="scope">
          <div v-if="!scope.row.supplierNo">-</div>
          <div v-else>{{ scope.row.supplierNo }}</div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="集团招采编码" prop="supplierNo100" width="120" align="center">
        <template slot-scope="scope">
          <div v-if="!scope.row.supplierNo100">-</div>
          <div v-else>{{ scope.row.supplierNo100 }}</div>
        </template>
      </el-table-column> -->
      <el-table-column label="SAP编码" prop="supplierCode" width="120" align="center">
        <template slot-scope="scope">
          <div v-if="!scope.row.supplierCode">-</div>
          <div v-else>{{ scope.row.supplierCode }}</div>
        </template>
      </el-table-column>
      <el-table-column label="供应商名称" prop="supplierName" width="200" />
      <el-table-column label="责任部门" prop="deptName" align="center" width="140">
        <template slot-scope="scope">
          <div v-if="!scope.row.deptName">-</div>
          <div v-else>{{ scope.row.deptName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="责任人员" prop="inspector" align="center" width="120" show-overflow-tooltip>
        <!-- <template slot-scope="scope">
          <div v-if="!scope.row.inspector">-</div>
          <div v-else style="width: 100%;">{{ scope.row.inspector }}</div>
        </template> -->
      </el-table-column>
      <el-table-column width="180" label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleEdit(scope.row, 'view')">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleEdit(scope.row, 'confirm')"
            v-if="scope.row.submitStatus == 0 && scope.row.deletedStatus == 0 && scope.row.secondType == '交货准时度'">确认</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row, 'edit')"
            v-if="scope.row.deletedStatus == 0 && scope.row.submitStatus == 1">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleEdit(scope.row, 'delete')"
            v-if="scope.row.deletedStatus == 0">删除</el-button>
        </template></el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <add ref="addDialog" :visible.sync="addDialogVisible" @refresh="handleQuery"></add>
    <process-import ref="processImport" @dialog="handleQuery"></process-import>
    <edit-score-dialog :visible.sync="editDialogVisible" @refresh="getList" :row="currentRow" :type="currentType" />
    <history-dialog :visible.sync="historyDialogVisible" :row="currentRow" />
  </div>
</template>

<script>
import add from "./components/add.vue";
import {
  apiGetPage,
  apiAdd,
  apiUpdate,
  apiDelete,
  apiConfirm,
  apiConfirmBatch,
  exportData
} from "@/api/dimensionData/deliveryIndicator.js";
import { apiGetSecondCategory } from "@/api/dimensionData/qualityIndicator.js";
import ProcessImport from "./components/processImport.vue";
import EditScoreDialog from "./components/edit.vue";
import HistoryDialog from "./components/historyDialog.vue";
export default {
  components: {
    add,
    ProcessImport,
    EditScoreDialog,
    HistoryDialog
  },
  data() {
    return {
      submitStatusList: [
        { label: '全部', value: '' },
        { label: '已确认', value: 1 },
        { label: '未确认', value: 0 },
      ],
      deletedStatusList: [
        { label: '全部', value: '' },
        { label: '已删除', value: 1 },
        { label: '未删除', value: 0 },
      ],
      // 当前选中行
      currentRow: {},
      // 二级分类
      secondCategoryList: [],
      // 修改历史弹出层
      historyDialogVisible: false,
      // 当前选中行id
      currentId: '',
      // 当前选中行类型
      currentType: 'view',
      editDialogVisible: false,
      // 添加弹出层
      addDialogVisible: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: undefined, //模块名称
        moduleCode: undefined, //模块编码
        posid: undefined, //令号
        post1: undefined, //机组名称
        powerPl: undefined, //业主名称（电厂）
        startTime: undefined, //开始时间
        endTime: undefined, //结束时间
        status: undefined, //状态
      },
      //机组详情
      posidList: {},
    };
  },
  created() {
    this.getList();
    this.getSecondCategory();
  },
  methods: {
    //导入
    handleImp() {
      this.$refs.processImport.init();
    },
    // 导出
    exportData() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请先选择要导出的数据");
        return;
      }
      this.download("/sd-server/delivery/export", { ids: this.ids, ...this.queryParams }, `交货指标.xlsx`);

    },
    // 导出
    exportDataAll() {
      this.download("/sd-server/delivery/export", { ...this.queryParams }, `交货指标.xlsx`);
    },
    // 获取二级分类
    getSecondCategory() {
      apiGetSecondCategory({ type: 2 }).then((res) => {
        this.secondCategoryList = res.data;
      });
    },
    // 获取修改历史
    historyHandle(row) {
      this.historyDialogVisible = true
      this.currentRow = row
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.addDialogVisible = true
    },
    handleEdit(row, type) {
      this.currentRow = row
      this.currentType = type
      this.editDialogVisible = true
    },

    // 批量启用
    openBatch() {
      this.$modal.msgError('功能开发中');
    },

    // 导入
    handleImp() {
      this.$refs.processImport.init();
    },

    /** 分页查询 */
    getList() {
      this.loading = true;
      if (this.dateRange) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
      }
      apiGetPage(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetForm() {
      this.dateRange = false
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
  },
};
</script>

<style scoped>
::v-deep .el-dialog__body {
  line-height: 36px;
}

.item-center {
  display: flex;
  align-items: center;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.font-size-14 {
  font-size: 14px;
}

.grey {
  color: #999;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
</style>
