import request from "@/utils/request";
// 分页查询
export function getTechDataPageSupplier(data) {
  return request({
    url: "/sd-server/tech/data/page/supplier",
    method: "get",
    params: data,
  });
}
// 获取部门
export function getDeptTree() {
    return request({
      url: "/back-server/dept/tree",
      method: "get",
    });
}
//获取'研发上传开启按'钮权限(0没权限,1有权限)
export function getTechDataButton(data) {
  return request({
    url: "/sd-server/tech/data/button",
    method: "get",
    params: data,
  });
}
//研发上传>开启
export function getTechDataOpen(data) {
  return request({
    url: "/sd-server/tech/data/open",
    method: "post",
    params: data,
  });
}
//保存提交
export function getTechDataSave(data) {
  return request({
    url: "/sd-server/tech/data/save",
    method: "post",
    data: data,
  });
}
//修改提交
export function getTechDataSupplier(data) {
  return request({
    url: "/sd-server/tech/data/supplier",
    method: "put",
    data: data,
  });
}
//新增时查询已开启的研发上传的年份
export function getTechDataYear() {
  return request({
    url: "/sd-server/tech/data/year",
    method: "get",
  });
}
//获取供应商
export function getTechDataFile(data) {
    return request({
      url: "/sd-server/tech/data/file",
      method: "get",
      params: data,
    });
}
//存草稿
export function postTechDataSaveDraft(data) {
  return request({
    url: "/sd-server/tech/data/save/draft",
    method: "post",
    data: data,
  });
}
//详情
export function getTechDataOneSupplier(data) {
  return request({
    url: "/sd-server/tech/data/one/supplier",
    method: "get",
    params: data,
  });
}