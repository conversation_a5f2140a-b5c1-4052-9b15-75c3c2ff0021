<template>
  <el-dialog title="新增" :visible.sync="dialogVisible" width="800px" :before-close="handleClose">
    <el-form :model="form" :rules="rules" ref="form" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="厂商名称" prop="supplierName">
            <el-input v-model="form.supplierName" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="厂商编码" prop="supplierCode">
            <el-input v-model="form.supplierCode" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分类编号" prop="typeNo">
            <el-select v-model="form.typeNo" placeholder="请选择" style="width: 100%" filterable>
              <el-option v-for="(item, index) in typeNoList" :key="index" :label="item.typeNo" :value="item.typeNo">
                <template #default>
                  <el-tooltip :content="item.typeName" placement="top">
                    <span>{{ item.typeNo }}</span>
                  </el-tooltip>
                </template>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="招标编号" prop="bidNo">
            <el-input v-model="form.bidNo" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同编号" prop="contractNo">
            <el-input v-model="form.contractNo" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同额" prop="contractPrice">
            <el-input v-model="form.contractPrice" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="合同签订时间" prop="signingDate">
            <el-date-picker v-model="form.signingDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { apiAdd, apiGetSecondCategory, apiGetAllTypeNo } from '@/api/dimensionData/contract'
import { apiGetTypeNoList } from '@/api/appraiseManager/appraiseAll'
import { QualitySecondaryCategory, getQualityTipText } from '@/enums/qualityIndicator'
export default {
  name: 'AddDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      secondCategoryList: [],
      typeNoList: [],
      dialogVisible: false,
      form: {
        supplierCode: '',
        supplierName: '',
        typeNo: '',
        bidNo: '',
        contractNo: '',
        contractPrice: '',
        signingDate: new Date().toISOString().split('T')[0],
      },
      rules: {
        supplierCode: [{ required: true, message: '请输入供方编号', trigger: 'blur' }],
        supplierName: [{ required: true, message: '请输入供方名称', trigger: 'blur' }],
        typeNo: [{ required: true, message: '请输入分类编号', trigger: 'blur' }],
        bidNo: [{ required: true, message: '请输入招标编号', trigger: 'blur' }],
        contractNo: [{ required: true, message: '请输入采购合同号', trigger: 'blur' }],
        contractPrice: [{ required: true, message: '请输入合同额', trigger: 'blur' }],
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.getAllTypeNo()
          this.getSecondCategory()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    getTipText() {
      return getQualityTipText(this.form.secondType)
    },
    // 获取全部的分类编号
    getAllTypeNo() {
      apiGetTypeNoList().then((response) => {
        this.typeNoList = response.data
      })
    },
    // 获取二级分类
    getSecondCategory() {
      apiGetSecondCategory({ type: 1 }).then((response) => {
        this.secondCategoryList = response.data
      })
    },
    handleClose() {
      this.$refs.form.resetFields()
      this.dialogVisible = false
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            await apiAdd(this.form)
            this.$message.success('保存成功')
            this.$emit('refresh')
            this.handleClose()
          } catch (error) {
            this.$message.error(error.message || '保存失败')
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.tip-text {
  color: #409EFF;
  font-size: 12px;
  margin-bottom: 20px;
  padding: 0 20px;
  display: flex;
}

.text-nowrap {
  white-space: nowrap;
}

.el-form {
  padding: 0 20px;
}
</style>
