<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="搜索" prop="query">
        <el-input
          v-model="queryParams.query"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postList">
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column label="角色" width="200" prop="userName" />
      <el-table-column label="部门" prop="deptName" />
      <el-table-column label="部门领导" prop="leaderUserName" />
      <el-table-column
        label="操作"
        width="200"
        align="center"
        class-name="small-padding fixed-width"
      > 
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDept(scope.row)"
            >修改部门</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleLead(scope.row)"
            >部门领导</el-button
          >

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <dept-people ref="deptPeople" @getList="getList"></dept-people>
    <modify-dept ref="modifyDept" @getList="getList"></modify-dept>

    <lead-people ref="leadPeople" @getList="getList"></lead-people>
  </div>
</template>

<script>
import {
  getDutyUserPage,
  delDutyUser,
} from "@/api/riskControl/peopleVindicate";
import deptPeople from './components/deptPeople'
import LeadPeople from './components/leadPeople.vue';
import ModifyDept from './components/modifyDept.vue';
export default {
  name: "Post",
  components:{deptPeople,LeadPeople, ModifyDept},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      selectedList: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        query: "",
        pageNum: 1,
        pageSize: 10,
      },
      //机组详情
      posidList: {},
      // 表单参数
      form: {
        checkList: [],
      },
      dialogTableList: [],
      dialogQueryParams: {
        keyword: "",
        page: 1,
        size: 10,
        enable: 1,
      },
      dialogLoading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //创建时间处理
    timeChange() {
      this.queryParam.startTime = this.dateRange[0];
      this.queryParam.endTime = this.dateRange[1];
    },
    // 部门人员列表
    getList() {
      this.loading = true;
      getDutyUserPage(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.deptPeople.init()
    },

    // 修改部门
    handleDept(row){
      this.$refs.modifyDept.init(row)
    },

    // 部门领导
    handleLead(row){
      this.$refs.leadPeople.init(row)
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      let ids = [row.id];
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delDutyUser(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("操作成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    // 查看部套跳转 需要带参数 跳转到部套管理  根据参数查询
    // toItems(){
    //  this.$router.push("/items/Department");

    // },
    // 部套数跳转  需要带参数 跳转到部套管理  根据参数查询
    toItems(posid, moduleCode) {
      localStorage.setItem("posid", posid);
      localStorage.setItem("moduleCode", moduleCode);
      this.$router.push({
        path: "/items/items/Department/index",
      });
    },
  },
};
</script>

<style scoped>
::v-deep .el-dialog__body {
  line-height: 36px;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}
.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
::v-deep .el-row--flex {
  margin-left: 22px;
}
/* ::v-deep .el-dialog {
  margin-top: 30vh !important;
} */
</style>
