<template>
    <el-dialog
    title="新增节点记录"
    :visible.sync="dialogVisible"
    width="50%"
    @close="cancel"
    >
        <div class="">
            <p class="time">规定完成时间：{{rowData.planTime | dateFormat}}</p>
            <el-form :model="form" :rules="rules" ref="form" label-width="50px" class="demo-ruleForm">
                <el-form-item label="备注" prop="record">
                    <el-input type="textarea" :rows="5" v-model="form.record"></el-input>
                </el-form-item>
                <el-form-item label="图片" prop="imageList">
                    <el-upload
                    :action="uploadUrl"
                    :headers="headers"
                    :file-list="fileList"
                    :before-upload="beforeUpload"
                    :on-success="successUpload"
                    list-type="picture-card"
                    class="el-upload-container"
                    >
                        <i slot="default" class="el-icon-plus" v-loading="btnLoading"></i>
                        <div slot="file" slot-scope="{file}" v-loading="btnLoading">
                        <img
                            class="el-upload-list__item-thumbnail"
                            :src="file.url" alt=""
                        >
                        <span class="el-upload-list__item-actions">
                            <span
                                class="el-upload-list__item-delete"
                                @click="handleRemove(file)"
                                >
                                <i class="el-icon-delete"></i>
                            </span>
                        </span>
                        </div>
                    </el-upload>
                </el-form-item>
            </el-form>
        </div>

        <span slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import uploadFile from '@/components/uploadFile/main'
import { detailNodeRecord } from '@/api/suppilerProductionPlan/productionPlan'
import { getToken } from "@/utils/auth";
export default {
  components:{uploadFile},

  data(){
    return{
        dialogVisible:false,
        btnLoading:false,
        fileList:[],
        headers: {
            "X-Token": "Bearer " + getToken(),
        },
        baseImg:process.env.VUE_APP_IMAGE_API,
        uploadUrl: process.env.VUE_APP_BASE_API + "/user-server/file/upload",
        form:{
            record:'',
            imageList:[]
        },
        suppilerInfo:{},
        rowData:{},
        rules:{}
    }
  },

  methods:{
    init(row){
        this.dialogVisible = true
        this.btnLoading = false
        this.form = {
            record:'',
            imageList:[]
        }
        this.rowData = row
    },

    beforeUpload(){
        this.btnLoading = true
    },

    successUpload(response,file){
        this.fileList.push(file)
        this.form.imageList.push(response.data)
        this.btnLoading = false
    },

    handleRemove(file){
        let id = file.response.data.id
        let fileIndex = this.fileList.findIndex((item)=>{
            return item.uid === file.uid
        })
        let findIndex = this.form.imageList.findIndex((item)=>{
            console.log('item',item.id)
            return item.id === id
        })
        this.fileList.splice(fileIndex,1)
        this.form.imageList.splice(findIndex,1)
    },

    confirm(){
        if(!this.form.record && this.form.imageList.length === 0){
            this.$message({
                type:'warning',
                message:'请填写数据',
                duration:2000
            })
            return
        }
        let params = {
            ...this.form,
            partId:this.rowData.partId,
            nodeId:this.rowData.id
        }
        this.btnLoading = true
        detailNodeRecord(params).then((res)=>{
            this.$message({
                type:'success',
                message:'操作成功',
                duration:1500
            })
            this.btnLoading = false
            this.$emit('loadData')
            this.dialogVisible = false
        }).catch(()=>{
            this.btnLoading = false
        })
    },


    cancel(){
        this.dialogVisible = false
        this.fileList = []
    }
  },

}

</script>

<style scoped lang='scss'>
    ::v-deep.el-upload-container {
        height:148px;
        .el-upload-list {
            height:148px;
            .el-upload-list__item {
                height:100%;
                margin-bottom:0;
                >div{
                    height:100%;
                }
            }
        }
        .el-upload-list__item-thumbnail {
            height:148px;
        }
    }
    .el-dialog__body {
        padding-top:15px;
    }
    .time {
        margin-bottom:15px;
    }
</style>
