<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="状态" prop="status" label-width="40px">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable>
          <el-option
            v-for="dict in statusList"
            :key="dict.value"
            :label="dict.text"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="风险等级" prop="riskLevel" label-width="80px">
        <el-select
          v-model="queryParams.riskLevel"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in riskLevelList"
            :key="dict.value"
            :label="dict.text"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="query">
        <el-input
          v-model="queryParams.query"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column label="序号" type="index" />
      <el-table-column label="责任方" prop="dutyName" />
      <el-table-column label="责任部门" prop="dutyUserName" />
      <el-table-column label="责任供应商" prop="alarmStatus">
        <template slot-scope="scope">
          {{
            scope.row.mesProjectRiskSupplier
              ? scope.row.mesProjectRiskSupplier.supplierName
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column label="涉及产品" prop="productName" />
      <el-table-column label="风险等级" prop="riskLevel">
        <template slot-scope="scope">
          {{ listFind(riskLevelList, scope.row.riskLevel).text }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="300"
        label="风险点描述"
        prop="riskDesc"
      >
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            width="200"
            trigger="click"
            :content="scope.row.riskDesc"
          >
            <el-button slot="reference" type="text">
              <div class="over-text">{{ scope.row.riskDesc }}</div>
            </el-button>
          </el-popover>
          <!-- {{ scope.row.riskDesc }} -->
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createUserName" />
      <el-table-column label="创建时间" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" prop="reviewTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.reviewTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status">
        <template slot-scope="scope">
          {{ listFind(statusListTwo, scope.row.status).text }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="140"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="showItem(scope.row)"
            >反馈</el-button
          >
        </template></el-table-column
      >
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 项目名称详情弹框 -->
    <el-dialog
      title="项目详情"
      :visible.sync="itemOpen"
      width="800px"
      append-to-body
    >
      <div v-loading="detailLoading">
        <el-row>
          <el-col :span="8"
            ><div class="grid-content bg-purple">
              <span
                ><span class="content-label">责任方：</span
                >{{ riskData.dutyName }}</span
              >
            </div></el-col
          >
          <el-col :span="8"
            ><div class="grid-content bg-purple-light">
              <span
                ><span class="content-label">涉及产品：</span
                >{{ riskData.productName }}</span
              >
            </div></el-col
          >
          <el-col :span="8"
            ><div class="grid-content bg-purple-light">
              <span
                ><span class="content-label">风险登记：</span
                ><span>{{
                  listFind(riskLevelList, riskData.riskLevel)
                    ? listFind(riskLevelList, riskData.riskLevel).text
                    : ""
                }}</span></span
              >
            </div></el-col
          >
        </el-row>

        <el-row class="row-bg">
          <el-col :span="24"
            ><div class="grid-content bg-purple">
              <div class="content-label">风险点描述：</div>
              <span>{{ riskData.riskDesc }}</span>
            </div></el-col
          >
          <el-col :span="24"
            ><div class="grid-content bg-purple-light">
              <div class="content-label">原因分析：</div>
              <span>{{ riskData.riskReason }}</span>
            </div></el-col
          >
          <el-col :span="24"
            ><div class="grid-content bg-purple-light">
              <div class="content-label">保证措施：</div>
              <span>{{ riskData.measuresDesc }}</span>
            </div></el-col
          >
        </el-row>

        <div class="block">
          <div class="content-label">反馈内容：</div>
          <el-timeline>
            <el-timeline-item
              color="#1890FF"
              v-if="riskData.mesProjectRiskSupplierList"
            >
              <div class="timeline-item-top">
                <span>厂商反馈</span>
                <!-- <span>已完成</span> -->
              </div>
              <div class="">
                <div class="timeline-item-card" v-for="(aItem,aIndex) in riskData.mesProjectRiskSupplierList" :key="aIndex">
                  <div class="card-left">
                    <p>{{ aItem.supplierName }}</p>
                    <p
                      v-if="
                        aItem.status == 2 ||
                        aItem.status == 4
                      "
                    >
                      驳回原因：{{ aItem.rejectDesc }}
                    </p>

                    <div
                      class="remark-inp"
                      v-if="
                        aItem.userPermission === 1
                      "
                    >
                      <span class="remark-txt">回复：</span>
                      <el-input
                        type="textarea"
                        autosize
                        placeholder="请输入内容"
                        v-model="aItem.feedbackDesc"
                      >
                      </el-input>
                    </div>

                    <p v-else>
                      回复：{{
                        aItem.feedbackDesc || "待回复"
                      }}
                    </p>

                    <!-- <el-dialog :visible.sync="dialogVisible" append-to-body>
                      <img width="100%" :src="dialogImageUrl" alt="" />
                    </el-dialog> -->

                    <div
                      v-if="
                        aItem.userPermission === 1
                      "
                    >
                      <el-upload
                        class="upload-demo"
                        multiple
                        :action="uploadUrlFile"
                        :headers="headers"
                        :on-preview="handlePreview"
                        :on-remove="handleRemove"
                        :on-success="handleSuccess"
                        :file-list="fileList"
                      >
                        <el-button size="small" type="primary"
                          >上传文件</el-button
                        >
                      </el-upload>
                    </div>

                    <div class="file-box" v-else>
                      <div v-for="(item, index) in fileList" :key="index">
                        <el-button type="text" @click="handlePreview(item)">{{
                          item.name
                        }}</el-button>
                      </div>
                    </div>
                  </div>

                  <div
                    class="btn-box"
                    v-if="aItem.userPermission === 1"
                  >
                    <el-button
                      @click="submitFeedback(aItem)"
                      type="primary"
                      size="small"
                      >提交</el-button
                    >
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getRiskSupplierPage,
  getProjectRiskSupplier,
  addSupplierFeedback,
} from "@/api/riskControl/projectRiskListBySup";
import { getToken } from "@/utils/auth";

export default {
  name: "Post",
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/user-server/file/upload",
      uploadUrlFile:
        process.env.VUE_APP_BASE_API + "/user-server/file/uploadFile",
      headers: { "X-Token": "Bearer " + getToken() },
      dialogImageUrl: "",
      dialogVisible: false,
      imageList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        query: "",
        pageNum: 1,
        pageSize: 10,
        status: undefined, //状态
        riskLevel: undefined, // 风险等级
      },
      riskLevelList: [
        {
          text: "严重",
          value: 0,
        },
        {
          text: "一般",
          value: 1,
        },
        {
          text: "轻微",
          value: 2,
        },
      ],
      statusList: [
        {
          text: "待部门反馈",
          value: 1,
        },
        {
          text: "待供应商反馈",
          value: 2,
        },
        {
          text: "待审核",
          value: 3,
        },
        {
          text: "已完成",
          value: 4,
        },
      ],
      statusListTwo: [
        {
          text: "待部门反馈",
          value: 1,
        },
        {
          text: "待供应商反馈",
          value: 2,
        },
        {
          text: "待审核",
          value: 3,
        },
        {
          text: "已完成",
          value: 4,
        },
      ],
      //机组详情
      riskData: {},
      detailLoading: false,

      // 文件列表
      fileList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 点击文件
    handlePreview(file) {
      console.log(file);
      window.open(
        process.env.VUE_APP_FILE_API + file.response.data.newName,
        "_blank"
      );
    },

    // 删除文件
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },

    // 上传文件成功
    handleSuccess(response, file, fileList) {
      this.fileList = fileList;
    },

    listFind(list = [], value) {
      let index = list.findIndex((item) => {
        return item.value == value;
      });
      if (index > -1) {
        return list[index];
      }
    },

    /** 下载模板操作 */
    importTemplate() {
      // downloadRisk().then((response) => {
      //   const url = window.URL.createObjectURL(new Blob([response]));
      //   const link = document.createElement("a");
      //   link.target = "_blank";
      //   link.href = url;
      //   link.setAttribute("download", "铸锻件节点模板.xlsx");
      //   document.body.appendChild(link);
      //   link.click();
      // });
      this.download(
        "mes-server/project/risk/import/download",
        {
          ...this.queryParams,
        },
        "项目风险控制导入模板.xlsx"
      );
    },

    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },

    //图片上传
    handleFileSuccess(response, file, fileList) {
      this.imageList = fileList;
    },

    // 删除成功
    fileRemove(file, fileList) {
      this.imageList = fileList;
    },

    //创建时间处理
    timeChange() {
      this.queryParam.startTime = this.dateRange[0];
      this.queryParam.endTime = this.dateRange[1];
    },
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getRiskSupplierPage(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 项目名称弹窗
    showItem(row) {
      this.imageList = [];
      this.getProjectRiskSupplier(row.id);
      this.itemOpen = true;
    },

    getProjectRiskSupplier(id) {
      this.imageList = [];
      this.detailLoading = true;
      getProjectRiskSupplier(id).then((res) => {
        this.detailLoading = false;
        this.riskData = res.data;

        if (res.data.mesProjectRiskSupplierList) {
          res.data.mesProjectRiskSupplierList.forEach(item =>{
            if(item.feedbackImage){
              let list = JSON.parse(item.feedbackImage);
              this.imageList = list.map((aItem) => {
                return {
                  url: process.env.VUE_APP_IMAGE_API + aItem,
                  response: {
                    data: {
                      newName: aItem,
                    },
                  },
                };
              });
            }

            if(item.feedbackFile){
              let fileAry = JSON.parse(item.feedbackFile);
              this.fileList = fileAry.map((bItem) => {
                return {
                  name: bItem.name,
                  response: {
                    data: {
                      name: bItem.name,
                      newName: bItem.newName,
                      url: "/file/",
                    },
                  },
                };
              });
            }

          })
        }

      });
    },

    submitFeedback(val) {
      this.detailLoading = true;
      let feedbackImage = this.imageList.map((item) => {
        return item.response.data.newName;
      });
      let feedbackFile = this.fileList.map((item) => {
        return {
          name: item.response.data.name,
          newName: item.response.data.newName,
        };
      });
      addSupplierFeedback({
        feedbackDesc: val.feedbackDesc,
        feedbackImage: JSON.stringify(feedbackImage),
        feedbackFile: JSON.stringify(feedbackFile),
        id: val.id,
      }).then((res) => {
        this.detailLoading = false;
        this.$modal.msgSuccess("操作成功");
        this.getProjectRiskSupplier(this.riskData.id);
      });
    },
  },
};
</script>

<style scoped lang="scss">
.remark-inp {
  display: flex;
  width: 100%;
  align-items: flex-start;
  .remark-txt {
    width: 50px;
  }
}

.img-box {
  display: flex;
  img {
    cursor: pointer;
    width: 146px;
    height: 146px;
    margin-right: 12px;
    border-radius: 8px;
  }
}
.upload-box {
  margin-top: 20px;
}
.over-text {
  font-size: 13px;
  width: 290px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.content-label {
  font-weight: 700;
}
::v-deep .el-dialog__body {
  line-height: 36px;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}
.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
::v-deep .el-row--flex {
  margin-left: 22px;
}
// ::v-deep .el-dialog {
//   margin-top: 30vh !important;
// }
.timeline-item-top {
  display: flex;
  justify-content: space-between;
  background-color: #f3f6fd;
  padding: 4px 12px;
  border-radius: 4px;
}
.timeline-item-card {
  margin-top: 8px;
  background-color: #f6f7f9;
  padding: 4px 12px 10px;
  border-radius: 4px;
  display: flex;
  box-sizing: border-box;
  .card-left {
    width: 80%;
  }
}

.btn-box {
  margin-top: 10px;
  width: 20%;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
</style>
