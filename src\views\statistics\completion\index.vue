<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <!-- <el-form-item label="日期">
        <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" />
      </el-form-item> -->
      <el-form-item label="搜索">
        <el-input v-model="queryParams.query" placeholder="请输入关键字" clearable style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :offset="1" :span="3">
        <el-card shadow="hover" class="statistics-card pink">
          <div class="card-title">零件的总数量</div>
          <div class="card-number">{{ summaryData.castingNum }}<span class="unit">个</span></div>
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card shadow="hover" class="statistics-card orange">
          <div class="card-title">完成数量</div>
          <div class="card-number">{{ summaryData.finishNum }}<span class="unit">个</span></div>
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card shadow="hover" class="statistics-card green">
          <div class="card-title">报警数量</div>
          <div class="card-number">{{ summaryData.alarmNum }}<span class="unit">个</span></div>
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card shadow="hover" class="statistics-card blue">
          <div class="card-title">进行中</div>
          <div class="card-number">{{ summaryData.normalNum }}<span class="unit">个</span></div>
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card shadow="hover" class="statistics-card cyan">
          <div class="card-title">完成率</div>
          <div class="card-number">{{ summaryData.finishRate }}<span class="unit">%</span></div>
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card shadow="hover" class="statistics-card purple">
          <div class="card-title">按期完成率</div>
          <div class="card-number">{{ summaryData.onTimeFinishRate }}<span class="unit">%</span></div>
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card shadow="hover" class="statistics-card pink-light">
          <div class="card-title">按期完成数量</div>
          <div class="card-number">{{ summaryData.onTimeFinishNum }}<span class="unit">个</span></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="tableData" style="width: 100%; margin-top: 20px"
      @sort-change="handleSortChange">
      <el-table-column label="序号" type="index" width="60" align="center">
        <template slot-scope="scope">
          {{ queryParams.pageSize * (queryParams.pageNum - 1) + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="厂商名称" prop="supplierName" align="center" width="200" show-overflow-tooltip />
      <el-table-column label="厂商编号" prop="supplierCode" align="center" />
      <el-table-column label="铸钢件数量" prop="castingNum" align="center" sortable="custom" />
      <el-table-column label="报警数量" prop="alarmNum" align="center" sortable="custom" />
      <el-table-column label="进行中数量" prop="normalNum" align="center" sortable="custom" />
      <el-table-column label="完成数量" prop="finishNum" align="center" sortable="custom" />
      <el-table-column label="完成率" prop="finishRate" align="center" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.finishRate }}%
        </template>
      </el-table-column>
      <el-table-column label="按期完成率" prop="onTimeFinishRate" align="center" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.onTimeFinishRate }}%
        </template>
      </el-table-column>
      <el-table-column label="按期完成数量" prop="onTimeFinishNum" align="center" sortable="custom" />
    </el-table>

    <!-- 添加分页组件 -->
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
      :current-page="queryParams.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="total" style="margin-top: 20px; text-align: right" />
  </div>
</template>

<script>
import { getSupplierCompletionList, getSupplierCompletionSummary } from '@/api/supplier/supply'
export default {
  name: 'SupplierCompletion',
  data() {
    return {
      loading: false,
      queryParams: {
        dateRange: [],
        query: '',
        pageNum: 1,
        pageSize: 10,
        sort: '',
        sortType: ''
      },
      tableData: [],
      summaryData: {},
      total: 0
    }
  },
  created() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      Promise.all([
        this.getSupplierCompletionList(),
        this.getSupplierCompletionSummary()
      ]).finally(() => {
        this.loading = false
      })
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.queryParams = {
        dateRange: [],
        query: '',
        pageNum: 1,
        pageSize: 10,
        sort: '',
        sortType: ''
      }
      this.handleQuery()
    },
    getSupplierCompletionList() {
      const params = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        query: this.queryParams.query,
        startTime: this.queryParams.dateRange && this.queryParams.dateRange[0],
        endTime: this.queryParams.dateRange && this.queryParams.dateRange[1],
        sort: this.queryParams.sort,
        sortType: this.queryParams.sortType
      }
      return getSupplierCompletionList(params).then(res => {
        this.tableData = res.data.records
        this.total = res.data.total
      })
    },
    getSupplierCompletionSummary() {
      const params = {
        query: this.queryParams.query,
        startTime: this.queryParams.dateRange && this.queryParams.dateRange[0],
        endTime: this.queryParams.dateRange && this.queryParams.dateRange[1]
      }
      return getSupplierCompletionSummary(params).then(res => {
        this.summaryData = res.data
      })
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.handleQuery()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.handleQuery()
    },
    handleSortChange(column) {
      if (column.prop) {
        this.queryParams.sortType = column.prop
        this.queryParams.sort = column.order === 'ascending' ? '1' : '2'
        this.handleQuery()
      }
    }
  }
}
</script>

<style scoped>
.statistics-cards {
  margin: 20px auto;
  display: flex;
  justify-content: center;
}

.statistics-card {
  background-color: #fff;
  border-radius: 4px;
}

.card-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.card-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.unit {
  font-size: 14px;
  margin-left: 4px;
}

/* 卡片背景色 */
.pink {
  background-color: #fef0f0;
}

.orange {
  background-color: #fdf6ec;
}

.green {
  background-color: #f0f9eb;
}

.blue {
  background-color: #ecf5ff;
}

.cyan {
  background-color: #e1f3f1;
}

.purple {
  background-color: #f5f0fa;
}

.pink-light {
  background-color: #feeff1;
}
</style>
