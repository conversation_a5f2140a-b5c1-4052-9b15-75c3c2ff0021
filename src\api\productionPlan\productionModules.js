import request from '@/utils/request'
// 列表查询
export function amountList(param) {
    return request({
        url: '/mes-server/part/amount/list',
        method: 'get',
        params: param,
    })
}

// 新增
export function addPartAmount(param) {
    return request({
        url: '/mes-server/part/amount',
        method: 'post',
        data: param,
    })
}

// 修改
export function modifyPartAmount(param) {
    return request({
        url: '/mes-server/part/amount',
        method: 'put',
        data: param,
    })
}

// 删除
export function deletePartAmount(param) {
    return request({
        url: '/mes-server/part/amount',
        method: 'delete',
        data: param,
    })
}

// 导入
export function amountFile(param) {
    return request({
        url: '/mes-server/part/amount/file',
        method: 'post',
        data: param,
    })
}

// 查询
export function getDetailPage(param) {
    return request({
        url: '/mes-server/amount/detail/page',
        method: 'get',
        params: param,
    })
}
// 新增
export function addDetail(param) {
    return request({
        url: '/mes-server/amount/detail',
        method: 'post',
        data: param,
    })
}
// 修改
export function editDetail(param) {
    return request({
        url: '/mes-server/amount/detail',
        method: 'put',
        data: param,
    })
}
// 删除
export function deltDetailData(param) {
    return request({
        url: '/mes-server/amount/detail',
        method: 'delete',
        params: param,
    })
}
