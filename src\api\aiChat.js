import axios from "axios";
import CryptoJS from "crypto-js";
import { Message } from "element-ui";

// 创建一个专门用于AI接口的axios实例
const aiAxios = axios.create({
  baseURL: "http://10.20.100.253",
  timeout: 30000, // 30秒超时
});

// 响应拦截器
aiAxios.interceptors.response.use(
  (response) => {
    // 如果返回的是数据就直接返回数据
    if (response.data && response.data.code === "0000") {
      return response.data;
    } else {
      Message.error(response.data.msg);
      return Promise.reject(new Error(response.data.msg));
    }
  },
  (error) => {
    // 处理网络错误、超时等情况
    const errorMsg = error.message || "网络异常，请检查网络连接";
    console.error("网络请求错误:", errorMsg);
    Message.error(errorMsg);
    return Promise.reject(new Error(errorMsg));
  }
);

// Token存储的key
const TOKEN_KEY = "aiChatToken";

// 从本地存储获取Token
export function getStoredToken() {
  return localStorage.getItem(TOKEN_KEY) || "";
}

// 生成Token并存储
export async function generateToken() {
  const appKey = "5gGwfQJt";
  const secretKey = "5ca0f06d8c5499869d80ed54c3f19029f9c204ee";
  const timestamp = Date.now().toString();

  // 生成sign参数，按照Java示例的逻辑
  let signStr = appKey + secretKey + timestamp;
  // 使用crypto-js进行MD5加密
  let sign = CryptoJS.MD5(signStr).toString();

  // 构造JSON字符串
  const jsonStr = `{"appKey":"${appKey}","timestamp":"${timestamp}","sign":"${sign}"}`;

  // 生成CUSWRITER_TOKEN - Base64编码
  const CUSWRITER_TOKEN = btoa(unescape(encodeURIComponent(jsonStr)));

  try {
    const res = await axios({
      url: "/spec-apps/api/app/rpachat/cuswriter/web/generateToken",
      method: "post",
      headers: {
        "CUSWRITER-TOKEN": CUSWRITER_TOKEN,
        "Content-Type": "application/json",
      },
      data: {
        appKey: appKey,
        timestamp: timestamp,
        sign: sign,
      },
    });
    const response = res.data;
    if (response.code == "0000" && response.data && response.data.token) {
      // 将Token存入本地存储
      localStorage.setItem(TOKEN_KEY, response.data.token);
    }

    return response;
  } catch (error) {
    console.error("获取Token失败：", error);
    return { code: 500, msg: "获取Token失败" };
  }
}

// 创建问题（开始新会话）
export function createQuestion(data, token = getStoredToken()) {
  return axios({
    url: "/spec-apps/api/app/rpachat/createQuestion",
    method: "post",
    headers: {
      Authorization: token,
      rpaUserAgent: "cuswriter",
      "Content-Type": "application/json",
    },
    data,
  });
}

// 提问问题（发送消息并获取回复）
export function askQuestion(
  data,
  onMessage,
  onError,
  token = getStoredToken()
) {
  // 使用fetch API的流式响应功能替代EventSource
  // 这样可以在header中传入token

  // 创建一个控制器，用于在需要时取消请求
  const controller = new AbortController();
  const signal = controller.signal;

  // 构建URL
  const url = new URL(
    "/spec-apps/api/app/rpachat/askQuestion",
    window.location.origin
  );

  // 添加查询参数
  url.searchParams.append("mappingId", data.mappingId);
  url.searchParams.append("question", data.question);

  // 设置请求选项
  const fetchOptions = {
    method: "GET",
    headers: {
      Authorization: token,
      rpaUserAgent: "cuswriter",
      "Content-Type": "application/json",
      Accept: "text/event-stream",
    },
    signal: signal,
  };

  // 创建一个缓冲区来存储不完整的数据
  let buffer = "";
  // 字节缓冲区，用于处理跨数据包的Unicode字符
  let byteBuffer = new Uint8Array(0);
  // 创建解码器，保持状态
  const decoder = new TextDecoder("utf-8", { fatal: false, stream: true });

  // 发起fetch请求
  fetch(url.toString(), fetchOptions)
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP 错误! 状态码: ${response.status}`);
      }

      // 获取响应的读取流
      const reader = response.body.getReader();

      // 创建一个函数来处理流数据
      function processStream() {
        return reader.read().then(({ done, value }) => {
          // 如果流结束了
          if (done) {
            // 处理最后可能的字节
            if (byteBuffer.length > 0) {
              const lastChunk = decoder.decode(byteBuffer, { stream: false });
              buffer += lastChunk;
            }

            // 处理缓冲区中可能剩余的数据
            if (buffer.trim() !== "") {
              console.log("处理缓冲区中的最后数据", buffer);
              processBuffer(buffer);
            }
            return;
          }

          // 将接收到的字节添加到字节缓冲区
          const newByteBuffer = new Uint8Array(byteBuffer.length + value.length);
          newByteBuffer.set(byteBuffer);
          newByteBuffer.set(value, byteBuffer.length);
          byteBuffer = newByteBuffer;

          // 解码字节缓冲区，保持流状态
          const chunk = decoder.decode(byteBuffer, { stream: true });

          // 更新字节缓冲区，只保留可能是不完整Unicode字符的部分
          // 通常是最后几个字节
          const remainingBytes = Math.min(6, byteBuffer.length); // 保留最多6个字节，足够处理大多数Unicode字符
          byteBuffer = byteBuffer.slice(byteBuffer.length - remainingBytes);

          // 将新数据添加到文本缓冲区
          buffer += chunk;

          // 处理缓冲区中的数据
          processBuffer(buffer);

          // 继续处理流
          return processStream();
        });
      }

      // 处理缓冲区中的数据
      function processBuffer(data) {
        try {
          // 尝试查找完整的数据块
          const dataBlocks = data.split("\n\n");
          let processedLength = 0;

          for (let i = 0; i < dataBlocks.length; i++) {
            const block = dataBlocks[i];
            if (!block.trim()) {
              processedLength += block.length + 2; // +2 是因为\n\n
              continue;
            }

            // 如果是最后一个块且不以\n\n结尾，可能是不完整的
            if (i === dataBlocks.length - 1 && !data.endsWith("\n\n")) {
              break; // 保留在缓冲区中
            }

            if (block.startsWith("data:")) {
              try {
                const jsonStr = block.substring(5).trim();
                const eventData = JSON.parse(jsonStr);

                // 处理可能的文字重复问题
                const cleanData = cleanRepeatedChars(eventData);

                if (onMessage && typeof onMessage === "function") {
                  onMessage(cleanData);
                }

                // 记录已处理的长度
                processedLength += block.length + 2; // +2 是因为\n\n
              } catch (e) {
                console.log("解析JSON失败，可能是不完整的数据", block);
                // 如果不是最后一块，但解析失败，仍然标记为已处理
                if (i < dataBlocks.length - 1) {
                  processedLength += block.length + 2;
                }
              }
            } else {
              // 非data:开头的块，直接跳过
              processedLength += block.length + 2;
            }
          }

          // 移除已处理的数据
          if (processedLength > 0) {
            buffer = buffer.substring(processedLength);
          }
        } catch (error) {
          console.error("处理缓冲区数据失败:", error);
          // 不将解析错误传给回调，避免中断流处理
        }
      }

      // 清理数据中可能重复的文字
      function cleanRepeatedChars(data) {
        if (!data || typeof data !== 'object') return data;

        try {
          // 处理answer部分
          if (data.data && data.data.answerList && data.data.answerList.length > 0) {
            data.data.answerList.forEach(answer => {
              // 处理答案部分
              if (answer.answerPart && typeof answer.answerPart === 'string') {
                answer.answerPart = removeRepeatedChars(answer.answerPart);
                answer.answerPart = fixConsecutiveChineseChars(answer.answerPart);
              }

              // 处理思考部分
              if (answer.reasoningPart && typeof answer.reasoningPart === 'string') {
                answer.reasoningPart = removeRepeatedChars(answer.reasoningPart);
                answer.reasoningPart = fixConsecutiveChineseChars(answer.reasoningPart);
              }

              // 处理其他可能的字段
              if (answer.target_language && typeof answer.target_language === 'string') {
                answer.target_language = removeRepeatedChars(answer.target_language);
                answer.target_language = fixConsecutiveChineseChars(answer.target_language);
              }
            });
          }

          return data;
        } catch (e) {
          console.error('清理重复字符时出错:', e);
          return data; // 出错时返回原始数据
        }
      }

      // 专门修复连续汉字中第二个汉字可能乱码的问题
      function fixConsecutiveChineseChars(text) {
        if (!text) return text;

        try {
          // 使用正则表达式查找中文字符后跟着不正常的Unicode字符的模式
          // 这通常表示第二个字符被错误编码
          const result = text.replace(/([^\x00-\xff])[\uFFFD\u{FFFD}]/gu, '$1');

          // 处理常见的连续两个汉字中第二个乱码的情况
          // 查找模式：一个正常汉字后跟一个高位代理字符但缺少低位代理字符
          return result.replace(/([^\x00-\xff])[\uD800-\uDBFF](?![\uDC00-\uDFFF])/g, '$1');
        } catch (e) {
          console.error('修复连续汉字时出错:', e);
          return text; // 发生错误时返回原始文本
        }
      }

      // 移除字符串中重复的文字
      function removeRepeatedChars(text) {
        if (!text) return text;

        try {
          // 1. 修复常见的Unicode组合错误
          let result = text;

          // 2. 移除无效的UTF-8序列和替换字符
          result = result.replace(/\uFFFD/g, ''); // 替换字符

          // 3. 处理不完整的代理对
          result = result.replace(/[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g, '');

          // 4. 处理中文等多字节字符的重复
          result = result.replace(/([^\x00-\xff])\1+/g, '$1');

          // 5. 处理可能的乱码模式 (常见的乱码组合)
          const commonBadPatterns = [
            /[\u0080-\u009F]/g,  // 控制字符
            /[\u0000-\u001F]/g,  // 低位控制字符
            /\u{FFFD}+/gu,       // 多个替换字符
          ];

          for (const pattern of commonBadPatterns) {
            result = result.replace(pattern, '');
          }

          return result;
        } catch (e) {
          console.error('清理字符时出错:', e);
          return text; // 发生错误时返回原始文本
        }
      }

      // 开始处理流
      return processStream();
    })
    .catch((error) => {
      console.error("获取流数据错误:", error);
      if (onError && typeof onError === "function") {
        onError(error);
      }
    });

  // 返回控制器，用于取消请求
  return controller;
}

// 停止生成回答
export function stopAskQuestion(data, token = getStoredToken()) {
  return axios({
    url: "/spec-apps/api/app/rpachat/stopAskQuestion",
    method: "post",
    headers: {
      Authorization: token,
      rpaUserAgent: "cuswriter",
      "Content-Type": "application/json",
    },
    data,
  });
}
