<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="搜索" prop="query">
        <el-input style="width:500px;" v-model="queryParams.query" placeholder="产品名称/部套名称/部套编码/厂商编码/计划编号/物料/采购订单号/合同号"
          clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="过程类型" prop="processType">
        <el-select v-model="queryParams.processType" clearable placeholder="请选择">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" clearable placeholder="请选择">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="生产厂商" prop="produceFactoryName">
        <el-input v-model="queryParams.produceFactoryName" placeholder="生产厂商" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="工作令号" prop="productCode">
        <el-input v-model="queryParams.productCode" placeholder="工作令号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="短文本" prop="txz01">
        <el-input v-model="queryParams.txz01" placeholder="短文本" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="炉号" prop="heatNo">
        <el-input v-model="queryParams.heatNo" placeholder="炉号" clearable :maxlength="32"
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="质检员" prop="htcId">
        <el-select v-model="queryParams.htcId" clearable filterable placeholder="请选择">
          <el-option v-for="item in peopleOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="完成时间">
        <el-date-picker v-model="time" type="daterange" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="采购订单号" prop="ebeln">
        <el-input v-model="queryParams.ebeln" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="采购订单行号" prop="ebelp">
        <el-input v-model="queryParams.ebelp" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-download" size="mini" @click="handleImport">导入</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-upload2" size="mini" @click="handleExp">导出</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>



    <el-table v-loading="loading" :data="postList">
      <el-table-column label="序号" type="index">
        <template slot-scope="{row,$index}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ $index + 1 }}</div>
        </template>
      </el-table-column>
      <el-table-column label="产品名称" prop="productName" width="150">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.productName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="工作令号" prop="productCode">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.productCode }}</div>
        </template>
      </el-table-column>

      <el-table-column label="采购订单号" prop="ebeln" width="100">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.ebeln }}</div>
        </template>
      </el-table-column>

      <el-table-column label="项目" prop="ebelp" width="100">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.ebelp }}</div>
        </template>
      </el-table-column>

      <el-table-column label="短文本" prop="txz01" width="230">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.txz01 }}</div>
        </template>
      </el-table-column>

      <el-table-column label="物料" prop="matnr" width="150">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.matnr }}</div>
        </template>
      </el-table-column>

      <el-table-column label="炉号" prop="heatNo" width="100">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.heatNo }}</div>
        </template>
      </el-table-column>

      <el-table-column label="数量" prop="menge" width="100">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.menge }}</div>
        </template>
      </el-table-column>

      <el-table-column label="单位" prop="meins" width="100">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.meins }}</div>
        </template>
      </el-table-column>

      <el-table-column label="部套名称" prop="partName" width="200">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.partName }}</div>
        </template>
      </el-table-column>

      <el-table-column label="部套编码" prop="partCode" width="150">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.partCode }}</div>
        </template>
      </el-table-column>

      <el-table-column label="厂商编码" prop="produceFactory" width="100">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.produceFactory }}</div>
        </template>
      </el-table-column>

      <el-table-column label="生产厂商" prop="produceFactoryName" width="250px">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.produceFactoryName }}</div>
        </template>
      </el-table-column>

      <el-table-column label="合同号" prop="contractNo" width="150">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.contractNo }}</div>
        </template>
      </el-table-column>

      <el-table-column label="检验实验计划编号" prop="planCode" min-width="100">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.planCode }}</div>
        </template>
      </el-table-column>

      <el-table-column label="过程类型" prop="processType" width="150">
        <template slot-scope="{ row }">
          <div :class="{ 'gender': row.rejectStatus === 1 }">
            {{ row.processType | filterStauts(typeOptions, 'label') }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="应提交计划" prop="nodeNum" width="100">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">{{ row.nodeNum }}</div>
        </template>
      </el-table-column>

      <el-table-column label="完成时间" prop="" width="150">
        <template slot-scope="{ row }">
          <div :class="{ 'gender': row.rejectStatus === 1 }">
            {{ row.finishTime | dateFormat }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="质检员" prop="htcUserName" fixed="right">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 || row.htcDeleteStatus === 1 }">
            {{ row.htcDeleteStatus === 1 ? '已删除' : row.htcUserName }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="状态" prop="updateStatus" fixed="right">
        <template slot-scope="{row}">
          <div :class="{ 'gender': row.rejectStatus === 1 }">
            {{ row.status | filterStauts(statusOptions, 'label') }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="110">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="showItem(scope.row)">详情</el-button>
          <el-button v-if="scope.row.roleType != 2" size="mini" type="text" icon="el-icon-edit"
            @click="handleModify(scope.row)">修改</el-button>
        </template></el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />



    <detial ref="detial"></detial>

    <modify-project ref="modifyProject" @getList="getList"></modify-project>

    <add-project ref="addProject" @getList="getList"></add-project>

    <import-file ref="importFile" @getList="getList"></import-file>
  </div>
</template>

<script>
import { getSmPlanPage } from "@/api/smallTestExperiment/experimentPlan.js";
import { getHtcUserList } from "@/api/smallTestExperiment/personnel.js";
import modifyProject from './components/modifyProject'
import detial from "./components/detial.vue";
import addProject from './components/addProject.vue'
import importFile from './components/importFile.vue';
import { exportFile } from '@/utils/gloabUtile'
export default {
  components: {
    detial,
    modifyProject,
    addProject,
    importFile
  },
  name: "Post",
  data() {
    return {
      loading: false,
      showSearch: true, // 显示搜索条件
      total: 0, // 总条数
      postList: [], // 列表数据

      time: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: '',
        processType: '',
        status: '',
        produceFactoryName: '',
        heatNo: '',
        txz01: '',
        productCode: '',
        htcId: ''
      },
      // 过程类型
      typeOptions: [
        {
          value: 1,
          label: "外购（原材料）",
        },
        {
          value: 2,
          label: "外购（机械）",
        },
        {
          value: 3,
          label: "外购（焊接）",
        },
        {
          value: 4,
          label: "厂内（机械）",
        },
        {
          value: 5,
          label: "工序外协",
        },
        {
          value: 6,
          label: "成套采购",
        },
        {
          value: 7,
          label: "外协（包工包料）",
        },
        {
          value: 8,
          label: "外协（带料加工）",
        }
      ],
      // 状态
      statusOptions: [
        {
          value: 0,
          label: "未完成",
        },
        {
          value: 1,
          label: "已完成",
        },
        {
          value: 2,
          label: "进行中",
        },
      ],
      // 质检员
      peopleOptions: [],

    };
  },
  created() {
    this.$route.query.row && (this.queryParams.ebelp = this.$route.query.row.ebelp) && (this.queryParams.ebeln = this.$route.query.row.ebeln)

    this.userType = JSON.parse(localStorage.getItem("userInfo")).userType;
    this.getHtcUserList()
    this.getList();
  },

  filters: {
    filterStauts(val, statusOptions, property) {
      let options = statusOptions.find((item) => {
        return item.value == val
      })
      return options ? options[property] : ''
    }
  },

  methods: {
    handleExp() {
      this.queryParams.startTime = this.time && this.time.length > 0 ? this.time[0] : ''
      this.queryParams.endTime = this.time && this.time.length > 0 ? this.time[1] : ''
      let params = {
        ...this.queryParams,
        loginType: this.userType
      }
      exportFile('/mes-server/sm/plan/export/plan', params)
    },
    getHtcUserList() {
      getHtcUserList().then((res) => {
        let resData = res.data || []
        this.peopleOptions = resData.map((item) => {
          return { label: item.name, value: String(item.userId) }
        });
        this.peopleOptions.push({ label: '已删除', value: 'delete' })
      }).catch(() => {
      });
    },

    /** 查询表格 */
    getList() {
      this.loading = true;
      this.queryParams.startTime = this.time && this.time.length > 0 ? this.time[0] : ''
      this.queryParams.endTime = this.time && this.time.length > 0 ? this.time[1] : ''
      let params = {
        ...this.queryParams,
        loginType: this.userType
      }
      getSmPlanPage(params)
        .then((response) => {
          this.postList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.time = []
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 修改按钮操作 */
    handleModify(row) {
      this.$refs.modifyProject.init(row)
    },

    // 项目名称弹窗
    showItem(row) {
      this.$refs.detial.init(row);
    },

    // 新增
    handleAdd() {
      this.$refs.addProject.init()
    },

    // 导入
    handleImport() {
      this.$refs.importFile.init()
    },


  },
};
</script>

<style scoped>
.gender {
  color: #F56C6C !important;
}

::v-deep .el-dialog__body {
  line-height: 36px;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}

::v-deep .el-row--flex {
  margin-left: 22px;
}

::v-deep .el-dialog {
  /* margin-top: 30vh !important; */
}

::v-deep .el-input.is-disabled .el-input__inner {
  color: #333;
}
</style>
