<template>
     <el-dialog
      title="基本信息"
      :visible.sync="dialogVisible"
      append-to-body
      @close="cancel"
      width="50%"
    >
        <el-form ref="form" :model="form" :rules="rules" label-width="140px">
            <el-form-item
            prop="htcId"
            label="选择HTC"
            >
                <el-button type="primary" size="small" plain @click="handleUser">选择HTC</el-button>
                <span  class="margin-left">已选择：{{form.htcName}}</span>
            </el-form-item>

            <el-form-item
            label="炉批号/件号(标识)"
            prop="heatNo"
            >
                <el-input
                style="width: 400px"
                v-model="form.heatNo"
                placeholder="请输入炉批号/件号(标识)"
                :maxlength="256">
                </el-input>
            </el-form-item>

            <el-form-item
            label="检验试验编号"
            prop="planCode"
            >
                <el-input
                    style="width: 400px"
                    v-model="form.planCode"
                    placeholder="请输入检验试验编号"
                ></el-input>
            </el-form-item>

            <el-form-item
            prop="processType"
            label="过程类型"
            >
                <el-select
                v-model="form.processType"
                clearable
                filterable
                placeholder="过程类型"
                >
                    <el-option
                        v-for="item in typeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item
            label="规范号批量上传"
            >   
                <div class="upload-file">
                    <el-button class="upload-btn" type="primary" size="small" plain @click="handleDowdload">下载节点</el-button>
                    <el-upload
                    class="margin-left"
                    action="#"
                    show-file-list
                    accept=".xlsx,.xls"
                    :file-list="fileList"
                    :limit="1"
                    :http-request="fileSuccess"
                    :before-upload="beforeUpload"
                    :file-error="fileError"
                    :before-remove="beforeRemove"
                    >
                        <el-button class="upload-btn" type="primary" size="small" plain :loading="btnLoading">上传规范号</el-button>
                    </el-upload>

                    <span class="margin-left" v-if="fileSucess">
                        已匹配
                        <i class="icon el-icon-success"></i>
                    </span>
                </div>
            </el-form-item>


        </el-form>

        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
            <el-button @click="cancel()">取 消</el-button>
        </span>

        <select-htc ref="selectHtc" @setHtc="setHtc"></select-htc>
    </el-dialog>
</template>

<script>
import selectHtc from './selectHtc.vue'
import { smPlanDetail,exportData } from "@/api/smallTestExperiment/experimentPlan.js";
import { exportFile } from '@/utils/gloabUtile'
import uploadFile from '@/components/uploadFile/main'
export default {
  components:{selectHtc,uploadFile},

  data(){
    return{
        dialogVisible:false,
        btnLoading:false,
        detailsData:{},
        fileList:[],
        fileSucess:false,
        form:{
            htcId:'',
            htcName:'',
            heatNo:'',
            planCode:'',
            processType:''
        },
        typeOptions: [
            {
                label: "外购（原材料）",
                value: 1,
            },
            {
                label: "外购（机械）",
                value: 2,
            },
            {
                label: "外购（焊接）",
                value: 3,
            },
            {
                label: "厂内（机械）",
                value: 4,
            },
            {
                value: 5,
                label: "工序外协",
            },
            {
                value: 6,
                label: "成套采购",
            },
            {
                value: 7,
                label: "外协（包工包料）",
            },
            {
                value: 8,
                label: "外协（带料加工）",
            }
        ],
        rules:{
            htcId:[
                { required: true, message: '请选择HTC', trigger: 'blur' }
            ],
            heatNo:[
                { required: true, message: '请输入炉批号/件号(标识)', trigger: 'blur' }
            ],
            planCode:[
                { required: true, message: '请选择检验试验编号', trigger: 'blur' },
            ],
            processType:[
                { required: true, message: '请选择过程类型', trigger: 'blur' },
            ]
        }
    }
  },

  methods:{
    init(detailsData){    
        this.dialogVisible = true
        this.btnLoading = false
        this.fileSucess = false
        this.form = {
            htcId:'',
            htcName:'',
            heatNo:'',
            planCode:'',
            processType:''
        }
        this.fileList = []
        this.detailsData = JSON.parse(JSON.stringify(detailsData))
    },

    // 选择HTC
    handleUser() {
      this.$refs.selectHtc.init(this.detailsData);
    },

    // 设置HTC
    setHtc(form){
        this.form.htcId = form.htcId
        this.form.htcName = form.htcName
        this.detailsData.htcId = form.htcId
    },

    // 下载节点
    handleDowdload(){
        let params = {
            planId:this.detailsData.id
        }
        exportFile('/mes-server/sm/plan/export/excel',params)
    },

    // 上传文件前
    beforeUpload(file,callBack){
        this.btnLoading = true
    },

    // 上传成功
    fileSuccess(file){
        this.fileList = [file.file]
        this.fileSucess = true
        this.btnLoading = false
    },

    // 文件上传失败
    fileError(error){
        this.btnLoading = false
    },

    // 删除文件前
    beforeRemove(file,fileList){
        this.fileList = []
        this.fileSucess = false
    },

    
    async smPlanDetail(){
        let params = {
            ...this.form,
            planId:this.detailsData.id
        }
        await smPlanDetail(params).catch(()=>{
            this.btnLoading = false
        })
    },

    async exportData(){
        let formData = new FormData()
        let file = this.fileList[0]
        formData.append('file',file)
        formData.append('planId',this.detailsData.id)
        await exportData(formData).catch(()=>{
            this.btnLoading = false
        })
    },

    async confirm(){
        this.$refs['form'].validate(async (valid) => {
            if(valid){
                this.$confirm('此操作将不能更改，如需修改请联系管理员', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    this.btnLoading = true
                    await this.smPlanDetail()
                    if(this.fileList && this.fileList.length>0){
                        await this.exportData()
                    }
                    if(this.btnLoading){
                        this.$emit('getDetail')
                        this.dialogVisible = false
                    }
                    this.btnLoading = false
                }).catch(() => {    
                });
                
            }
        })
    },



    cancel(){
        this.$refs['form'].resetFields()
        this.dialogVisible = false
    },
  },

}

</script>

<style scoped lang='scss'>
.margin-left {
    margin-left:10px;
    .icon {
        color:#67C23A;
    }
}
.upload-file {
    display:flex;
    .upload-btn {
        height:36px;
    }
}
</style>