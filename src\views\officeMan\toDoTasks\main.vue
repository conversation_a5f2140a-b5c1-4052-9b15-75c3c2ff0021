<template>
  <div class="to_do_tasksMian">
    <div class="search_form" style="margin-bottom: 10px">
      <el-row type="flex" :gutter="6">
        <el-col :span="4">
          <el-input
            v-model="searchForm.processName"
            class="format_option"
            size="small"
            placeholder="请输入流程名称"
            clearable
          ></el-input>
        </el-col>
        <el-col :span="5">
          <el-date-picker
            v-model="searchForm.time"
            class="format_option"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-col>

        <el-col :span="4" style="margin-left: 130px">
          <div class="btn_box">
            <el-button
              @click="search(false)"
              size="small"
              class="btn search_btn"
              icon="el-icon-search"
              type="primary"
              >搜索</el-button
            >
            <el-button
              @click="search(true)"
              size="small"
              class="btn reset_btn"
              icon="el-icon-refresh"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- <div class="operation_btn">
        <el-button
          class="btn add_btn"
          size="small"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd()"
          >新增</el-button
        >
      </div> -->

    <div class="table_box">
      <el-table
        v-loading="loading"
        :data="tableData"
        row-key="id"
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="50" align="center">
        </el-table-column>

        <el-table-column
          v-for="(item, index) in tableColumn"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :width="item.width"
        >
          <template slot-scope="{ row }">
            <label v-if="item.depTabStatus" class="dep_tab">
              <span class="text">{{ row[item.prop] }}</span>
              <!-- <el-tag size="mini" type="info">{{ row.startDeptName }}</el-tag> -->
            </label>
            <el-tag v-else-if="item.versionStatus" type="default"
              >v{{ row[item.prop] }}</el-tag
            >
            <div v-else-if="item.checkTime">
              <i class="el-icon-time"></i>
              {{ row[item.prop] | dateTimeFormat }}
            </div>
            <div v-else>{{ row[item.prop] }}</div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="200">
          <template slot-scope="{ row }">
            <div class="handle_btn">
              <el-button
                type="text"
                @click="handleOperation(row)"
                size="small"
                icon="el-icon-edit-outline"
                >办理</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="page_box">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentPage"
          :current-page="page.pageNum"
          :pager-count="5"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
        >
        </el-pagination>
      </div>
    </div>
    <transaction-tasks
      ref="transactionTasks"
      @hidDialog="hidDialog"
    ></transaction-tasks>
  </div>
</template>

<script>
import transactionTasks from "./components/transactionTasks";
import { todoList } from "@/api/officeMan/toDoTasks";
export default {
  components: { transactionTasks },
  data() {
    return {
      loading: false,
      statusOptions: [],
      tableData: [],
      typeOptions: [],
      time: [],

      searchForm: {
        processName: "",
      },
      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },

      tableColumn: [
        {
          prop: "taskId",
          label: "任务编号",
          width: "300",
        },
        {
          prop: "procDefName",
          label: "流程名称",
          width: "300",
        },
        {
          prop: "taskName",
          label: "任务节点",
          width: "200",
        },
        {
          prop: "procDefVersion",
          label: "流程版本",
          versionStatus: true,
          width: "",
        },

        {
          prop: "startUserName",
          label: "流程发起人",
          depTabStatus: true,
          width: "200",
        },
        {
          prop: "createTime",
          label: "接收时间",
          checkTime: true,
          width: "200",
        },
      ],
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    hidDialog() {
      this.loadData();
    },

    // 搜索/重置
    search(reset) {
      if (reset) {
        this.searchForm = {
          processName: "",
        };
      }
      this.page.pageNum = 1;
      this.loadData();
    },

    // 办理
    handleOperation(row) {
      this.$refs.transactionTasks.init(row);
    },

    loadData() {
      this.loading = true;
      let params = {
        ...this.page,
        ...this.searchForm,
      };
      todoList(params)
        .then((res) => {
          let resData = res.data;
          resData.records.forEach((item) => {
            item.status = item.finishTime ? "进行中" : "已完成";
          });
          this.tableData = resData.records;
          this.page.total = resData.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 更改每页显示条数
    handleSizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.loadData();
    },

    // 选择页数/点击上一页/下一页
    handleCurrentPage(currentPage) {
      this.page.pageNum = currentPage;
      this.loadData();
    },
  },
};
</script>
<style lang="less">
.to_do_tasksMian {
  padding: 16px 12px 0;
}
</style>
