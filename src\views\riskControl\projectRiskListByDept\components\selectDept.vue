<template>
    <el-dialog
    title="新增"
    :visible.sync="dialogVisible"
    width="680px"
    append-to-body
    >
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChangeDept"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" />
        <el-table-column label="姓名" prop="userName" width="160" />
        <el-table-column label="部门" prop="deptName" />
      </el-table>

      <pagination
        :pager-count="5"
        v-show="total > 0"
        :total="deptForm.total"
        :page.sync="deptForm.pageNum"
        :limit.sync="deptForm.pageSize"
        @pagination="getDutyUserPage"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
</template>

<script>
import { getDutyUserPage,addDeptInfo  } from "@/api/riskControl/projectRiskListByDept";
export default {
    components:{},

    data(){
        return{
            dialogVisible:false,
            loading:false,
            btnLoading:false,
            tableData:[],
            riskDataId:null,
            total: 0,
            deptForm: {
                pageNum: 1,
                pageSize: 10,
                total: 0,
            },
        }
    },

    methods:{
        init(riskDataId){
            this.dialogVisible = true
            this.riskDataId = riskDataId
            this.getDutyUserPage()
        },

        handleSelectionChangeDept(selection) {
            this.deptCheckList = selection;
        },

        getDutyUserPage() {
            this.loading = true
            getDutyUserPage(this.deptForm).then((res) => {
                this.tableData = res.data.records;
                this.deptForm.total = res.data.total || 0;
                this.loading = false
            }).catch(()=>{
                this.loading = false
            });
        },

        confirm(){
            let deptList = this.deptCheckList.map((item) => {
                return {
                    dutyDeptId: item.deptId,
                    dutyUserId: item.userId,
                };
            });
            this.btnLoading = true
            addDeptInfo({
                deptList,
                id: this.riskDataId,
            }).then((response) => {
                this.btnLoading = false
                this.$modal.msgSuccess("新增成功");
                this.dialogVisible = false
                this.$emit('getProjectRiskDept')
            }).catch(()=>{
                this.btnLoading = false
            });
        },

        cancel(){
            this.dialogVisible = false
        }
    },

}

</script>

<style scoped lang='less'>
</style>