import request from '@/utils/request'
// app角色列表
export function getAppListPage(param) {
  return request({
    url: '/user-server/appRole/getListPage',
    method: 'post',
    params: param,
  })
}
// 添加app角色
export function addAppRole(param) {
  return request({
    url: '/user-server/appRole/add',
    method: 'post',
    data: param,
  })
}
// 删除app角色
export function deletesAppRole(param) {
  return request({
    url: '/user-server/appRole/deletes',
    method: 'post',
    params: param,
  })
}
// 修改app角色
export function updateAppRole(data) {
  return request({
    url: '/user-server/appRole/update',
    method: 'post',
    data,
  })
}
//导出
export function exportData(query) {
  return request({
    url: '/user-server/appUser/exportExcel',
    method: 'get',
    params: query,
    responseType: 'blob', // important
  })
}
//模板
export function exportExcelTemplate() {
  return request({
    url: '/user-server/appUser/exportExcelTemplate',
    method: 'get',
    responseType: 'blob', // important
  })
}
// 后台角色修改状态
export function updateStatus(param) {
  return request({
    url: '/user-server/appRole/updateStatus',
    method: 'get',
    params: param,
  })
}
// 获取模块列表
export function getModuleByRoleId(param) {
  return request({
    url: '/user-server/module/getModuleByRoleId',
    method: 'get',
    params: param,
  })
}
// 获取模块列表
export function updateRoleModule(param) {
  return request({
    url: '/user-server/module/updateRoleModule',
    method: 'post',
    data: param,
  })
}



