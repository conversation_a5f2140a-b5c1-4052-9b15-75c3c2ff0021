import request from '@/utils/request'
// 我的流程列表
export function ownList(query) {
    return request({
        url: `/flowable/workflow/process/ownList`,
        method:'get',
        params:query
    });
}

// 查询流程详情信息
export function detailInfo(query) {
    return request({
        url: `/flowable/workflow/process/detail`,
        method:'get',
        params:query
    });
}


// 取消申请
export function cancelDetail(query) {
    return request({
        url: `/flowable/workflow/task/stopProcess`,
        method:'post',
        data:query
    });
}


// 删除
export function delProcess(instanceId) {
    return request({
        url: `/flowable/workflow/instance/delete?instanceId=${instanceId}`,
        method:'delete',
    });
}








