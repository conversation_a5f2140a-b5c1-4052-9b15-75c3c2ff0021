
<template>
    <div v-if="show" class="dialog_mask" :class="{ 'dialog':true, 'active':show}" @mousewheel="mousewheelFn">
        <i class="el-icon-error close" v-if="showClose" @click="closeFn"></i>
        <div class="wrap" ref="wrap">
            <div class="pdfWrap" id="pdfWrap" :style="{cursor: 'pointer', transform: `rotate(${rotate}deg)`,width: `${scale}%`}"
            >
                <pdf
                :src="pdfSrc"
                :page="currentPage"
                ref="pdf"
                @num-pages="pageCount=$event"
                @page-loaded="currentPage=$event"
                @loaded="loadPdfHandler"
                ></pdf>
            </div>

            <!-- 操作栏 -->
            <div class="control">
                <i
                v-for="(item,index) in controlMenu"
                :key="index"
                @click="handle(item.index)"
                :title="item.title"
                :class="item.icon"
                ></i>
            </div>
        
        
        <div :class="{toast:true,showToast,animate}">{{toastScale}}</div>
        </div>
    </div>
</template>

<script>
import { bind, unbind, objDrag } from "./js/util";
import pdf from "vue-pdf";
export default {
    props:{
        dialog:{
            type:Boolean,
            default(){
                return false
            }
        }
    },
    components: {
        pdf
    },
    data() {
        return {
            show:false,
            initPosX: 0,
            initPosY: 0,
            pdfSrc: "",
            currentPage: 0, // pdf文件页码
            pageCount: 0, // pdf文件总页数
            numPages: 1,
            activeIndex: 0,
            iWidth: document.documentElement.clientWidth,
            iHeight: document.documentElement.clientHeight,
            scale: 50,
            rotate: 0,
            scaleAdd: 10,
            rotateAdd: 90,
            showToast: false,
            animate: false,
            toastTimer1: null,
            toastTimer2: null,
            objDrag: null,
            controlMenu:[
                {icon:'el-icon-zoom-in',title:'放大',index:1},
                {icon:'el-icon-zoom-out',title:'缩小',index:2},
                {icon:'el-icon-c-scale-to-original',title:'还原',index:3},
                {icon:'el-icon-refresh-left',title:'向左旋转90°',index:4},
                {icon:'el-icon-refresh-right',title:'向右旋转90°',index:5},
                // {icon:'el-icon-caret-left',title:'上一页',index:6},
                // {icon:'el-icon-caret-right',title:'下一页',index:7},
            ]
        }
    },

    computed: {
        toastScale() {
            let { scale } = this;
            return `${scale}%`;
        }
    },
    props: {
        showName: {
            //控制显示组件的名称
            type: String,
            default: ""
        },
        showClose: {
            //是否显示组件关闭按钮
            type: Boolean,
            default: true
        },
    },

    beforeDestroy() {
        unbind(window, "resize", this.initPos);
    },

    mounted() {
        //图片加上拖拽
        bind(window, "resize", this.initPos);
    },

    methods: {
        init(pdfSrc){
            this.pdfSrc = pdfSrc
            this.show = true
        },
        
        initPos() {
            let pdfWrap = document.getElementById("pdfWrap");
            this.iWidth = document.documentElement.clientWidth;
            this.iHeight = document.documentElement.clientHeight;
            this.initPosX = this.iWidth / 4;
            this.initPosY = this.iHeight / 8;
            this.scale = 50;
            this.rotate = 0;
            pdfWrap.style.left = this.initPosX + "px";
            pdfWrap.style.top = this.initPosY + "px";
        },

        changePdfPage(val) {
        if (val === 0 && this.currentPage > 1) {
            this.currentPage--;
        }
        if (val === 1 && this.currentPage < this.pageCount) {
            this.currentPage++;
        }
        },

        loadPdfHandler(e) {
            this.currentPage = 1; // 加载的时候先加载第一页
            this.drag("pdfWrap");
            this.initPos();
        },

        closeFn() {
            this.show = false
            this.scale = 50;
            this.rotate = 0;
        },

        showToastFn() {
            this.showToast = true;
            this.animate = false;
            clearTimeout(this.toastTimer1);
            clearTimeout(this.toastTimer2);
            this.toastTimer1 = setTimeout(() => {
                this.animate = true;
                this.toastTimer2 = setTimeout(() => {
                this.showToast = false;
                this.animate = false;
                }, 300);
            }, 300);
        },

        // 放大
        magnify() {
            let { scale } = this;
            scale += this.scaleAdd;
            scale = scale > 1000 ? 1000 : scale;
            this.showToastFn();
            this.scale = scale;
        },

        // 缩小
        shrink() {
            let { scale } = this;
            scale -= this.scaleAdd;
            scale = scale <= 10 ? 10 : scale;
            this.showToastFn();
            this.scale = scale;
        },

        handle(index) {
            let { scale, rotate } = this;
            switch (index) {
                case 1:
                this.magnify();
                break;
                case 2:
                this.shrink();
                break;
                case 3:
                this.initPos();
                break;
                case 4:
                this.rotate -= this.rotateAdd;
                break;
                case 5:
                this.rotate += this.rotateAdd;
                break;
                case 6:
                this.changePdfPage(0);
                break;
                case 7:
                this.changePdfPage(1);
                break;
            }
        },

        mousewheelFn(ev) {
            let up = true;
            if (ev.wheelDelta) {
                up = ev.wheelDelta > 0 ? true : false;
            } else {
                up = ev.detail < 0 ? true : false;
            }
            if (up) {
                this.magnify();
            } else {
                this.shrink();
            }
        },

        drag(obj) {
            this.objDrag = new objDrag(obj);
        }

    }

}

</script>
<style lang="less">
.dialog {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1999;
  display: none;
  &.active {
    display: block;
  }
  > .close {
    position:absolute;
    font-size:30px;
    z-index:999;
    cursor:pointer;
    right: 30px;
    top: 30px;
  }
  > .wrap {
    width: 100%;
    height: 100%;
    position: relative;
    .pdfWrap {
      position: absolute;
    }
    > .control {
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 320px;
        padding: 10px 10px;
        box-sizing: border-box;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 25px;
        position: absolute;
        left: 50%;
        margin-left: -150px;
        bottom: 20px;
        i {
            width: 20px;
            margin: 0 5px;
            height: 20px;
            font-size:20px;
            display:inline-block;
            cursor:pointer;
            background: no-repeat center center;
            background-size: 16px;
        }
    }
    > .toast {
        width: 100px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        position: absolute;
        top: 30px;
        left: 50%;
        margin-left: -50px;
        z-index: 10;
        transition: opacity 0.3s ease-out;
        opacity: 1;
        display: none;
        &.showToast {
            display: block;
        }
        &.animate {
            opacity: 0;
        }
    }
  }
}
 
</style>
  