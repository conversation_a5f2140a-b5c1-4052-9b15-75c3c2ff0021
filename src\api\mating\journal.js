import request from '@/utils/request'
// 机组日志服务
// 新增机组日志
export function unitLogAdd(param) {
    return request({
      url: '/back-server/unitLog/add',
      method: 'post',
      data: param,
    })
  }
// 删除机组日志  
export function unitLogDeleteLog(ids) {
    return request({
      url: `/back-server/unitLog/deleteLog?logId=${ids}`,
      method: 'get',
    })
  }
//   批量删除机组日志
export function unitLogDeleteLogs(ids) {
    return request({
      url: `/back-server/unitLog/deleteLogs?logIds=${ids}`,
      method: 'get',
    })
  }
// 分页查询
export function findAllPage(param) {
    return request({
      url: '/back-server/unitLog/findAllPage',
      method: 'get',
      params: param,
    })
  }
// 根据机组令号查询机组日志
export function unitLogGetLogByPosid(param) {
    return request({
      url: '/back-server/unitLog/getLogByPosid',
      method: 'get',
      param: param,
    })
  }  
//   修改机组日志
export function unitLogUpdateLog(param) {
    return request({
      url: '/back-server/unitLog/updateLog',
      method: 'post',
      data: param,
    })
  }

  // 获取机组信息
  export function findAllUnits(param) {
    return request({
      url: '/back-server/engineeringUnit/findAllUnits',
      method: 'get',
      param: param,
    })
  }
  // 获取音频文件
  export function fileVoice(param) {
    return request({
      url: '/user-server/file/voice',
      method: 'post',
      data: param,
    })
  }
  