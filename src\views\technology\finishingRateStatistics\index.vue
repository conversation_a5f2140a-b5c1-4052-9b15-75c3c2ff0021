<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="月份" prop="monthGroup" label-width="40px">
        <el-date-picker
          v-model="queryParams.monthGroup"
          type="month"
          value-format="yyyy-MM"
          placeholder="选择月"
          clearable
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="节点" prop="nodeType" label-width="40px">
        <el-select
          v-model="queryParams.nodeType"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in nodeTypeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >偏离修正</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column label="序号" type="index" />
      <el-table-column label="时间" prop="monthGroup" />
      <el-table-column label="节点名称" prop="nodeName" />
      <el-table-column label="计划数量" prop="planInMonthNum" />
      <el-table-column label="按期完成数" prop="finishOnTimeInMonthNum">
      </el-table-column>
      <el-table-column label="超期完成数" prop="finishTimeOutInMonthNum" />
      <el-table-column label="完成数" prop="finishInMonthNum" />
      <el-table-column label="未完成数" prop="unFinishInMonthNum" />
      <el-table-column label="按期完成率(%)" prop="finishOnTimeRate" />
      <el-table-column label="超期完成率(%)" prop="finishTimeOutRate" />
      <el-table-column label="完成率(%)" prop="finishInMonthRate" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="月份" prop="monthGroup" style="width: 90%">
              <el-date-picker
                style="width: 100%"
                v-model="form.monthGroup"
                type="month"
                value-format="yyyy-MM"
                placeholder="选择月"
                clearable
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="节点" prop="nodeType" style="width: 90%">
              <el-select
                style="width: 100%"
                v-model="form.nodeType"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="dict in nodeTypeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="计划数量"
              prop="planInMonthNum"
              style="width: 90%"
            >
              <el-input
                v-model="form.planInMonthNum"
                placeholder="请输入"
                type="number"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="按期完成数"
              prop="finishOnTimeInMonthNum"
              style="width: 90%"
            >
              <el-input
                v-model="form.finishOnTimeInMonthNum"
                type="number"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="完成数"
              prop="finishInMonthNum"
              style="width: 90%"
            >
              <el-input
                v-model="form.finishInMonthNum"
                type="number"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getEngineeringList } from "@/api/items/items";
import {
  getRatePage,
  delRate,
  addRate,
  updateRate,
} from "@/api/technology/finishingRateStatistics";

export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      engineeringList: [],
      nodeList: [],
      studes: [
        { label: "正常", value: 0 },
        { label: "报警", value: 1 },
        { label: "预警", value: 2 },
        { label: "完成", value: 3 },
      ],
      nodeTypeOptions: [
        { label: "设计审定", value: 1 },
        { label: "工艺文件", value: 5 },
        { label:'技术准备', value:7}
      ],
      //机组详情
      posidList: {},
      orderList: {},
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 节点详情弹出层
      opens: false,
      // 项目名称
      itemOpen: false,
      dataForm: {
        keyword: undefined,
      },
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        monthGroup: "",
        nodeType: "",
      },
      // 表单参数
      form: {
        monthGroup: "",
        finishOnTimeInMonthNum: "",
        finishInMonthNum: "",
        planInMonthNum: "",
        nodeType: "",
      },
      // 表单校验
      rules: {
        nodeType: [
          { required: true, message: "节点不能为空", trigger: "blur" },
        ],
        planInMonthNum: [
          { required: true, message: "计划数量不能为空", trigger: "blur" },
        ],
        finishInMonthNum: [
          { required: true, message: "完成数不能为空", trigger: "blur" },
        ],
        finishOnTimeInMonthNum: [
          { required: true, message: "按期完成数不能为空", trigger: "blur" },
        ],
        monthGroup: [
          { required: true, message: "时间不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getEngineeringList();
    this.getList();
  },
  methods: {
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getRatePage(this.queryParams).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    //机组列表
    getEngineeringList() {
      getEngineeringList(this.dataForm).then((res) => {
        res.data.forEach((element, index) => {
          this.engineeringList.push({
            value: element.posid,
            label: element.post1 + "(" + element.posid + ")",
          });
        });
      });
    },
    //获取节点名称
    getNodeName(val) {
      let status = this.nodeTypeOptions.find((e) => e.value == val);
      if (status) {
        return status.label;
      } else {
        return "";
      }
    },
    // 项目名称弹窗
    item(row) {
      this.posidList = JSON.parse(JSON.stringify(row));
      this.itemOpen = true;
    },
    //查看节点
    sayJie(nodeList) {
      this.nodeList = nodeList;
      this.title = "节点详情";
      this.opens = true;
      for (let item of nodeList) {
        if (item.nodeStatus == 2) {
          return false;
        }
        item.nodeStatus = 2;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        monthGroup: "",
        finishOnTimeInMonthNum: "",
        finishInMonthNum: "",
        planInMonthNum: "",
        nodeName: "",
      };
      // this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "偏离修正";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.form = JSON.parse(JSON.stringify(row));
      this.open = true;
      this.title = "偏离修正修改";
    },
    /**查看节点*/
    handleLook(row) {
      this.opens = true;
      this.title = "节点详情";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let nodeName = this.getNodeName(this.form.nodeType);
          if (this.form.id != undefined) {
            updateRate({ ...this.form, nodeName }).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRate({ ...this.form, nodeName }).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删?")
        .then(function () {
          return delRate({ id: row.id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style scoped>
::v-deep .el-dialog__body {
  line-height: 30px;
}
::v-deep .el-card__body {
  line-height: normal;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}
.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
.node {
  font-size: 14px;
  color: #666;
  display: flex;
  flex-direction: column;
}
.font16 {
  font-size: 16px;
  color: #333;
  padding-bottom: 8px;
}
.fontweight {
  font-weight: 700;
}
::v-deep .el-dialog__body {
  padding-top: 25px !important;
}
::v-deep .el-dialog__body .grid-content {
  padding: 6px;
}
::v-deep .el-row--flex {
  margin-left: 16px;
}

::v-deep .el-card.is-always-shadow {
  box-shadow: none;
  border: none;
  background: #f3f7fd;
  border-radius: 5px;
}
</style>
