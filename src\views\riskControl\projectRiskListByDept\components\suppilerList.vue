<template>
    <el-dialog
    title="新增"
    :visible.sync="dialogVisible"
    width="680px"
    append-to-body
    >
      <el-form
        :model="form"
        ref="form"
        size="small"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="搜索" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="searchForm(false)"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="searchForm(true)"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-table
        :data="tableData"
        highlight-current-row
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column label="序号" type="index" />
        <el-table-column label="供应商名称" prop="supplierName" />
      </el-table>

      <pagination
        :pager-count="5"
        v-show="form.total > 0"
        :total="form.total"
        :page.sync="form.page"
        :limit.sync="form.size"
        @pagination="loadData"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="multipleSelection.length>0?false:true" @click="submitFormSupplier" :loading="btnLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
</template>

<script>
import { addSupplierInfo,getSupplierPage } from "@/api/riskControl/projectRiskListByDept";
export default {
    components:{},

    data(){
        return{
            dialogVisible:false,
            loading:false,
            btnLoading:false,
            tableData:[],
            radio: "",
            riskDataId:null,
            form: {
                keyword: "",
                type: 1,
                page: 1,
                size: 10,
                total: 0,
            },
            multipleSelection:[],
        }
    },

    methods:{
        init(riskDataId){
            this.dialogVisible = true
            this.riskDataId = riskDataId
            this.searchForm(true)
        },
        handleSelectionChange(val){
          this.multipleSelection = val
        },
        // // 厂商选框选中数据
        // handleSelectionChangeSup(val) {
        //     let index = this.tableData.findIndex((item) => {
        //         return item.supplierCode == val;
        //     });
        //     if (index > -1) {
        //         this.supplierCheck = {
        //             supplierName: this.tableData[index].supplierName,
        //             supplierCode: this.tableData[index].supplierCode,
        //         };
        //     }
        // },

        searchForm(flag){
            if(flag){
                this.form.keyword = ''
                this.form.page = 1
            }
            this.loadData()
        },

        loadData(){
            this.loading = true
            getSupplierPage(this.form).then((res) => {
                this.tableData = res.data;
                this.form.total = res.total;
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            });
        },

        submitFormSupplier() {
            this.btnLoading = true
            let supplierList = []
            this.multipleSelection.forEach(item =>{
              let obj = {
                supplierNo:item.supplierCode,
                supplierName:item.supplierName
              }
              supplierList.push(obj)
            })
            addSupplierInfo({
                id: this.riskDataId,
                supplierList:supplierList,
            }).then((response) => {
                this.btnLoading = false
                this.$modal.msgSuccess("新增成功");
                this.$emit('getProjectRiskDept')
                this.dialogVisible = false;
            }).catch(()=>{
                this.btnLoading = false
            });
        },

        cancel(){
            this.dialogVisible = false
        }
    }

}

</script>

<style scoped lang='less'>
</style>