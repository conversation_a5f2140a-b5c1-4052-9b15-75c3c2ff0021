<template>
  <el-dialog title="文件预览" :visible.sync="dialogVisible" :width="width">
    <div class="dailog_con" v-loading="loading">
        <pdf class="base_pdf" :src="pdfSrc" ref="pdf" @click.native="handleView"></pdf>
    </div>
    <pdf-view :showClose="true" :dochref="pdfSrc" ref="pdfView" ></pdf-view>
  </el-dialog>
</template>

<script>
import {getFileUrl} from '@/api/publicReq.js'
import pdf from "vue-pdf";
import PdfView from './pdfView/pdf-view-dailog.vue';
export default {
  components: { pdf,PdfView },
  
  props:{
    width:{
      type:String,
      default(){
        return '40%'
      }
    }
  },

  data() {
    return {
      dialogVisible: false,
      error:false,
      loading:false,
      fileId:null,
      pdfSrc: null,
    };
  },

  created() {},

  methods: {
    async init(fileId) {
      this.dialogVisible = true;
      this.error = false
      this.pdfSrc = null
      this.fileId = fileId
      await this.getFileUrl()
    },

    // 校验文件
    checkError(pdfSrc) {
      let loadingTask = pdf.createLoadingTask(pdfSrc);
      try{
        loadingTask.promise.catch(()=>{
          this.$message({
            type:'error',
            message:'文件预览错误！',
            duration:2000
          })
          this.error = true
        })
      }catch(e){

      }
      
    },

    getFileUrl(){
      this.loading = true
      getFileUrl(this.fileId).then((res)=>{
        this.pdfSrc = res.data
        this.checkError(this.pdfSrc)
        this.loading = false
      }).catch(()=>{
        this.loading = false
      })
    },

    handleView(){
      if(this.error)return;
      this.$refs.pdfView.init(this.pdfSrc)
    },

  }

};
</script>

<style lang='less' scoped>
.dailog_con {
  min-height: 30vh;
  .base_pdf {
    cursor:pointer;
  }
}
</style>