import request from '@/utils/request'
// 状态阀
export function getAlarmList(data) {
  return request({
    url: `/back-server/alarm/getAlarmList`,
    method: 'get',
    params: data,
  })
}
// 推送人列表
export function getPushUserList(data) {
  return request({
    url: `/back-server/alarm/getPushUserList`,
    method: 'get',
    params: data,
  })
}
// 修改
export function updateAlarm(data) {
  return request({
    url: `/back-server/alarm/updateAlarm`,
    method: 'post',
    data,
  })
}
//上传文件
export function uploadImagesFiles(data) {
  return request({
    url: `/user-server/file/uploadImagesFiles`,
    method: 'post',
    data,
  })
}