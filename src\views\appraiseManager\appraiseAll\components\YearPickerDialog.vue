<template>
  <el-dialog title="选择年份" :visible.sync="visible" width="30%" :close-on-click-modal="false">
    <div class="year-picker-container">
      <el-date-picker v-model="selectedYear" type="year" placeholder="选择年份" value-format="yyyy" />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'YearPickerDialog',
  data() {
    return {
      visible: false,
      selectedYear: ''
    }
  },
  methods: {
    open() {
      this.visible = true
      this.selectedYear = new Date().getFullYear().toString()
    },
    handleConfirm() {
      if (!this.selectedYear) {
        this.$message.warning('请选择年份')
        return
      }
      this.$emit('confirm', this.selectedYear)
      this.visible = false
    }
  }
}
</script>

<style scoped>
.year-picker-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}
</style>
