import request from '@/utils/request'
// 责任部门分页列表
export function getRiskDeptPage(param) {
    return request({
        url: '/mes-server/project/risk/dept/page',
        method: 'get',
        params: param,
    })
}

// 新增
export function addRisk(param) {
    return request({
        url: '/mes-server/project/risk/',
        method: 'post',
        data: param,
    })
}

// 查询部门人员
export function getDutyUserPage(param) {
    return request({
        url: '/mes-server/duty/user/page',
        method: 'get',
        params: param,
    })
}

// 查询厂商
export function getSupplierPage(param) {
    return request({
        url: '/user-server/supplier/getListPage',
        method: 'get',
        params: param,
    })
}


// 详情
export function getProjectRiskDept(id) {
    return request({
        url: `/mes-server/project/risk/dept/${id}`,
        method: 'get',
    })
}

// 新增部门人员
export function addDeptInfo(param) {
    return request({
        url: `/mes-server/project/risk/dept/dept/info`,
        method: 'put',
        data: param
    })
}

// 新增厂商
export function addSupplierInfo(param) {
    return request({
        url: `/mes-server/project/risk/dept/supplier/info`,
        method: 'put',
        data: param
    })
}

// 反馈
export function addFeedback(param) {
    return request({
        url: `/mes-server/project/risk/dept/feedback`,
        method: 'put',
        data: param
    })
}

// 二级部门领导审核
export function leaderStatus(param) {
    return request({
        url: `/mes-server/project/risk/dept/dept/leader/status`,
        method: 'put',
        data: param
    })
}

// 部门审核
export function setDeptStatus(param) {
    return request({
        url: `/mes-server/project/risk/dept/dept/status`,
        method: 'put',
        data: param
    })
}

// 厂商审核
export function setSupplierStatus(param) {
    return request({
        url: `/mes-server/project/risk/dept/supplier/status`,
        method: 'put',
        data: param
    })
}