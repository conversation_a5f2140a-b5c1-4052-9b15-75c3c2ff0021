//绑定事件，可重复绑定('事件名称'必须加引号)
export function bind(obj, evname, fn, params = false) {
    if (obj.addEventListener) {
        obj.addEventListener(evname, fn, params);
        if (evname == 'mousewheel') {
            obj.addEventListener('DOMMouseScroll', fn, params);
        }
    } else {
        obj.attachEvent('on' + evname, function () {
            fn.call(obj);
        });
    }
};

//取消绑定，可重复取消('事件名称'必须加引号)
export function unbind(obj, evname, fn, params = false) {
    if (obj.removeEventListener) {
        obj.removeEventListener(evname, fn, params);
        if (evname == 'mousewheel') {
            obj.removeEventListener('DOMMouseScroll', fn, params);
        }
    } else {
        obj.detachEvent('on' + evname, fn);
    }
};

// 拖拽

export function objDrag(id) {
    var _this = this;
    this.oDiv = document.getElementById(id);
    this.oDiv.onmousedown = function (event) {
        _this.fnDown(event);
        return false;
    }
    this.positionArray = []
    this.nX = null;
    this.nY = null;
    this.timer = null;
}

objDrag.prototype.fnDown = function (event) {
    var _this = this;
    var evt = event || window.event;
    this.nX = evt.clientX - this.oDiv.offsetLeft;
    this.nY = evt.clientY - this.oDiv.offsetTop;
    document.onmousemove = function (event) {
        _this.fnMove(event);
    }
    this.oDiv.onmouseup = function (event) {
        _this.fnUp(event)
    };
}

objDrag.prototype.fnMove = function (event) {
    var evt = event || window.event;
    var left = evt.clientX - this.nX;
    var top = evt.clientY - this.nY;
    this.oDiv.style.left = left + 'px';
    this.oDiv.style.top = top + 'px';
    this.positionArray.push({
        left: this.oDiv.offsetLeft,
        top: this.oDiv.offsetTop
    });
}

objDrag.prototype.returnWay = function () { // 拖拽轨迹原路返回
    let positionArray = this.positionArray
    this.positionArray = []
    let _this = this
    var index = positionArray.length - 1;
    this.timer = setInterval(function () {
        if (index < 0) {
            clearInterval(this.timer);
            return;
        }
        if (positionArray.length) {
            if (_this.oDiv.style) {
                _this.oDiv.style.left = positionArray[index--].left + "px";
                _this.oDiv.style.top = positionArray[index--].top + "px";
            }
        }
    }, 1);
}

objDrag.prototype.fnUp = function () {
    document.onmousemove = null;
}
