<template>
    <el-dialog
    :title="rowData ? '新增' : '修改'"
    :visible.sync="dialogVisible"
    width="40%"
    >
        <div class="dialog_con">
            <el-form :model="form" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm">
<!--                <el-col :span="24">-->
<!--                    <el-form-item label="板块名称" prop="moduleId">-->
<!--                        <el-select v-model="form.moduleId" placeholder="板块名称">-->
<!--                            <el-option-->
<!--                            v-for="item in moduleOptions"-->
<!--                            :key="item.id"-->
<!--                            :label="item.name"-->
<!--                            :value="item.id">-->
<!--                            </el-option>-->
<!--                        </el-select>-->
<!--                    </el-form-item>-->
<!--                </el-col>-->

                <el-col :span="24">
                    <el-form-item label="分类名称" prop="categoryName">
                        <el-input size="small" v-model="form.categoryName" placeholder="分类名称" clearable></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item label="分类编码" prop="code">
                        <el-input size="small" v-model="form.code" placeholder="分类编码" clearable></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item label="备注" prop="remark">
                        <el-input
                            clearable
                            type="textarea"
                            :rows="5"
                            resize="none"
                            placeholder="备注"
                            v-model="form.remark">
                        </el-input>
                    </el-form-item>
                </el-col>

            </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" @click="dialogVisible = false">取 消</el-button>
            <el-button size="small" :loading="btnLoading" type="primary" @click="confim()">确 定</el-button>
        </div>
    </el-dialog>
</template>
<script>
import {addProcessClass,editProcessClass} from '@/api/processMan/processClass.js'
// import { getPlateList } from "@/api/platformMan/plateMan";
export default {
    data(){
        return {
            btnLoading:false,
            dialogVisible:false,
            rowData:null,
            iconLoading:false,
            moduleOptions:[],
            form:{
                categoryName:'',
                moduleId:'',
                code:'',
                remark:''
            },
            rules:{
                moduleId:[
                    { required: true, message: '请选择板块名称', trigger: 'blur' }
                ],
                categoryName: [
                    { required: true, message: '请输入分类名称', trigger: 'blur' }
                ],
                code: [
                    { required: true, message: '请输入分类编码', trigger: 'blur' }
                ],
                remark: [
                    { required: true, message: '请输入备注', trigger: 'blur' }
                ]
            }
        }
    },
    methods:{
        async init(row){
            this.dialogVisible = true
            // await this.getPlateList()
            this.rowData = row
            this.$nextTick(()=>{
                if(row){
                    this.form = JSON.parse(JSON.stringify(row))
                }else{
                    this.form = {
                        categoryName:'',
                        code:'',
                        remark:''
                    }
                }
                this.$refs['form'].resetFields();
            })
        },

        // getPlateList(){
        //     getPlateList().then((res) => {
        //     this.moduleOptions = res.data;
        //   });
        // },

        confim(){
            this.$refs['form'].validate((valid) => {
                if(valid){
                    this.btnLoading = true
                    let params = {
                        ...this.form
                    }
                   if(this.rowData){
                        editProcessClass(params).then((res)=>{
                           this.$message({
                               type:'success',
                               message:'操作成功',
                               duration:1500
                           })
                           this.dialogVisible = false
                           this.btnLoading = false
                           this.$emit('hideDialog')
                       }).catch(()=>{
                           this.btnLoading = false
                       })
                   }else{
                       addProcessClass(params).then((res)=>{
                           this.$message({
                               type:'success',
                               message:'操作成功',
                               duration:1500
                           })
                           this.dialogVisible = false
                           this.btnLoading = false
                           this.$emit('hideDialog')
                       }).catch(()=>{
                           this.btnLoading = false
                       })
                   }
                }
            })
        }
    }
}
</script>
<style lang="less" scoped>
.demo-ruleForm {
    /deep/.el-form-item {
        .el-form-item__content {
            .el-select {
                width:100%;
            }
        }
    }
    .avatar-uploader {
        display:inline-block;
        width:50px;
        height:50px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        /deep/.el-upload {
            display:flex;
            align-items:center;
            justify-content: center;
            width:100%;
            height:100%;
            .avatar {
                width:100%;
                height:100%;
            }
        }
        /deep/.el-loading-mask {
            .el-loading-spinner {
                margin-top:-17px;
            }
        }
    }
    .avtar_hint {
        display:inline-block;
        font-size:12px;
        line-height:20px;
        color:#F56C6C;
        margin-left:10px;
    }
}
</style>
