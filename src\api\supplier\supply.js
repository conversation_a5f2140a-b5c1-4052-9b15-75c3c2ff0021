import request from "@/utils/request";
// 状态阀
export function getSupplierList(data) {
  return request({
    url: `/back-server/castingPurchase/supplier/list`,
    method: "get",
    params: data,
  });
}

// 获取供应商统计数据
export function getSupplierSummary(params) {
  return request({
    url: "/back-server/castingPurchase/supplier/summary",
    method: "get",
    params,
  });
}

// 获取供应商记录分页列表
export function getSupplierRecordList(params) {
  return request({
    url: "/back-server/castingPurchase/supplier/record",
    method: "get",
    params,
  });
}

// 供应商完成情况列表
export function getSupplierCompletionList(params) {
  return request({
    url: "/back-server/castingPurchase/casting/supplier",
    method: "get",
    params,
  });
}

//供应商完成情况汇总
export function getSupplierCompletionSummary(params) {
  return request({
    url: "/back-server/castingPurchase/casting/summary",
    method: "get",
    params,
  });
}
