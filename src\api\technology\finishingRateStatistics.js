import request from '@/utils/request'
// 分页查询
export function getRatePage(param) {
    return request({
        url: '/back-server/rate/page',
        method: 'get',
        params: param,
    })
}

// 新增
export function addRate(param) {
    return request({
        url: '/back-server/rate/add',
        method: 'post',
        data: param,
    })
}

// 修改
export function updateRate(param) {
    return request({
        url: '/back-server/rate/update',
        method: 'post',
        data: param,
    })
}

// 删除
export function delRate(param) {
    return request({
        url: '/back-server/rate/delete',
        method: 'get',
        params: param,
    })
}