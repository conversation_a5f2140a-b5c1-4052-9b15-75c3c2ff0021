import request from '@/utils/request'


// // 分页获取用户列表
// export function userList(query) {
//   return request({
//     url: `/system/user/page`,
//     method: "get",
//     params: query,
//   });
// }
// 查询用户列表
export function userList(query) {
  return request({
    url: '/user-server/appUser/getListPage',
    method: 'get',
    params: query,
  })
}


// 根据部门获取用户
export function listUser(query) {
  return request({
    url: `/user-server/appUser/dept`,
    method: "get",
    params: query,
  });
}


