<template>
  <el-upload
    :action="
      hbcApp
        ? `/appFile/file`
        : `/file/minio/upload/${this.bucketName}/${this.moduleId}`
    "
    :accept="accept"
    :file-list="fileLists"
    :multiple="multiple"
    :show-file-list="showFileList"
    :on-success="fileSuccess"
    :before-upload="beforeUpload"
    :on-error="fileError"
    :on-preview="filePreview"
    :on-exceed="fileExceed"
    :before-remove="beforeRemove"
    :on-remove="onRemove"
    :headers="importHeaders"
    :limit="limit"
    :disabled="disabled"
  >
    <slot></slot>
  </el-upload>
</template>

<script>
export default {
  // inheritAttrs: false,

  props: {
    rowData: [String, Object, Number],

    // 判断是app还是web
    hbcApp: {
      type: Boolean,
      default() {
        return false;
      },
    },

    // 是否支持多文件上传
    multiple: {
      type: Boolean,
      default() {
        return false;
      },
    },

    // 板块id
    moduleId: {
      type: String,
      default() {
        return "";
      },
    },
    // 是否显示上传文件列表 true 显示  false 不显示
    showFileList: {
      type: <PERSON>olean,
      default() {
        return false;
      },
    },

    // file文件列表
    fileList: {
      type: Array,
      default() {
        return [];
      },
    },

    // 板块名称
    bucketName: {
      type: String,
      default() {
        return "public";
      },
    },

    // 上传文件类型
    accept: {
      type: String,
      default() {
        return "";
      },
    },

    // 上传文件个数
    limit: {
      type: Number,
      default() {
        return 10;
      },
    },
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  computed: {
    fileLists() {
      return this.fileList;
    },
  },

  data() {
    return {
      importHeaders: {
        Authorization: `Bearer ${sessionStorage.getItem("token")}`,
      },
    };
  },

  methods: {
    // 上传文件前
    beforeUpload(file) {
      let rowData = this.rowData;
      let state = true;
      this.$emit(
        "before-upload",
        file,
        (val) => {
          state = val;
        },
        rowData
      );
      return state;
    },

    // 上传文件成功
    fileSuccess(response, file, fileList) {
      let rowData = this.rowData;
      switch (response.code) {
        case -1:
          let message = response.msg || "请求失败";
          this.$message({
            type: "error",
            message,
            duration: 1500,
          });
          this.$emit("file-error", response, rowData);
          break;
        default:
          this.$emit("file-success", response, file, fileList, rowData);
          break;
      }
    },

    // 文件上传失败
    fileError(error) {
      let rowData = this.rowData;
      this.$emit("file-error", error, rowData);
    },

    // 删除已上传文件前
    beforeRemove(file, fileList) {
      let rowData = this.rowData;
      this.$emit("before-remove", file, fileList, rowData);
    },

    onRemove(file, fileList) {
      this.$emit("on-remove", file, fileList);
    },

    // 点击下载文件
    filePreview(file) {
      let rowData = this.rowData;
      this.$emit("file-preview", rowData, file);
    },

    // 文件超出最大数限制
    fileExceed(files, fileList) {
      let rowData = this.rowData;
      this.$emit("file-exceed", files, fileList, rowData);
    },
  },
};
</script>

<style scoped lang="less"></style>
