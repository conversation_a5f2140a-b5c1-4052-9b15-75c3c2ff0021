import request from "@/utils/request";
// 分页查询节点模型
export function selectPage(param) {
  return request({
    url: "/back-server/nodeTemplate/selectPage",
    method: "get",
    params: param,
  });
}
// 新增节点模型
export function addSelectPage(param) {
  return request({
    url: "/back-server/nodeTemplate/add",
    method: "post",
    data: param,
  });
}
//批量删除节点模型
export function deletes(ids) {
  return request({
    url: `/back-server/nodeTemplate/deletes?ids=${ids}`,
    method: "get",
  });
}

// 分页查询节点模型-详情列表
export function detailSelectPage(param) {
  return request({
    url: "/back-server/nodeTemplateDetail/selectPage",
    method: "get",
    params: param,
  });
}
// 节点模型-新增节点
export function addDetailSelectPage(param) {
  return request({
    url: "/back-server/nodeTemplateDetail/add",
    method: "post",
    data: param,
  });
}
// 节点模型-修改节点
export function updateDetailSelectPage(param) {
  return request({
    url: "/back-server/nodeTemplateDetail/update",
    method: "post",
    data: param,
  });
}
//节点模型-批量删除详情
export function deletesDetailSelectPage(ids) {
  return request({
    url: `/back-server/nodeTemplateDetail/deletes?ids=${ids}`,
    method: "get",
  });
}
//导出
export function exportData(query) {
  return request({
    url: "/back-server/nodeTemplate/exportExcel",
    method: "get",
    params: query,
    responseType: "blob", // important
  });
}
//模板
export function exportExcelTemplate() {
  return request({
    url: "/back-server/nodeTemplate/downloadExcel",
    method: "get",
    responseType: "blob", // important
  });
}
//模板
export function updateAlarmStatus(param) {
  return request({
    url: "/back-server/nodeTemplate/updateAlarmStatus",
    method: "get",
    params: param,
  });
}
// 更新采购组类型
export function updateGroupType(data) {
  return request({
    url: "/back-server/nodeTemplate/group/type",
    method: "put",
    data: data,
  });
}
