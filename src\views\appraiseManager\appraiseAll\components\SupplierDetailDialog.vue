<template>
  <el-dialog title="供方画像" :visible.sync="dialogVisible" width="800px" :before-close="handleClose"
    custom-class="supplier-portrait-dialog" :top="'2vh'" destroy-on-close>
    <div v-loading="loading" class="supplier-info">
      <template v-if="supplierData">
        <div class="basic-info">
          <h3>{{ supplierData.name }}</h3>
          <div class="info-row">
            <span>供方编号：{{ supplierData.code }}</span>
            <span>准入时间：{{ supplierData.entryDate }}</span>
          </div>
          <div class="info-row">
            <span>供方编码：{{ supplierData.supplierCode }}</span>
          </div>
        </div>

        <!-- 评分详情 -->
        <div class="score-details">
          <!-- <div class="score-row">
            <div v-for="(item, index) in supplierData.scoreDetails" :key="index" class="score-item">
              <div class="total-score">总分：<span class="rank-text">{{ item.score }}</span></div>
              <div class="rank">排名：<span class="rank-text">第{{ item.rank }}名</span></div>
            </div>
          </div> -->

          <!-- 使用 el-tabs -->
          <el-tabs v-model="activeTab" class="supplier-tabs" @tab-click="handleTabClick">
            <el-tab-pane v-for="(item, index) in scoreDetails" :key="index" :label="item.typeNo" :name="item.typeNo">
              <template #label>
                <div class="tab-label" @click="handleTabClicks(item)">
                  <div class="score-rank">
                    <div><span class="score-rank-text">总分：</span>{{ item.dto.totalScore }}</div>
                    <div><span class="score-rank-text">排名：</span>第{{ item.dto.ranking }}名</div>
                  </div>
                  <div class="tab-label-code">
                    <el-tooltip :content="item.typeName" placement="top">
                      <span>{{ item.typeNo }}</span>
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 物料信息 -->
        <div class="material-info" v-if="dto">
          <div class="material-name">{{ dto.materialName }}</div>
          <div class="material-desc">{{ dto.materialDesc }}</div>
        </div>

        <!-- 评分指标表格 -->
        <div class="metrics-table" v-if="dto">
          <div class="table-row">
            <div class="table-cell">质量</div>
            <div class="table-cell">交货</div>
            <div class="table-cell">服务</div>
            <div class="table-cell">技术</div>
            <div class="table-cell">价格</div>
            <div class="table-cell">响标度</div>
            <div class="table-cell">供货</div>
          </div>
          <div class="table-row values">
            <div class="table-cell">{{ dto.qualityScore }}</div>
            <div class="table-cell">{{ dto.deliveryScore }}</div>
            <div class="table-cell">{{ dto.serveScore }}</div>
            <div class="table-cell">{{ dto.technologyScore }}</div>
            <div class="table-cell">{{ dto.priceScore }}</div>
            <div class="table-cell">{{ dto.bidScore }}</div>
            <div class="table-cell">{{ dto.supplyScore }}</div>
          </div>
        </div>
        <div v-else>
          <el-empty description="暂无数据" />
        </div>
        <!-- 雷达图 -->
        <div ref="radarChart" class="radar-chart" />
      </template>
    </div>
  </el-dialog>
</template>

<script>
import * as echarts from 'echarts'
import { apiGetSupplierDetail } from '@/api/appraiseManager/appraiseAll'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: ''
    }
  },

  data() {
    return {
      dialogVisible: false,
      loading: false,
      supplierData: null,
      chart: null,
      currentTabIndex: 0,
      activeTab: '', // 当前激活的标签页
      scoreDetails: [],
      indicator: [],
      dto: null
    }
  },

  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.row) {
        this.fetchSupplierData()
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },

  methods: {
    handleTabClick(tab) {
      this.$nextTick(() => {
        const tabsWrapper = this.$el.querySelector('.el-tabs__header .el-tabs__nav-scroll');
        const activeTabEl = this.$el.querySelector(`.el-tabs__item.is-active`);
        if (tabsWrapper && activeTabEl) {
          // 滚动到选中的标签
          tabsWrapper.scrollLeft = activeTabEl.offsetLeft - tabsWrapper.offsetLeft;
        }
      });
    },
    // 获取数据
    async fetchSupplierData() {
      this.loading = true
      const res = await apiGetSupplierDetail({ ...this.row })
      const { supplierName, supplierCode, supplierNo, joinTime } = this.row
      // 更新模拟数据
      this.supplierData = {
        name: supplierName,
        code: supplierCode,
        entryDate: joinTime,
        supplierCode: supplierNo,
      }
      if (!res.data || !res.data.length) {
        this.loading = false
        return
      }
      this.scoreDetails = res.data

      // 设置默认激活的标签页
      this.activeTab = this.scoreDetails[0]?.typeNo
      this.dto = this.scoreDetails[0]?.dto
      if (this.scoreDetails[0]?.dto.supplyStatus) {
        this.indicator = [
          { name: '质量', max: 30 },
          { name: '交货', max: 15 },
          { name: '服务', max: 15 },
          { name: '技术', max: 10 },
          { name: '价格', max: 10 },
          { name: '响标度', max: 10 },
          { name: '供货', max: 10 }
        ]

      } else {
        this.indicator = [
          { name: '质量', max: 0 },
          { name: '交货', max: 0 },
          { name: '服务', max: 0 },
          { name: '技术', max: 50 },
          { name: '价格', max: 30 },
          { name: '响标度', max: 20 },
          { name: '供货', max: 0 }
        ]
      }
      this.$nextTick(() => {
        this.initRadarChart()
      })

      this.loading = false
    },

    initRadarChart() {
      if (!this.supplierData) return

      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = echarts.init(this.$refs.radarChart)

      // 计算雷达图的百分比值
      const calculateRadarPercentage = (value, maxValue) => {
        return maxValue === 0 ? 0 : Math.round((value / maxValue) * 100)
      }

      const option = {
        backgroundColor: 'transparent',
        radar: {
          indicator: this.indicator,
          shape: 'polygon',
          radius: '65%',
          axisName: {
            color: '#333',
            fontSize: 12,
            padding: [0, 15]
          },
          splitLine: {
            lineStyle: {
              color: '#E4E7ED',
              width: 1
            }
          },
          splitArea: {
            show: true,
            areaStyle: {
              color: ['rgba(255,255,255,0.3)']
            }
          },
          center: ['50%', '50%'],
          name: {
            textStyle: {
              padding: [20, 0, 0, 0]
            }
          }
        },
        series: [{
          type: 'radar',
          data: [{
            value: [
              this.dto.qualityScore,
              this.dto.deliveryScore,
              this.dto.serveScore,
              this.dto.technologyScore,
              this.dto.priceScore,
              this.dto.bidScore,
              this.dto.supplyScore,
              // calculateRadarPercentage(this.dto.qualityScore, this.indicator[0].max),
              // calculateRadarPercentage(this.dto.deliveryScore, this.indicator[1].max),
              // calculateRadarPercentage(this.dto.serveScore, this.indicator[2].max),
              // calculateRadarPercentage(this.dto.technologyScore, this.indicator[3].max),
              // calculateRadarPercentage(this.dto.priceScore, this.indicator[4].max),
              // calculateRadarPercentage(this.dto.bidScore, this.indicator[5].max),
              // calculateRadarPercentage(this.dto.supplyScore, this.indicator[6].max)
            ],
            name: '评分指标',
            label: {
              show: true,
              formatter: (params) => {
                return params.value
              },
              distance: -15,
              color: '#333',
              fontSize: 11,
              position: 'inside',
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              padding: [2, 4],
              borderRadius: 2
            },
            areaStyle: {
              color: 'rgba(64, 158, 255, 0.2)'
            },
            lineStyle: {
              color: '#409EFF',
              width: 1
            },
            itemStyle: {
              color: '#409EFF'
            }
          }]
        }]
      }

      this.chart.setOption(option)
    },

    handleClose() {
      this.dialogVisible = false
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
      this.supplierData = null
    },

    handleTabClicks(item) {
      this.dto = item.dto
      this.activeTab = item.typeNo
      this.initRadarChart()
    }
  },

  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  }
}
</script>

<style scoped lang="scss">
.supplier-portrait-dialog {
  min-width: 700px;
}

.supplier-info {
  height: 80vh;
  overflow-y: auto;
  padding: 20px;
}

.basic-info {
  margin-bottom: 20px;
}

h3 {
  margin: 0;
  font-size: 16px;
  font-weight: normal;
  margin-bottom: 15px;
}

.info-row {
  color: #666;
  font-size: 14px;
  line-height: 1.8;
}

.info-row span {
  margin-right: 30px;
}

.score-details {
  margin: 20px 0;
}

.score-row {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.score-item {
  flex: 1;
  text-align: center;
}

.total-score {
  color: #333;
  font-size: 14px;
}

.rank {
  margin: 5px 0;
  font-size: 14px;
}

.rank-text {
  font-size: 15px;
  color: #409EFF;
  font-weight: bold;
}

.material-info {
  margin: 20px 0;
  text-align: center;
}

.material-name {
  text-align: center;
  font-size: 18px;
  margin-bottom: 8px;
  font-weight: bold;
}

.material-desc {
  color: #666;
  font-size: 12px;
}

.metrics-table {
  margin: 20px 60px;
  background-color: #F5F7FA;
  border-radius: 4px;
}

.table-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 15px;
  text-align: center;
}

.table-cell {
  flex: 1;
  font-size: 14px;
  font-weight: bold;
}

.values .table-cell {
  color: #333;
}

.radar-chart {
  width: 100%;
  height: 400px;
  margin-top: 20px;
}

/* 覆盖 element-ui 的默认样式 */
:deep(.el-dialog__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #EBEEF5;
}

.supplier-tabs {
  margin-top: 10px;
}

/* 自定义 el-tabs 样式 */
:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  height: 30px;
  line-height: 30px;
  padding: 0 15px;
  font-size: 12px;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
  background-color: #ECF5FF;
  border-radius: 4px;
}

:deep(.el-tabs__nav) {
  border: none;
}

:deep(.el-tabs__active-bar) {
  display: none;
}

:deep(.el-tabs__item:hover) {
  color: #409EFF;
}

:deep(.el-tabs__content) {
  display: none;
  /* 隐藏内容区域，因为我们不需要显示标签页内容 */
}

.tab-label {
  font-size: 14px;
  text-align: center;
}

.score-rank {
  color: #409EFF;
  margin-top: 3px;
  font-weight: bold;
  font-size: 15px;
  text-align: left;

  .score-rank-text {
    color: #999
  }

}

// .tab-label-code {
//   border: 1px solid #409EFF;
//   border-radius: 4px;
//   padding: 2px 4px;
//   margin-top: 3px;
// }

.score-rank span {
  margin: 0 5px;
}

/* 调整tab的高度以适应两行内容 */
:deep(.el-tabs__item) {
  height: auto;
  padding: 5px 15px;
  line-height: 1.4;
}
</style>
