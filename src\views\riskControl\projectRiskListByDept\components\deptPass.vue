<template>
    <el-dialog
    :visible.sync="dialogVisible"
    append-to-body
    width="30%"
    title="通过"
    @close="cancel"
    >
      <div>是否确认该环节审批通过</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading"
          >确 定</el-button
        >
        <el-button @click="cancel"
          >取 消</el-button
        >
      </div>
    </el-dialog>
</template>

<script>
import { setDeptStatus,setSupplierStatus } from '@/api/riskControl/projectRiskListByDept'
export default {
    components:{},

    data(){
        return{
            dialogVisible:false,
            btnLoading:false,
            rowData:null,
            riskDataId:null,
            isSuppiler:null,
        }
    },

    methods:{
        init(riskDataId,row,isSuppiler){
            this.dialogVisible = true
            this.btnLoading = false,
            this.isSuppiler = isSuppiler
            this.riskDataId = riskDataId
            this.rowData = row
        },
        

        // 审核确定
        confirm() {
            if(this.isSuppiler){
                let params = {
                    id: this.riskDataId,
                    projectRiskSupplierId: this.rowData.id,
                    status: 1,
                }
                this.btnLoading = true
                setSupplierStatus(params).then((res) => {
                    this.$modal.msgSuccess("操作成功");
                    this.btnLoading = false;
                    this.dialogVisible = false;
                    this.$emit('getProjectRiskDept')
                })
            }else{
                let params = {
                    id: this.riskDataId,
                    projectRiskDeptId: this.rowData.id,
                    status: 1,
                }
                this.btnLoading = true
                setDeptStatus(params).then((res) => {
                    this.$modal.msgSuccess("操作成功");
                    this.btnLoading = false;
                    this.dialogVisible = false;
                    this.$emit('getProjectRiskDept')
                })
            }
        },

        cancel(){
            this.dialogVisible = false
        }
    },

}

</script>

<style scoped lang='less'>
</style>