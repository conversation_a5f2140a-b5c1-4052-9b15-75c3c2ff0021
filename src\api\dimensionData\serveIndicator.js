import request from "@/utils/request";
// 分页查询
export function apiGetList(data) {
  return request({
    url: "/sd-server/serve",
    method: "get",
    params: data,
  });
}
// 新增
export function apiAdd(data) {
  return request({
    url: "/sd-server/serve",
    method: "post",
    data: data,
  });
}
// 修改
export function apiUpdate(data) {
  return request({
    url: "/sd-server/serve",
    method: "put",
    data: data,
  });
}
// 删除
export function apiDelete(data) {
  return request({
    url: "/sd-server/serve",
    method: "delete",
    data: data,
  });
}
