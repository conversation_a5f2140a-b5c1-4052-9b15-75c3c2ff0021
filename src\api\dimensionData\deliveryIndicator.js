import request from "@/utils/request";
// 分页查询
export function apiGetPage(data) {
  return request({
    url: "/sd-server/delivery",
    method: "get",
    params: data,
  });
}
// 新增
export function apiAdd(data) {
  return request({
    url: "/sd-server/delivery",
    method: "post",
    data,
  });
}
// 修改
export function apiUpdate(data) {
  return request({
    url: "/sd-server/delivery",
    method: "put",
    data,
  });
}
// 删除
export function apiDelete(data) {
  return request({
    url: "/sd-server/delivery",
    method: "delete",
    data,
  });
}
// 确认
export function apiConfirm(data) {
  return request({
    url: "/sd-server/delivery/submit",
    method: "put",
    data,
  });
}
// 批量确认
export function apiConfirmBatch(data) {
  return request({
    url: "/sd-server/delivery/submit/batch",
    method: "put",
    data,
  });
}
// 导出
export function exportData(data) {
  return request({
    url: "/sd-server/delivery/upload",
    method: "get",
    params: data,
    responseType: "blob",
  });
}
