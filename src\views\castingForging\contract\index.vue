<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="机组名称" prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="机组令号" prop="posid">
        <el-input v-model="queryParams.posid" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="部套名称" prop="dwgName">
        <el-input v-model="queryParams.dwgName" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="部套图号" prop="dwgNo">
        <el-input v-model="queryParams.bismt" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="零件名称" prop="bismtName">
        <el-input v-model="queryParams.bismtName" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="零件图号" prop="bismtNo">
        <el-input v-model="queryParams.bismtName" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="供应商名称" prop="supplierName">
        <el-input v-model="queryParams.supplierName" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="供应商编码" prop="supplierCode">
        <el-input v-model="queryParams.supplierCode" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="合同编号" prop="contractNo">
        <el-input v-model="queryParams.contractNo" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="签订人" prop="contractUserName">
        <el-input v-model="queryParams.contractUserName" placeholder="请输入" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="alarmStatus">
        <el-select v-model="queryParams.alarmStatus" placeholder="请选择" clearable>
          <el-option v-for="dict in status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="采购订单号" prop="ebeln">
        <el-input v-model="queryParams.ebeln" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="采购订单行号" prop="ebelp">
        <el-input v-model="queryParams.ebelp" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item style="margin-left: 40px;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="info" icon="el-icon-upload2" size="mini" plain :disabled="total == 0"
          @click="handleImp">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleImport">导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange" height="50vh">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" align="center" />
      <el-table-column label="零件图号" prop="bismtNo" width="150" align="center" />
      <el-table-column label="零件名称" prop="bismtName" width="200" align="center" />
      <el-table-column label="图号" prop="bismt" width="150" align="center" />
      <el-table-column label="当前节点" prop="nodeName" width="120" align="center" />
      <el-table-column label="记录数" prop="nodeRecordNum" width="120" align="center" />
      <el-table-column label="到货时间" prop="arrivalTime" width="120" align="center" />
      <el-table-column label="供应商" prop="supplierName" width="220" align="center" show-overflow-tooltip />
      <el-table-column label="合同编号" prop="contractNo" width="150" align="center" />
      <el-table-column label="合同签订人" prop="contractUserName" width="120" align="center" />
      <el-table-column label="部套名称" prop="dwgName" width="200" align="center" />
      <el-table-column label="部套编码" prop="dwgNo" width="150" align="center" />
      <el-table-column label="机组名称" prop="projectName" width="200" align="center" show-overflow-tooltip />
      <el-table-column label="令号" prop="posid" width="120" align="center" />
      <el-table-column label="采购订单号" prop="ebeln" width="120" align="center" />
      <el-table-column label="采购订单行号" prop="ebelp" width="120" align="center" />
      <el-table-column label="完成状态" prop="alarmStatus" align="center">
        <template slot-scope="scope">
          <el-tag :type="{ 1: 'danger', 2: 'warning', 3: 'success', 4: 'info' }[scope.row.alarmStatus]">{{
            ["报警", "预警", "正常", "完成"][scope.row.alarmStatus - 1] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="showItem(scope.row)">详情</el-button>
        </template></el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.size"
      @pagination="getList" />
    <!-- 项目名称详情弹框 -->
    <el-dialog title="铸锻件合同详情" :visible.sync="itemOpen" width="980px" append-to-body>
      <div class="dialog-content" v-loading="itemLoading">
        <div class="content">
          <el-row>
            <el-col :span="24">
              <div class="grid-content bg-purple">
                <span>公司名称：<span>{{ posidList.supplierName }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>联系人：<span>{{ posidList.projectManager }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>电话：<span>{{ posidList.phone }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>当前节点：<span>{{ posidList.currentNodeName }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>步骤节点：<span>{{ posidList.currentNode }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>节点记录数：<span>{{
                  posidList.nodeRecordCount
                    }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>合同开始时间：<span>{{ posidList.startTime }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>约定交货时间：<span>{{ posidList.plifz }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>计划需求时间：<span>{{ posidList.frgdt }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>实际完成时间：<span>{{ posidList.budatMkp }}</span></span>
              </div>
            </el-col>

            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>厂家预计完成时间：<span>{{ posidList.zrsv08 }}</span></span>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="block">
          <el-timeline>
            <el-timeline-item color="#1890FF" v-for="(item, index) in posidList.nodeList" :key="index">
              <div class="timeline-item-top">
                <div class="top-left">
                  <div class="top-title">{{ item.nodeName }}</div>
                  <div class="top-content">
                    <p>规定完成时间：{{ item.planTime }}</p>

                    <p v-if="item.finishTime">实际完成时间：{{ item.finishTime }}</p>

                    <p v-if="item.supplierPlanTime">厂家当序预计完成时间：{{ item.supplierPlanTime }}</p>
                  </div>
                </div>
                <div class="top-right">
                  <div v-if="item.finishStatus == 2">
                    <el-popconfirm title="是否确认完成？" @confirm="updateNodeStatus(item)">
                      <el-button slot="reference" type="text" size="small">点击完成</el-button>
                    </el-popconfirm>
                  </div>
                  <div v-else>{{ getStatus(item.finishStatus) }}</div>
                  <el-button v-if="item.finishStatus == 2" type="primary" size="small" icon="el-icon-plus" plain
                    @click="handleAdd(item)">新增</el-button>
                </div>
              </div>
              <div class="">
                <div class="timeline-item-card" v-for="(item2, index2) in item.recordList" :key="'a' + index2">
                  <div class="card-left">
                    <span>{{ item2.record }}</span>
                    <span class="card-address">{{ item2.createTime }}</span>
                    <div class="img-box">
                      <img v-for="(img, imgIndex) in item2.imageList" :key="imgIndex"
                        :src="process.env.VUE_APP_IMAGE_API + img.newName" />
                    </div>

                    <span v-if="item2.address" class="card-address">
                      <i class="el-icon-location-outline"></i>
                      {{ item2.address }}</span>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>

    <!-- 添加或修改岗位对话框 -->
    <el-dialog title="新增" class="loading-box" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" label-width="60px">
        <el-row>
          <el-col :span="12">
            <div class="time-box">
              <span>规定完成时间:{{ form.planTime }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item class="record" label="备注" prop="remarks">
              <el-input v-model="form.record" type="textarea" :rows="2" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="图片" prop="postName">
              <el-upload :action="uploadUrl" :headers="headers" :on-remove="handleRemove"
                :on-success="handleFileSuccess" :on-preview="handlePictureCardPreview" :file-list="form.imageList"
                list-type="picture-card" accept=".jpeg,.png,.jpg,.bmp,.gif" :limit="9" multiple>
                <i slot="default" class="el-icon-plus icon_pic"></i>
              </el-upload>
              <el-dialog :visible.sync="dialogVisible" append-to-body>
                <img width="100%" :src="dialogImageUrl" alt="" />
              </el-dialog>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
        :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="fileSuccess"
        :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <Detail :detailVisible.sync="detailVisible" :castingId="castingId" @maintenance="goMaintenance" page="contract" />

  </div>
</template>

<script>
// import { exportData } from "@/api/productionPlanning/Modulemanagement";
import {
  getCastingPurchasePage,
  getCastingDetails,
  addNodeRecord,
  updateNodeStatus,
  exportData,
} from "@/api/castingForging/contract";
import { getToken } from "@/utils/auth";
import Detail from "@/views/casting/list/detail.vue";

export default {
  components: {
    Detail
  },
  name: "Post",
  data() {
    return {
      detailVisible: false,
      castingId: '',
      uploadUrl: process.env.VUE_APP_BASE_API + "/user-server/file/upload",
      headers: { "X-Token": "Bearer " + getToken() },
      dialogImageUrl: "",
      dialogVisible: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 项目名称显示弹出层
      itemOpen: false,
      itemLoading: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        page: 1,
        size: 10,
        query: undefined, //关键字查询
        alarmStatus: undefined, //报警状态
      },
      status: [
        { label: "报警", value: 1 },
        { label: "预警", value: 2 },
        { label: "正常", value: 3 },
        { label: "已完成", value: 4 },
      ],
      statusList: [
        {
          text: "报警",
          value: 1,
        },
        {
          text: "预警",
          value: 2,
        },
        {
          text: "正常",
          value: 3,
        },
        {
          text: "已完成",
          value: 4,
        },
      ],
      //详情
      posidList: {},
      // 表单参数
      form: {},
      studes: [
        { label: "正常", value: 0 },
        { label: "报警", value: 1 },
        { label: "预警", value: 2 },
        { label: "完成", value: 3 },
      ],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { "X-Token": "Bearer " + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/back-server/castingPurchase/web/import/casting",
      },
    };
  },
  created() {
    this.$route.query.row && (this.queryParams.ebelp = this.$route.query.row.ebelp) && (this.queryParams.ebeln = this.$route.query.row.ebeln)

    this.getList();
  },
  methods: {
    goMaintenance(row) {
      this.detailVisible = false;
      this.queryParams.ebelp = row.ebelp
      this.queryParams.ebeln = row.ebeln
      this.handleQuery()
    },
    listFind(list = [], value) {
      let index = list.findIndex((item) => {
        return item.value == value;
      });
      if (index > -1) {
        return list[index];
      }
    },
    // 导入
    handleImport() {
      this.upload.title = "导入";
      this.upload.open = true;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    fileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.getList();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 导出
    handleImp() {
      const _this = this;
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          query: this.queryParams.query,
          alarmStatus: this.queryParams.alarmStatus,
          ids: this.ids.join(","),
        };
        exportData(params).then((res) => {
          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement("a");
          link.target = "_blank";
          link.href = url;
          link.setAttribute("download", "铸锻件模块数据.xlsx");
          document.body.appendChild(link);
          link.click();
        });
      });
    },

    //创建时间处理
    timeChange() {
      this.queryParam.startTime = this.dateRange[0];
      this.queryParam.endTime = this.dateRange[1];
    },

    // 修改状态
    updateNodeStatus(item) {
      this.itemLoading = true;
      updateNodeStatus({ nodeId: item.id })
        .then((res) => {
          this.itemLoading = false;
          this.$modal.msgSuccess("修改成功");
          this.getDetail(this.posidList.castingId);
        })
        .catch(() => {
          this.itemLoading = false;
        });
    },
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getCastingPurchasePage(this.queryParams).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.castingId);
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.open = true;
      this.form = {
        imageList: [],
        record: "",
        castingId: row.castingId,
        planTime: row.planTime,
        nodeId: row.id,
      };
      this.title = "新增";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          if (this.form.castingId != undefined) {
            let list = this.form.imageList.map((item) => {
              return {
                ...item.response.data,
              };
            });
            addNodeRecord({ ...this.form, imageList: list }).then(
              (response) => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getDetail(this.form.castingId);
              }
            );
          }
        }
      });
    },
    //删除图片
    handleRemove(file, fileList) {
      this.form.imageList = fileList;
    },
    // 获取状态
    getStatus(val) {
      switch (val) {
        case 0:
          return "未完成";
        case 1:
          return "已完成";
        case 2:
          return "进行中";
        default:
          break;
      }
    },

    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    //图片上传
    handleFileSuccess(response, file, fileList) {
      this.form.imageList = fileList;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/back-server/castingPurchase/web/export/casting",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    // 项目名称弹窗
    showItem(row) {
      // this.getDetail(row.castingId);
      // this.itemOpen = true;
      this.detailVisible = true
      this.castingId = row.castingId
    },
    getDetail(id) {
      getCastingDetails({ castingId: id }).then((res) => {
        this.posidList = res.data;
      });
    },
    // 查看部套跳转 需要带参数 跳转到部套管理  根据参数查询
    // toItems(){
    //  this.$router.push("/items/Department");

    // },
    // 部套数跳转  需要带参数 跳转到部套管理  根据参数查询
    toItems(posid, moduleCode) {
      localStorage.setItem("posid", posid);
      localStorage.setItem("moduleCode", moduleCode);
      this.$router.push({
        path: "/items/items/Department/index",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.time-box {
  margin-left: 20px;
  margin-bottom: 16px;
}

::v-deep .el-dialog__body {
  line-height: 36px;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}

::v-deep .el-row--flex {
  margin-left: 22px;
}

.dialog-content {
  height: 60vh;
  display: flex;
  justify-content: space-between;

  .content {
    width: 30%;
  }

  .block {
    flex: 1;
    padding: 8px;
    overflow-y: scroll;
  }
}

.timeline-item-top {
  display: flex;
  justify-content: space-between;
  background-color: #e3f0ff;
  padding: 4px 12px;
  border-radius: 4px;

  .top-right {
    font-size: 12px;
    color: #666;
    text-align: right;
  }

  .top-title {
    font-size: 16px;
    color: #1890ff;
    font-weight: 700;
  }

  .top-content {
    font-size: 12px;
    color: #666;

    >p {
      padding: 0;
      margin: 0;
      line-height: 18px;
    }

    // :nth-child(2) {
    //   margin-left: 24px;
    // }
  }
}

.timeline-item-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  background-color: #f6f7f9;
  padding: 4px 12px;
  border-radius: 4px;

  .card-left {
    display: flex;
    flex-direction: column;

    .card-address {
      font-size: 12px;
      color: #85929b;
    }

    .img-box {
      width: 100%;
      display: flex;

      img {
        width: 100px;
        height: 100px;
        margin-right: 12px;
      }
    }
  }
}

.loading-box {

  ::v-deep .el-upload-list__item,
  ::v-deep .el-upload--picture-card {
    width: 80px;
    height: 80px;
    position: relative;
  }

  ::v-deep .icon_pic {
    position: absolute;
    top: 25px;
    left: 25px;
  }
}
</style>
