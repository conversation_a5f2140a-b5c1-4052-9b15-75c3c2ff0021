<template>
    <div class="my_process">

      <div class="search_form">
        <el-row type="flex" :gutter="6">
          <el-col :span="4">
            <el-input
              v-model="searchForm.processKey"
              class="format_option"
              size="small"
              placeholder="请输入流程标识"
              clearable
            ></el-input>
          </el-col>
          <el-col :span="4">
            <el-input
              v-model="searchForm.processName"
              class="format_option"
              size="small"
              placeholder="请输入流程名称"
              clearable
            ></el-input>
          </el-col>

          <el-col :span="4">
            <el-select
              v-model="searchForm.category"
              class="format_option"
              size="small"
              placeholder="请选择流程分类"
              clearable
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.id"
                :label="item.categoryName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-col>

          <el-col :span="5">
            <el-date-picker
              class="format_option"
              v-model="time"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-col>

          <el-col :span="4">
            <div class="btn_box">
              <el-button @click="search(false)" size="small" class="btn search_btn" icon="el-icon-search" type="primary" >搜索</el-button>
              <el-button
                @click="search(true)"
                size="small"
                class="btn reset_btn"
                icon="el-icon-refresh"
                >重置</el-button
              >
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- <div class="operation_btn">
        <el-button
          class="btn add_btn"
          size="small"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd()"
          >新增</el-button
        >
      </div> -->

      <div class="table_box">
          <el-table
          v-loading="loading"
          :data="tableData"
          row-key="id"
          style="width: 100%">

              <el-table-column
                  type="index"
                  label="序号"
                  width="50"
                  align="center">
              </el-table-column>


              <el-table-column
                  v-for="(item,index) in tableColumn"
                  :key="index"
                  :prop="item.prop"
                  :label="item.label"
                  align="center"
                  :width="item.width">
                  <template slot-scope="{row}">
                      <el-tag v-if="item.tabStatus" :type="row.finishTime ? 'warning' : 'success'">{{ row[item.prop] }}</el-tag>
                      <el-tag v-else-if="item.versionStatus" type="default">v{{ row[item.prop] }}</el-tag>
                      <div v-else-if="item.checkTime">
                          <i class="el-icon-time"></i>
                          {{ row[item.prop] | dateTimeFormat }}
                      </div>

                      <div v-else>{{row[item.prop]}}</div>
                  </template>
              </el-table-column>
              <el-table-column
                  fixed="right"
                  label="操作"
                  align="center"
                  width="200">
                  <template slot-scope="{row}">
                      <div class="handle_btn">
                        <el-button type="text" @click="handleInfo(row)" size="small" icon="el-icon-view">详情</el-button>

                        <el-popconfirm title="是否确定取消？" @confirm="handleCancel(row)">
                          <el-button type="text" slot="reference" size="small" icon="el-icon-circle-close">取消</el-button>
                        </el-popconfirm>

                        <el-popconfirm title="是否确定删除？" @confirm="handleDel(row)">
                          <el-button type="text" slot="reference" class="del" size="small" icon="el-icon-view">删除</el-button>
                        </el-popconfirm>
                      </div>
                  </template>
              </el-table-column>
          </el-table>

          <div class="page_box">
              <el-pagination
                  background
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentPage"
                  :current-page="page.pageNum"
                  :pager-count="5"
                  :page-sizes="[10, 20,30,40]"
                  :page-size="10"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="page.total">
              </el-pagination>
          </div>
      </div>

      <my-process-info ref="myProcessInfo"></my-process-info>
    </div>

  </template>

<script>
import myProcessInfo from './components/myProcessInfo.vue';
import {processClassListAll} from '@/api/processMan/processClass.js'
import { ownList,cancelDetail,delProcess } from '@/api/officeMan/myProcess'
export default {
components: {myProcessInfo},
data() {
    return {
    loading: false,
    typeOptions:[],
    tableData:[],
    time:[],
    searchForm: {
      processKey:'',
      processName:'',
      category:'',
    },
    page:{
        pageSize:10,
        pageNum:1,
        total:0
    },


    tableColumn:[
        {
          prop:'procInsId',
          label:'流程编号',
          width:'300'
        },
        {
          prop:'procDefName',
          label:'流程名称',
          width:'200'
        },
        {
          prop:'categoryName',
          label:'流程类别',
          width:'150'
        },
        {
          prop:'procDefVersion',
          label:'流程版本',
          versionStatus:true,
          width:'100'
        },

        {
          prop:'status',
          label:'状态',
          tabStatus:true,
          width:''
        },
        {
          prop:'duration',
          label:'耗时',
          width:'200'
        },
        {
          prop:'taskName',
          label:'当前节点',
          width:'100'
        },
        {
          prop:'assigneeName',
          label:'办理',
          width:'100'
        },
        {
          prop:'createTime',
          label:'提交时间',
          checkTime:true,
          width:'200'
        },
    ]
    };
},

beforeCreate(){
  processClassListAll().then((res)=>{
      let resData = res.data
      this.typeOptions = resData.map((item)=>{
          return {categoryName:`${item.categoryName}`,id:item.id}
      })
  }).catch(()=>{
  })
},

created() {
    this.loadData();
},
methods: {
    hidDialog() {
      this.loadData();
    },

    // 搜索/重置
    search(reset) {
    if (reset) {
        this.searchForm = {
          processKey:'',
          processName:'',
          category:''
        };
    }
    this.page.pageNum = 1;
      this.loadData();
    },

    // 详情
    handleInfo(row){
      this.$refs.myProcessInfo.init(row)
    },

    // 取消
    handleCancel(row){
      this.loading = true
      let params = {
        procInsId:row.procInsId
      }
      cancelDetail(params).then((res)=>{
        this.$message({
          type:'success',
          message:'操作成功',
          duration:1500
        })
        this.loading = false
      }).catch(()=>{
        this.loading = false
      })
    },

    // 删除
    handleDel(row){
      this.loading = true
      delProcess(row.procInsId).then((res)=>{
        this.$message({
          type:'success',
          message:'操作成功',
          duration:1500
        })
        this.loadData()
      }).catch(()=>{
        this.loading = false
      })
    },

    loadData(){
      this.loading = true
      this.searchForm.beginTime = this.time ? this.time[0] : ''
      this.searchForm.endTime = this.time ? this.time[1] : ''
      let params = {
        ...this.searchForm,
        ...this.page
      }
      ownList(params).then((res)=>{
        let resData = res.data
        resData.records.forEach((item)=>{
          item.status  = item.finishTime ? '已完成' : '进行中'
        })
        this.tableData = resData.records
        this.page.total = resData.total
        this.loading = false
      }).catch(()=>{
        this.loading = false
      })
    },

    // 更改每页显示条数
    handleSizeChange(pageSize){
        this.page.pageSize = pageSize
        this.loadData()
    },

    // 选择页数/点击上一页/下一页
    handleCurrentPage(currentPage){
        this.page.pageNum = currentPage
        this.loadData()
    }

}
};
</script>
<style lang="less">
.my_process {
    padding: 16px 12px 0;
}
</style>
