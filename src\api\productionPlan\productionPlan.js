import request from '@/utils/request'
// 列表查询
export function partWebPage(param) {
    return request({
        url: '/mes-server/mes/part/web/page',
        method: 'get',
        params: param,
    })
}

// 导入
export function detailUploadFile(param) {
    return request({
        url: '/mes-server/part/detail/upload/file',
        method: 'post',
        data: param,
        responseType: 'blob'
    })
}
// 详情
export function detailNodeInfo(detailId) {
    return request({
        url: `/mes-server/part/detail/node/${detailId}`,
        method: 'get',
    })
}
// 详情-修改报警状态
export function modifyStatus(query) {
    return request({
        url: `/mes-server/mes/node/${query.id}/${query.status}`,
        method: 'put'
    })
}
// 生产调度修改节点
export function editMesNode(query) {
    return request({
        url: `/mes-server/mes/node`,
        method: 'put',
        data:query
    })
}

// 详情-关联厂商
export function partDetailSupplier(query) {
    return request({
        url: `/mes-server/part/detail/supplier`,
        method: 'put',
        data: query
    })
}