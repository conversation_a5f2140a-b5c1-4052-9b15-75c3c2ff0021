import request from '@/utils/request'

// 新增用户
export function addUser(query) {
  return request({
    url: `/system/user`,
    method: "post",
    data: query,
  });
}

// 修改用户
export function editUser(query) {
  return request({
    url: `/system/user`,
    method: "put",
    data: query,
  });
}

// 分页获取用户列表
export function userList(query) {
  return request({
    url: `/system/user/page`,
    method: "get",
    params: query,
  });
}

// 获取全部用户列表
export function allUserList(query) {
  return request({
    url: `/user-server/appUser/getListPage`,
    method: "get",
    params: query,
  });
}

// 批量删除用户
export function delUser(query) {
  return request({
    url: `/system/user/batch/remove`,
    method: "put",
    data: query,
  });
}

// 更改用户状态
export function editUserStatus(query) {
  return request({
    url: `/system/user/${query.userId}/status/${query.status}`,
    method: "put",
  });
}

// 修改密码
export function editPwd(query) {
  return request({
    url: `/system/user/reset/self/pwd`,
    method: "put",
    data: query,
  });
}

// 获取当前登陆用户信息
export function getUserInfo(id) {
  return request({
    url: `/system/user/info/${id}`,
    method: "get",
  });
}

// 重置密码
export function resetPwd(query) {
  return request({
    url: `/system/user/reset/pwd`,
    method: "put",
    data: query,
  });
}

// 同步IM系统中的用户
export function syncUserInfo() {
  return request({
    url: `/system/user/im/sync`,
    method: "get",
  });
}

// 修改用户同步状态
export function editUserSyncStatus(query) {
  return request({
    url: `/system/user/${query.userId}/sync/${query.status}/status`,
    method: "put",
  });
}

// 获取脱敏列表
export function getFieldList() {
  return request({
    url: `/system/user/desensitize`,
    method: "get",
  });
}

// 修改脱敏规则
export function editField(query) {
  return request({
    url: `/system/user/desensitize`,
    method: "put",
    data: query,
  });
}

// 根据部门获取用户
export function listUser(query) {
  return request({
    url: `/system/user/dept`,
    method: "get",
    params: query,
  });
}

// 修改用户头像
export function editAvatar(query) {
  return request({
    url: `/system/user/avatar`,
    method: "put",
    data: query,
  });
}

// 图档管理>任务分配>设置责任人
export function getPeople() {
  return request({
    url: `/system/user/feign/liability`,
    method: "get",
  });
}

// 图档管理>开图管理>发放单位管理>查询机构里的人员
export function userDm(deptId) {
  return request({
    url: `/system/user/dm/${deptId}`,
    method: "get",
  });
}
