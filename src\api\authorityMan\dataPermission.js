// import request from '@/http/http'
import request from '@/utils/request'
/**
 * 分页查询
 * @param params { {current: number, size: number, wrapper: {}} }分页对象
 * @returns {*}
 */
export function pageRoleDataPermissions(params) {
  return request({
    url: `/system/role/data-permission/page`,
    method: 'post',
    data: params
  })
}
/**
 * 用户数据权限分页查询
 * @param params { {current: number, size: number, wrapper: {}} }分页对象
 * @returns {*}
 */
export function pageUserDataPermissions(params) {
  return request({
    url: `/system/user/data-permission/page`,
    method: 'post',
    data: params
  })
}

/**
 * 根据ids取消数据权限授权
 * @param ids {
 * [string]
 * }
 * @returns {*}
 */
export function batchRemoveRoleDataPermissions(ids) {
  return request({
    url: `/system/role/data-permission/remove-batch`,
    method: 'delete',
    data: ids
  })
}

/**
 * 批量保存
 * @param params {[{id: string, roleId: string, platform: string, platformName: string, tableName: string, columnName: string, sqlOperator: string, low: string, high: string}]}
 * @param operateType {string} 操作类型，允许值: update、insert
 * @returns {*}
 */
export function batchModifyRoleDataPermissions(params, operateType) {
  return request({
    url: `/system/role/data-permission/modify-batch/${operateType}`,
    method: 'put',
    data: params
  })
}

/**
 *  修改 / 创建一条数据权限
 * @param params {{id: string, roleId: string, platform: string, platformName: string, tableName: string, columnName: string, sqlOperator: string, low: string, high: string}}
 * @param isUpdate {boolean} 操作类型，true 修改；false 创建
 * @returns {*}
 */
export function saveOrUpdateRoleDataPermissions(params, isUpdate) {
  const paramsArr = Array.from([params])

  if (isUpdate) {
    return batchModifyRoleDataPermissions(paramsArr, 'update')
  }
  return batchModifyRoleDataPermissions(paramsArr, 'insert')
}

/**
 * 根据Id查询数据权限
 * @param id {string} 主键id
 * @returns {*}
 */
export function getRoleDataPermissionById(id) {
  return request({
    url: `/system/role/data-permission/${id}`,
    method: 'get'
  })
}

/**
 * 系统标识列表
 *
 * @returns {*}
 */
export function listPlatforms() {
  return request({
    url: `/system/role/data-permission/platform/list`,
    method: 'get'
  })
}

/**
 * Sql操作符列表
 * @returns {*}
 */
export function listSqlOperators() {
  return request({
    url: `/system/role/data-permission/sql-operator/list`,
    method: 'get'
  })
}

/**
 * Sql操作符列表
 * @param params { {platform: string, tableName: string, columnName: string} } platForm 为空时，无法获取任何信息
 * @returns {*}
 */
export function listTableInfos(params) {
  return request({
    url: `/system/role/data-permission/table-info/list`,
    method: 'post',
    data: params
  })
}

/**
 * Sql操作符列表
 * @param params { {platform: string, tableName: string, columnName: string} } platForm 为空时，无法获取任何信息
 * @returns {*}
 */
export function getTableInfo(params) {
  return request({
    url: `/system/role/data-permission/table-info/get`,
    method: 'post',
    data: params
  })
}

/**
 * 系统标识字典 { value: item.platForm, label: item.platFormName }
 * @returns {Promise<*[]>}
 */
export async function getDictPlatform() {
  let dict = []
  await listPlatforms()
    .then(res => {
      dict = res.data.map(item => {
        return { value: item.platform, label: item.platformName }
      })
    })
  return dict
}

/**
 * Sql操作符字典 { value: item.value, label: item.label, comment: item.comment }
 * @returns {Promise<*[]>}
 */
export async function getDictSqlOperator() {
  let dict = []
  await listSqlOperators()
    .then(res => {
      dict = res.data.map(item => {
        return { value: item.value, label: item.label, comment: item.comment }
      })
    })
  return dict
}
