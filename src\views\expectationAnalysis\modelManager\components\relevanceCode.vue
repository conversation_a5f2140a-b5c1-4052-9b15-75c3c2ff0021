<template>
  <el-dialog
      title="查看"
      :visible.sync="dialogVisible"
      @close="cancel"
      width="70%"
      >
        <div class="container" v-loading="loading">

            <el-form ref="form" :model="form" :inline="true" label-width="50px" :rules="rules">
                <el-form-item label="令号" prop="posid">
                    <el-input v-model="form.posid" size="small" placeholder="请输入"></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="small"
                    @click="handleRelevance"
                    >关联</el-button
                    >
                </el-form-item>
            </el-form>

            <p class="title">已关联令号</p>

            <el-form ref="searchform" :model="searchForm" :inline="true" label-width="50px">
                <el-form-item label="搜索">
                    <el-input v-model="searchForm.posid" size="small" placeholder="请输入"></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="small"
                    @click="handleQuery(false)"
                    >搜索</el-button
                    >
                    <el-button icon="el-icon-refresh" 
                    size="small" @click="handleQuery(true)"
                    >重置</el-button
                    >
                </el-form-item>
            </el-form>

            <div class="table-box">
                <el-table
                :data="tableData"
                v-loading="tbaleLoading"
                style="width: 100%">
                    <el-table-column
                        prop="posid"
                        label="令号"
                        align="center"
                        width="">
                    </el-table-column>
                    <el-table-column
                        prop="post1"
                        label="机组名称"
                        align="center"
                        width="">
                    </el-table-column>

                    <el-table-column
                    label="操作"
                    align="center"
                    class-name="small-padding fixed-width"
                    >
                        <template slot-scope="{$index}">
                            <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-delete"
                            @click="handleDel($index)"
                            >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { modelCode,tqListName } from '@/api/expectationAnalysis/modelManager'
export default {
  components:{},

  data(){
    return{
        btnLoading:false,
        loading:false,
        tbaleLoading:false,
        dialogVisible:false,
        rowData:null,
        tableData:[],
        codeList:[],
        form:{
            posid:''
        },
        searchForm:{
            posid:''
        },
        rules:{
            posid:[
                { required: true, message: '请输入令号', trigger: 'blur' }
            ]
        }
    }
  },

  methods:{
    init(row){
        this.rowData = row
        this.btnLoading = false
        this.tbaleLoading = false
        this.dialogVisible = true
        this.loadData()
    },
    
    // 点击关联
    handleRelevance(){
        this.$refs['form'].validate((valid) => {
            if(valid){
                let params = [this.form.posid]
                this.tbaleLoading = true
                tqListName(params).then((res)=>{
                    let resData = res.data || []
                    this.tableData.push(...resData)
                    this.codeList.push(...resData)
                    this.tbaleLoading = false
                    this.form.posid = ''
                }).catch(()=>{
                    this.tbaleLoading = false
                })
                
            }
        })
    },

    handleQuery(flag){
        if(flag){
            this.searchForm.posid =''
        }
        this.tableData = this.codeList.filter((item)=>{
            return item.posid.includes(this.searchForm.posid)
        })
    },

    loadData(){
        this.loading = true
        let codeList = this.rowData.codeList
        tqListName(codeList).then((res)=>{
            let resData = res.data || []
            this.tableData = [...resData]
            this.codeList = [...resData]
            this.loading = false
        }).catch(()=>{
            this.loading = false
        })
    },

    // 删除
    handleDel(index){
        this.tableData.splice(index,1)
        this.codeList.splice(index,1)
    },

    confirm(){
        if(this.tableData.length===0){
            this.$message({
                type:'warning',
                message:'令号列表不能为空',
                duration:2000
            })
            return
        }
        let codeList = this.tableData.map((item)=>{
            return item.posid
        })
        let params = {
            codeList:codeList,
        }
        this.btnLoading = true
        modelCode(params,this.rowData.id).then((res)=>{
            this.$message({
                type:'success',
                message:'操作成功',
                duration:1500
            })
            this.btnLoading = false
            this.dialogVisible = false
            this.$emit('hideDialog')
        }).catch(()=>{
            this.btnLoading = false
        })
    },

    cancel(){
        this.$refs['form'].resetFields()
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped>
.title {
  color:#409EFF;
  font-size:15px;
  margin-bottom:10px;
  text-align:center;
}
</style>