<template>
    <div>
        <el-dialog
        title="生产调度计划详情"
        :visible.sync="dialogVisible"
        append-to-body
        width="70%"
        @close="cancel"
        >
            <div class="dialog-content" v-loading="loading">
                <div class="left-content">
                    <p>零件名称：{{orderInfo.bismtName}}</p>
                    <p>图号：{{orderInfo.bismt}}</p>
                    <p>部套名称：{{orderInfo.dwgName}}</p>
                    <p>部套编码：{{orderInfo.dwgNo}}</p>
                    <p>机组名称：{{orderInfo.post1}}</p>
                    <p>令号：{{orderInfo.posid}}</p>
                    <p>当前节点：{{orderInfo.nodeName}}</p>
                    <p>步骤节点：{{orderInfo.nodeFinishNum}}/{{orderInfo.nodeNum}}</p>
                    <p>节点记录数：{{orderInfo.nodeRecordNum}}</p>
                    <p>计划需求时间：{{orderInfo.planTime | dateFormat}}</p>
                    <p>实际完成时间：{{orderInfo.finishTime | dateFormat}}</p>
                    <p v-if="orderInfo.updateRecordList && orderInfo.updateRecordList.length>0">节点历史记录</p>
                    <div v-if="orderInfo.updateRecordList && orderInfo.updateRecordList.length>0" class="updateRecordList">
                        <p v-for="(aItem,aIndex) in orderInfo.updateRecordList" :key="aIndex">
                            <span v-if="aItem.type == 1">{{aItem.userName}}在{{aItem.crtTime.split('T')[0]}}新增节点{{aItem.newValue}}</span>
                            <span v-if="aItem.type == 2">{{aItem.userName}}在{{aItem.crtTime.split('T')[0]}}将节点名称从{{aItem.oldValue}}改为{{aItem.newValue}}</span>
                            <span v-if="aItem.type == 3">{{aItem.userName}}在{{aItem.crtTime.split('T')[0]}}将节点时间从{{aItem.oldValue}}改为{{aItem.newValue}}</span>
                            <span v-if="aItem.type == 4">{{aItem.userName}}在{{aItem.crtTime.split('T')[0]}}将节点顺序从{{aItem.oldValue}}改为{{aItem.newValue}}</span>
                            <span v-if="aItem.type == 5">{{aItem.userName}}在{{aItem.crtTime.split('T')[0]}}删除节点{{aItem.newValue}}</span>
                        </p>
                    </div>
                </div>

                <div class="right-content">
                    <el-steps direction="vertical">
                        <el-step v-for="(item,index) in orderInfo.partNodeList" :key="index">
                            <template #icon>
                                <i v-if="item.nodeStatus === 1" class="icon el-icon-success"></i>
                                <i v-else class="default-icon"></i>
                            </template>
                            <template #title>
                                <div class="title-container">
                                    <div class="title-head">
                                        <p class="title">{{item.nodeName}}</p>
                                        <div class="btn-box" v-if="item.nodeStatus === 2">
                                            <el-button v-if="item.alarmStatus === 2" type="text" @click="changeStatus(item)">设置报警</el-button>
                                            <el-button v-if="item.alarmStatus === 1" type="text" @click="changeStatus(item)">取消报警</el-button>
                                        </div>
                                        <div class="status-box">
                                            <p class="status warning" v-if="item.nodeStatus === 0">未开始</p>
                                            <div v-else-if="item.nodeStatus === 2">
                                                <p class="status danger" v-if="item.alarmStatus === 1">报警</p>
                                                <p class="status default" v-else>进行中</p>
                                                <p class="btn" @click="handleFinish(item)">点击完成</p>
                                            </div>
                                            <p class="status success" v-else>已完成</p>
                                        </div>
                                    </div>

                                    <div class="title-content">
                                        <p class="time">规定完成时间：{{item.planTime | dateFormat}}</p>
                                        <p class="time">实际完成时间：{{item.finishTime | dateFormat}}</p>
                                        <!-- && index !== orderInfo.partNodeList.length-1 -->
                                        <p class="time btn-box" v-if="item.nodeStatus === 2 ">
                                            <span class="btn" @click="handleAdd(item)">
                                                <i class="el-icon-plus"></i>新增
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </template>
                            <template #description>
                                <div class="description-container" v-for="(aItem,aIndex) in item.nodeRecordList" :key="aIndex">
                                    <p class="report">{{aItem.record}}</p>
                                    <p class="company-info">
                                        <span class="name">{{aItem.supplierName}}</span>
                                        <span class=time>{{aItem.createTime | dateFormat}}</span>
                                    </p>
                                    <div>
                                        <el-image
                                        v-for="(aImg,aImgIndex) in aItem.imageList" :key="aImgIndex"
                                        style="width: 100px; height: 100px"
                                        :src="baseImg+aImg.newName"
                                        :preview-src-list="aItem.imageList | filterImg(baseImg)">
                                        </el-image>
                                    </div>
                                    <p class="addr-content" v-if="aItem.address">
                                        <i class="el-icon-location-outline"></i><span class="addr">{{aItem.address}}</span></p>
                                </div>
                            </template>
                        </el-step>
                    </el-steps>
                </div>
            </div>
        </el-dialog>

        <materials-add ref="materialsAdd" @loadData="loadData"></materials-add>
    </div>
</template>

<script>
import materialsAdd from './materialsAdd'
import { detailNodeInfo,detailNodeNodeId,modifyStatus } from '@/api/suppilerProductionPlan/productionPlan'
export default {
  components:{materialsAdd},

  data(){
    return{
        dialogVisible:false,
        loading:false,
        active:1,
        rowData:null,
        orderInfo:{},
        stepList:[],
        baseImg:process.env.VUE_APP_IMAGE_API
    }
  },

    filters:{
        filterImg(imgArr,baseImg){
            return imgArr.map((item)=>{
                return baseImg+item.newName
            })
        }
    },

  methods:{
    init(row){
        this.dialogVisible = true
        this.rowData = row
        this.loadData()
    },

    handleAdd(item){
        this.$refs.materialsAdd.init(item)
    },

    // 修改报警状态
    changeStatus(row){
        let status = row.alarmStatus === 2 ? 1 : 2
        let params = {
            id:row.id,
            status:status
        }
        this.loading = true
        modifyStatus(params).then((res)=>{
            this.$message({
                type:'success',
                message:'操作成功',
                duration:1500
            })
            this.loadData()
        }).catch(()=>{
            this.loading = false
        })
    },

    // 点击完成
    handleFinish(row){
        this.$confirm('是否确定?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
            this.loading = true
            detailNodeNodeId(row.id).then((res)=>{
                this.$message({
                    type:'success',
                    message:'操作成功',
                    duration:1500
                })
                this.loadData()
            }).catch(()=>{
                this.loading = false
            })
        }).catch(() => {});
    },

    loadData(){
        this.loading = true
        detailNodeInfo(this.rowData.partId).then((res)=>{
            this.orderInfo = res.data || {}
            this.loading = false
        }).catch(()=>{
            this.loading = false
        })
    },

    cancel(){
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped lang='scss'>

.warning {
    color:#E6A23C !important;
}
.success {
    color:#67C23A !important;
}
.default {
    color:#409EFF !important;
}

.danger {
    color:#F56C6C !important
}

.dialog-content {
    display:flex;
    height:auto;
}
.left-content {
    width:300px;
    font-size:14px;
    height:100%;
    >p {
        margin-bottom:10px;
    }
}

::v-deep.el-steps {
    .el-step__icon.is-text {
        border:none;
    }
    .default-icon {
        width:9px;
        height:9px;
        display:inline-block;
        background:#1890ff;
        border-radius:9px;
    }
    .el-step__description {
        padding-right:0;
        margin-left:15px;
    }
}

.right-content {
    width: calc(100% - 300px);
    height: fit-content;
    .el-icon-success {
        color:#1890ff;
    }
    .title-container {
        background:#E3F0FF;
        padding:10px;
        box-sizing: border-box;
        border-radius:5px;
        .title-head {
            font-size:15px;
            font-weight:bold;
            display:flex;
            justify-content: space-between;
            .title {
                margin-bottom:0;
                width: calc(100% - 100px);
                display:flex;
                justify-content: flex-start;
                color:#1890ff;
                margin-left: 10px;
            }
            .btn-box {
                display:flex;
                align-items:flex-end;
                box-sizing:border-box;
                padding:0 8px 6px;
                .el-button {
                    font-size:12px;
                    padding:0;
                    box-sizing: border-box;
                }
            }
            .status-box {
                width:66px;
                // display:flex;
                // justify-content: flex-end;
                text-align:center;
                .btn {
                    font-size:12px;
                    font-weight:500;
                    color:#1890ff;
                    cursor:pointer;
                }
                .status {
                    font-size:12px;
                    font-weight:500;
                    color:#999;
                }
            }
        }
        .title-content {
            display:flex;
            margin-top:5px;
            .time {
                flex:1;
                font-size:12px;
                color:#999;
                font-weight:normal;
            }
            .btn-box {
                text-align:right;
                .btn {
                    padding:5px 10px;
                    border:solid 1px #1890ff;
                    color:#1890ff;
                    cursor:pointer;
                    .el-icon-plus {
                        font-weight:bold;
                        margin-right:5px;
                    }
                }
            }
        }
    }

    .description-container {
        background:#F6F7F9;
        border-radius:5px;
        margin-top:10px;
        margin-bottom:10px;
        padding:10px;
        box-sizing: border-box;
        .report {
            font-size:15px;
            color:#000;
        }
        .company-info {
            color:#999;
            margin:5px 0;
            .name {
                margin-right:15px;
            }
        }
        .addr-content {
            font-size:12px;
            >i {
                color:#1890ff;
            }
            .addr {
                color:#999;
                margin-left:3px;
            }
        }
    }
}
.updateRecordList{
    height: 200px;
    overflow-y: auto;
}
.updateRecordList::-webkit-scrollbar{
    width: 7px; /* 定义滚动条的宽度 */
}
.updateRecordList::-webkit-scrollbar-track {
    background: #fff; /* 滚动条轨道的颜色 */
}
.updateRecordList::-webkit-scrollbar-thumb {
    background: #ddd; /* 滚动条的颜色 */
}
</style>
