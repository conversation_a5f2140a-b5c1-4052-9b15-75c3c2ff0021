<template>
     <el-dialog title="历史模型" :visible.sync="dialogVisible " width="70%" append-to-body>
        <div class="table_box">
            <el-table
            v-loading="loading"
            :data="tableData"
            row-key="id"
            style="width: 100%">

                <el-table-column
                    type="index"
                    label="序号"
                    width="50"
                    align="center">
                </el-table-column>


                <el-table-column
                    v-for="(item,index) in tableColumn"
                    :key="index"
                    :prop="item.prop"
                    :label="item.label"
                    align="center"
                    :width="item.width">
                    <template slot-scope="{row}">
                        <el-link v-if="item.linkstatus" type="primary" @click="handleProcessView(row)">{{row[item.prop]}}</el-link>
                        <el-tag
                            v-else-if="item.versionStatus"
                            type="default"
                            >v{{ row[item.prop] }}</el-tag
                        >
                        <div v-else-if="item.checkTime">
                            <i class="el-icon-time"></i>
                            {{ row[item.prop] | dateTimeFormat }}
                        </div>
                        <div v-else>{{row[item.prop]}}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    align="center"
                    width="150">
                    <template slot-scope="{row}">
                        <div class="handle_btn">
                            <el-popconfirm
                            title="确定部署？"
                            @confirm="handleDeploy(row)"
                            >
                                <el-button type="text" slot="reference" size="small" icon="el-icon-video-play">部署</el-button>
                            </el-popconfirm>

                            <el-popconfirm
                            title="确定设为最新？"
                            @confirm="setNew(row)"
                            >
                                <el-button type="text" slot="reference" size="small" icon="el-icon-star-off">设为最新</el-button>
                            </el-popconfirm>

                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <div class="page_box">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentPage"
                    @prev-click="handlePage"
                    @next-click="handlePage"
                    :current-page="page.pageNum"
                    :pager-count="5"
                    :page-sizes="[10, 20,30,40]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total">
                </el-pagination>
            </div>
        </div>
        <flow-chart ref="flowChart"></flow-chart>
      </el-dialog>
</template>
<script>
import {historyList,deployModel,latest } from '@/api/processMan/processModel.js'
import flowChart from './flowChart.vue'
export default {
    components:{flowChart},
    data(){
        return {
            dialogVisible:false,
            loading:false,
            rowData:null,
            tableData:[],
            page:{
                pageSize:10,
                pageNum:1,
                total:0
            },
            tableColumn:[
                {
                    prop:'modelKey',
                    label:'模型标识',
                    width:'200'
                },
                {
                    prop:'modelName',
                    label:'模型名称',
                    linkstatus:true,
                    width:'200'
                },
                {
                    prop:'categoryName',
                    label:'流程分类',
                    width:'200'
                },
                {
                    prop:'version',
                    label:'模型版本',
                    versionStatus:true,
                    width:'100'
                },
                {
                    prop:'description',
                    label:'描述',
                    width:''
                },
                {
                    prop:'createTime',
                    label:'创建时间',
                    checkTime:true,
                    width:'200'
                }
            ]
        }
    },
    methods:{
        init(row){
            this.dialogVisible = true
            this.rowData = row
            this.loadData()
        },
        loadData(){
            this.loading = true
            let params = {
                ...this.page,
                modelKey:this.rowData.modelKey
            }
            historyList(params).then((res)=>{
                let resData = res.data
                this.tableData = resData.records
                this.page.total = resData.total
                this.loading = false
            }).catch(()=>{
                this.loading = false
            })
        },
        // 部署
        handleDeploy(row){
            this.loading = true
            deployModel(row.modelId).then((res)=>{
                this.$message({
                    type:'success',
                    message:'操作成功',
                    duration:1500
                })
                this.loading = false
            }).catch(()=>{
                this.loading = false
            })
        },
        //设为最新
        setNew(row){
            this.loading = true
            latest(row.modelId).then((res)=>{
                this.$message({
                    type:'success',
                    message:'操作成功',
                    duration:1500
                })
                this.loading = false
            }).catch(()=>{
                this.loading = false
            })
        },

        // 流程图
        handleProcessView(row){
            this.$refs.flowChart.init(row)
        },

        // 更改每页显示条数
        handleSizeChange(pageSize){
            this.page.pageSize = pageSize
            this.loadData()
        },

        // 选择页数
        handleCurrentPage(currentPage){
            this.page.pageNum = currentPage
            this.loadData()
        },

        // 点击上一页/下一页
        handlePage(currentPage){
            this.page.pageNum = currentPage
            this.loadData()
        }
    }
}
</script>
