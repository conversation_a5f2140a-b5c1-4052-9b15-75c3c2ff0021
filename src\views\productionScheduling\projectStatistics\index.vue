<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期">
        <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" />
      </el-form-item>
      <el-form-item label="搜索">
        <el-input v-model="queryParams.query" placeholder="请输入关键字" clearable style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态">
        <!-- 0未完成 1已完成 不传是全部 -->
        <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 200px">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :offset="1" :span="3">
        <el-card shadow="hover" class="statistics-card pink">
          <div class="card-title">项目令号</div>
          <div class="card-number">{{ summaryData.totalCount }}<span class="unit">个</span></div>
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card shadow="hover" class="statistics-card orange">
          <div class="card-title">进行中</div>
          <div class="card-number">{{ summaryData.unFinishCount }}<span class="unit">个</span></div>
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card shadow="hover" class="statistics-card green">
          <div class="card-title">已完成</div>
          <div class="card-number">{{ summaryData.finishCount }}<span class="unit">个</span></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="tableData" @sort-change="handleSortChange"
      style="width: 100%; margin-top: 20px">
      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          {{ queryParams.pageSize * (queryParams.pageNum - 1) + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="令号" prop="posid" align="center" show-overflow-tooltip />
      <el-table-column label="供应商数量" prop="supplierNum" align="center" sortable="custom" width="120">
        <template slot-scope="scope">
          <el-link type="primary" @click="handleDetail(scope.row)">{{ scope.row.supplierNum }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="铸锻件总数量" prop="totalNum" align="center" sortable="custom" width="150" />
      <el-table-column label="报警" prop="alarmNum" align="center" sortable="custom" width="120" />
      <el-table-column label="预警" prop="earlyAlarmNum" align="center" sortable="custom" width="120" />
      <el-table-column label="进行中" prop="normalNum" align="center" sortable="custom" width="120" />
      <el-table-column label="已完成" prop="finishNum" align="center" sortable="custom" width="120" />
      <el-table-column label="完成率" prop="finishRate" align="center" sortable="custom" width="120">
        <template slot-scope="scope">
          {{ scope.row.finishRate }}%
        </template>
      </el-table-column>
      <el-table-column label="按期完成率" prop="finishOnTimeRate" align="center" sortable="custom" width="150">
        <template slot-scope="scope">
          {{ scope.row.finishOnTimeRate }}%
        </template>
      </el-table-column>
      <el-table-column label="按期完成数量" prop="finishOnTimeNum" align="center" sortable="custom" width="150" />
      <el-table-column label="超期完成率" prop="finishOutTimeRate" align="center" sortable="custom" width="150">
        <template slot-scope="scope">
          {{ scope.row.finishOutTimeRate }}%
        </template>
      </el-table-column>
      <el-table-column label="超期完成数量" prop="finishOutTimeNum" align="center" sortable="custom" width="150" />
    </el-table>

    <!-- 添加分页组件 -->
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
      :current-page="queryParams.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="total" style="margin-top: 20px; text-align: right" />
  </div>
</template>

<script>
import { getSupplierCompletionList, getSupplierCompletionSummary } from '@/api/productionScheduling/projectStatistics'
export default {
  name: 'SupplierCompletion',
  data() {
    return {
      loading: false,
      statusOptions: [
        {
          value: "",
          label: '全部'
        },
        {
          value: "0",
          label: '未完成'
        },
        {
          value: "1",
          label: '已完成'
        },

      ],
      queryParams: {
        dateRange: [],
        query: '',
        pageNum: 1,
        pageSize: 10,
        sortType: '',
        sort: '',
        status:''
      },
      tableData: [],
      summaryData: {},
      total: 0
    }
  },
  created() {
    this.handleQuery()
  },
  methods: {
    handleDetail(row) {
      this.$router.push({
        path: '/productionScheduling/projectStatistics/detail',
        query: {
          row
        }
      })
    },
    handleQuery() {
      this.loading = true
      Promise.all([
        this.getSupplierCompletionList(),
        this.getSupplierCompletionSummary()
      ]).finally(() => {
        this.loading = false
      })
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.queryParams = {
        dateRange: [],
        query: '',
        pageNum: 1,
        pageSize: 10,
        sortType: '',
        sort: undefined
      }
      this.handleQuery()
    },
    getSupplierCompletionList() {
      const params = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        query: this.queryParams.query,
        startTime: this.queryParams.dateRange && this.queryParams.dateRange[0],
        endTime: this.queryParams.dateRange && this.queryParams.dateRange[1],
        sortType: this.queryParams.sortType,
        sort: this.queryParams.sort,
        status: this.queryParams.status
      }
      return getSupplierCompletionList(params).then(res => {
        this.tableData = res.data.records
        this.total = res.data.total
      })
    },
    getSupplierCompletionSummary() {
      const params = {
        status: this.queryParams.status,
        query: this.queryParams.query,
        startTime: this.queryParams.dateRange && this.queryParams.dateRange[0],
        endTime: this.queryParams.dateRange && this.queryParams.dateRange[1]
      }
      return getSupplierCompletionSummary(params).then(res => {
        this.summaryData = res.data
      })
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.handleQuery()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.handleQuery()
    },
    handleSortChange(column) {
      if (column.prop) {
        this.queryParams.sortType = column.prop
        this.queryParams.sort = column.order === 'ascending' ? '1' : '2'
      } else {
        this.queryParams.sortType = ''
        this.queryParams.sort = ''
      }
      this.handleQuery()
    }
  }
}
</script>

<style scoped>
.statistics-cards {
  margin: 20px auto;
  display: flex;
  justify-content: center;
}

.statistics-card {
  background-color: #fff;
  border-radius: 4px;
}

.card-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.card-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.unit {
  font-size: 14px;
  margin-left: 4px;
}

/* 卡片背景色 */
.pink {
  background-color: #fef0f0;
}

.orange {
  background-color: #fdf6ec;
}

.green {
  background-color: #f0f9eb;
}

.blue {
  background-color: #ecf5ff;
}

.cyan {
  background-color: #e1f3f1;
}

.purple {
  background-color: #f5f0fa;
}

.pink-light {
  background-color: #feeff1;
}
</style>
