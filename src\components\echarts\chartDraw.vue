<template>
  <div class="pie-draw" :id="id"></div>
</template>

<script>
export default {
  props: {
    id: {
      type: String,
      required: true,
    },
    options: {
      type: Object,
      required: true,
    },
  },
  components: {},

  data() {
    return {};
  },
  methods: {
    createChart() {
      let chartDom = document.getElementById(this.id);
      let myChart = this.$echarts.init(chartDom);
      myChart.setOption(this.options);

      myChart.on("click", (params) => {
        this.$emit("clickECharts", params);
      });
    },
  },

  created() {},

  mounted() {
    // this.createChart()
  },
};
</script>

<style scoped lang="scss">
.pie-draw {
  width: 100%;
  height: 100%;
}
</style>
