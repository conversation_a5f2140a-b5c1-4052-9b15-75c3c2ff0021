import Vue from "vue";
import axios from 'axios';
import { Message } from 'element-ui';
import { Loading } from 'element-ui';
import store from "../store/index";

// import CryptoJS from 'crypto-js'

// 格式化菜单
export function routesResult(router) {
  // title: 菜单名称 moduleName: 板块名称 routerName:路由名称
  // type: 1、菜单 2、按钮
  router.forEach((system) => {
    if (system.shortName) {
      system.systemIdent = system.shortName
      system.moduleName = `${system.name}（${system.shortName}）`
      system.title = system.name
      system.typeName = '板块'
    }
    system.children.forEach((subMenu) => {
      subMenu.moduleName = system.moduleName
      if (subMenu.children.length > 0) {
        subMenu.typeName = subMenu.type === 1 ? '菜单' : '按钮'
        subMenu.children.forEach((menu) => {
          menu.typeName = menu.type === 1 ? '菜单' : '按钮'
          menu.moduleName = system.moduleName
          let meta = {
            title: menu.title,
            keepAlive: menu.keepAlive === 0 ? false : true
          }
          menu.meta = meta
        })
      } else {
        subMenu.typeName = subMenu.type === 1 ? '菜单' : '按钮'
        let meta = {
          title: subMenu.title
        }
        subMenu.meta = meta
      }
      if (subMenu.children && subMenu.children.length > 0) {
        routesResult(subMenu.children)
      }
    })
  })
  return router
}

// 深度遍历获取第一个叶子节点
export function getChildNode(parentNode) {
  if (parentNode.children && parentNode.children.length > 0) {
    return getChildNode(parentNode.children[0])
  } else {
    return parentNode
  }
}

// 转换字典值
Vue.filter('checkDic', (originVal, dicAttr) => {
  if (originVal === null || originVal === '' || originVal === undefined) return '';
  try {
    let dicList = store.state.dictionaries.dicList;

    if (!dicList || !dicList[dicAttr]) {
      console.warn(`字典 ${dicAttr} 不存在`);
      return originVal;
    }
    let option = dicList[dicAttr].find((item) => {
      return item.value === originVal
    })
    return option ? option.label : originVal;
  } catch (e) {
    console.warn('字典异常', dicAttr, originVal, typeof originVal);
    return originVal;
  }
})

// 时间格式   yyyy-mm
Vue.filter("dateYearMonth", function (originVal) {
  if (!originVal) return '';
  const dt = new Date(originVal);
  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1 + "").padStart(2, "0");
  return `${y}-${m}`;
});

// 时间格式  yyyy-mm-dd hh:mm:ss
Vue.filter("dateTimeFormat", function (originVal) {
  if (!originVal) return '';
  const dt = new Date(originVal);

  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1 + "").padStart(2, "0");
  const d = (dt.getDate() + "").padStart(2, "0");

  const hh = (dt.getHours() + "").padStart(2, "0");
  const mm = (dt.getMinutes() + "").padStart(2, "0");
  const ss = (dt.getSeconds() + "").padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
});


// 时间格式   yyyy-mm-dd
Vue.filter("dateFormat", function (originVal) {
  if (!originVal) return '';
  const dt = new Date(originVal);
  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1 + "").padStart(2, "0");
  const d = (dt.getDate() + "").padStart(2, "0");
  return `${y}-${m}-${d}`;
});

// 方法过滤  yyyy-mm-dd hh:mm:ss
export function dateTimeFormat(originVal) {
  if (!originVal) return '';
  const dt = new Date(originVal);

  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1 + "").padStart(2, "0");
  const d = (dt.getDate() + "").padStart(2, "0");

  const hh = (dt.getHours() + "").padStart(2, "0");
  const mm = (dt.getMinutes() + "").padStart(2, "0");
  const ss = (dt.getSeconds() + "").padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
}

// 方法  hh:mm:ss
export function timeFormat(originVal) {
  if (!originVal) return '';
  const dt = new Date(originVal);

  const hh = (dt.getHours() + "").padStart(2, "0");
  const mm = (dt.getMinutes() + "").padStart(2, "0");
  const ss = (dt.getSeconds() + "").padStart(2, "0");
  return `${hh}:${mm}:${ss}`;
}

// 方法过滤  yyyy-mm-dd
export function dateYearMonth(originVal) {
  if (!originVal) return '';
  const dt = new Date(originVal);
  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1 + "").padStart(2, "0");
  const d = (dt.getDate() + "").padStart(2, "0");
  return `${y}-${m}-${d}`;
}

// 方法过滤  yyyy-mm
export function dateFormat(originVal) {
  if (!originVal) return '';
  const dt = new Date(originVal);
  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1 + "").padStart(2, "0");
  return `${y}-${m}`;
}

// 周末判断
Vue.filter("weekend", function (originVal) {
  let dt = new Date(originVal);
  let day = dt.getDay(); // 0-周日，6-周六
  if (day === 0 || day === 6) {
    return "休息日";
  }
  return "";
});

// 判断是否为JSON对象
function isJSON(str) {
  if (typeof str == 'string') {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }
}

// 获取PDF文件地址
export function exportPDFUrl(url, query) {
  let token = sessionStorage.getItem('token') ? `Bearer ${sessionStorage.getItem('token')}` : ''
  let loadingInstance = Loading.service();
  return new Promise((resolve, reject) => {
    axios({
      method: 'get',
      url,
      params: query,
      responseType: 'blob',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json'
      },
    }).then((res) => {
      let blob = new Blob([res.data], { type: 'application/pdf' })
      let url = window.webkitURL.createObjectURL(blob);
      // 创建一个新的url，此url指向新建的Blob对象
      resolve(url)
    }).catch((error) => {
      loadingInstance.close();
      reject()
      if (axios.isAxiosError(error)) {
        Message.error({
          message: `网络错误:${new Error(error)}`,
          duration: 3000
        })
      } else {
        Message.error({
          message: `系统错误:${new Error(error)}`,
          duration: 3000
        })
      }
    })
  })
}

// get请求下载文件
export function exportFile(url, fileName, query) {
  let token = sessionStorage.getItem('token') ? `Bearer ${sessionStorage.getItem('token')}` : ''
  let loadingInstance = Loading.service();
  axios({
    method: 'get',
    url,
    params: query,
    responseType: 'blob',
    headers: {
      Authorization: token,
      'Content-Type': 'application/json'
    },
  }).then((res) => {
    const reader = new FileReader()
    reader.readAsText(res.data, 'utf-8')
    reader.onload = function () {
      // 这里下载所有文件名称统一取后端传来的
      if (res.headers['content-disposition'] && (reader.result && !isJSON(reader.result))) {
        let blob = res.data
        // 创建一个新的url，此url指向新建的Blob对象
        let url = window.webkitURL.createObjectURL(blob)
        let name = fileName || window.decodeURI(res.headers['content-disposition'].split('=')[1])
        // 创建a标签，并隐藏改a标签
        let link = document.createElement('a')
        link.style.display = 'none'
        // a标签的href属性指定下载链接
        link.href = url
        //setAttribute() 方法添加指定的属性，并为其赋指定的值。
        link.setAttribute('download', name)
        document.body.appendChild(link)
        link.click()
      } else {
        let result = '请求失败';
        if (isJSON(reader.result)) {
          const { msg, code } = JSON.parse(reader.result)
          result = msg || '请求失败'
        }
        Message.error(result)
      }
      loadingInstance.close();
    }

  }).catch((error) => {
    loadingInstance.close();
    if (axios.isAxiosError(error)) {
      Message.error({
        message: `网络错误:${new Error(error)}`,
        duration: 3000
      })
    } else {
      Message.error({
        message: `系统错误:${new Error(error)}`,
        duration: 3000
      })
    }
  })
}

// get请求批量下载文件
export function exportFiles(url, query,) {
  let token = sessionStorage.getItem('token') ? `Bearer ${sessionStorage.getItem('token')}` : ''
  return new Promise((resolve, reject) => {
    axios({
      method: 'get',
      url,
      params: query,
      responseType: 'blob',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json'
      },
    }).then((res) => {
      const reader = new FileReader()
      reader.readAsText(res.data, 'utf-8')
      reader.onload = function () {
        // 这里下载所有文件名称统一取后端传来的
        if (res.headers['content-disposition'] && (reader.result && !isJSON(reader.result))) {
          let blob = res.data
          // 创建一个新的url，此url指向新建的Blob对象
          let url = window.webkitURL.createObjectURL(blob)
          let name = window.decodeURI(res.headers['content-disposition'].split('=')[1])
          // 创建a标签，并隐藏改a标签
          let link = document.createElement('a')
          link.style.display = 'none'
          // a标签的href属性指定下载链接
          link.href = url
          //setAttribute() 方法添加指定的属性，并为其赋指定的值。
          link.setAttribute('download', name)
          document.body.appendChild(link)
          link.click()
          resolve()
        } else {
          let result = '请求失败';
          if (isJSON(reader.result)) {
            const { msg, code } = JSON.parse(reader.result)
            result = msg || '请求失败'
          }
          Message.error(result)
          reject(result)
        }
      }

    }).catch((error) => {
      if (axios.isAxiosError(error)) {
        Message.error({
          message: `网络错误:${new Error(error)}`,
          duration: 3000
        })
      } else {
        Message.error({
          message: `系统错误:${new Error(error)}`,
          duration: 3000
        })
      }
      reject()
    })
  })
}

// post请求下载文件
export function postExportFile(url, query,) {
  let token = sessionStorage.getItem('token') ? `Bearer ${sessionStorage.getItem('token')}` : ''
  return new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url,
      data: query,
      responseType: 'blob',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json'
      },
    }).then((res) => {
      const reader = new FileReader()
      reader.readAsText(res.data, 'utf-8')
      reader.onload = function () {
        // 这里下载所有文件名称统一取后端传来的
        if (res.headers['content-disposition'] && (reader.result && !isJSON(reader.result))) {
          let blob = res.data
          // 创建一个新的url，此url指向新建的Blob对象
          let url = window.webkitURL.createObjectURL(blob)
          let name = window.decodeURI(res.headers['content-disposition'].split('=')[1])
          // 创建a标签，并隐藏改a标签
          let link = document.createElement('a')
          link.style.display = 'none'
          // a标签的href属性指定下载链接
          link.href = url
          //setAttribute() 方法添加指定的属性，并为其赋指定的值。
          link.setAttribute('download', name)
          document.body.appendChild(link)
          link.click()
          resolve()
        } else {
          let result = '请求失败';
          if (isJSON(reader.result)) {
            const { msg, code } = JSON.parse(reader.result)
            result = msg || '请求失败'
          }
          Message.error(result)
          reject(result)
        }
      }

    }).catch((error) => {
      if (axios.isAxiosError(error)) {
        Message.error({
          message: `网络错误:${new Error(error)}`,
          duration: 3000
        })
      } else {
        Message.error({
          message: `系统错误:${new Error(error)}`,
          duration: 3000
        })
      }
      reject()
    })
  })
}

/**
 * CryptoJS 加密
 *
 * @param {String} encryptData  需要加密数据
 * @returns 加密后的数据
 * @memberof Utils
 */
// export const encrypt = (encryptData) => {
//   var key = CryptoJS.enc.Utf8.parse('as-Crypto-js')
//   var srcs = CryptoJS.enc.Utf8.parse(encryptData)
//   var encrypted = CryptoJS.AES.encrypt(srcs, key, {
//     mode: CryptoJS.mode.ECB,
//     padding: CryptoJS.pad.Pkcs7
//   })
//   return encrypted.toString()
// }

/**
 * CryptoJS 解密
 *
 * @param {String} encryptData  需要加密数据
 * @returns 解密后的数据
 * @memberof Utils
 */
export const decrypt = (encryptData) => {
  var key = CryptoJS.enc.Utf8.parse('as-Crypto-js')
  var decrypt = CryptoJS.AES.decrypt(encryptData, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Utf8.stringify(decrypt).toString()
}
//为指标树 添加序号
export const setTreeIndexLabel = (tree) => {
    let array = newTree(tree)
    array.forEach((item,index)=>{
      array[index].lable = (index+1)+ ' ' +item.lable
        if(array[index].children.length){
          array[index].children.forEach((aItem,aIndex)=>{
            array[index].children[aIndex].lable = (index+1)+ '.'+ (aIndex+1) + ' ' +aItem.lable
                if(array[index].children[aIndex].children.length){
                  array[index].children[aIndex].children.forEach((bItem,bIndex)=>{
                    array[index].children[aIndex].children[bIndex].lable = (index+1)+ '.'+ (aIndex+1) + '.'+ (bIndex+1) +' ' +bItem.lable
                        if(array[index].children[aIndex].children[bIndex].children.length){
                          array[index].children[aIndex].children[bIndex].children.forEach((cItem,cIndex)=>{
                            array[index].children[aIndex].children[bIndex].children[cIndex].lable = (index+1)+ '.'+ (aIndex+1) + '.'+ (bIndex+1) + '.'+ (cIndex+1) +' ' +cItem.lable
                          })
                        }
                  })
                }
          })
        }
    })
    return array
}
export const newTree =(tree)=>{
  let array = []
  tree.forEach((item,index)=>{
    if(item.status != 2){
      if(item.children && item.children.length){
        tree[index].children = newTree(item.children)
      }
      array.push(tree[index])
    }
  })
  return array
}
const formValidation = {
  //经度
  validatorLongitude: (rule, value, callback) => {
      const reg = /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,15})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,15}|180)$/
      if(value === ""){
          callback(new Error('请输入经度'));
      }else{
          if(!reg.test(value)){
              callback(new Error('经度范围：-180~180（保留小数点后十五位）'));
          }
          callback();
      }
  },
//纬度
  validatorLatitude: (rule, value, callback) => {
      const reg = /^(\-|\+)?([0-8]?\d{1}\.\d{0,15}|90\.0{0,15}|[0-8]?\d{1}|90)$/
      if(value === ""){
          callback(new Error('请输入纬度'));
      }else{
          if(!reg.test(value)){
              callback(new Error('纬度范围：-90~90（保留小数点后十五位）'));
          }
          callback();
      }
  },

}
export default formValidation
