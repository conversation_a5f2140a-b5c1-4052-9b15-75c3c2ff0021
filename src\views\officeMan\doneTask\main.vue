<template>
    <div class="done_task_main">
        <div class="search_form">
        <el-row type="flex" :gutter="6">
            <el-col :span="4">
            <el-input
                class="format_option"
                v-model="searchForm.processName"
                placeholder="请输入流程名称"
                clearable
            ></el-input>
            </el-col>

            <el-col :span="5">
                <el-date-picker
                class="format_option"
                v-model="searchForm.time"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
                </el-date-picker>
            </el-col>

            <el-col :span="6">
                <div class="btn_box">
                    <el-button
                    @click="search(false)"
                    class="btn search_btn"
                    icon="el-icon-search"
                    type="primary"
                    >搜索</el-button
                    >
                    <el-button
                    @click="search(true)"
                    class="btn reset_btn"
                    icon="el-icon-refresh"
                    >重置</el-button
                    >
                </div>
            </el-col>
        </el-row>
        </div>
        <!-- <div class="operation_btn">
        <el-button
            class="btn add_btn"
            icon="el-icon-plus"
            type="primary"
            @click="add()"
            >新增</el-button
        >
        </div> -->

        <div class="table_box" v-loading="loading">
        <el-table :data="tableData" style="width: 100%">
            <el-table-column type="index" label="序号" width="80" align="center">
            </el-table-column>
            <el-table-column
            v-for="(item, index) in tableColumn"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            align="center"
            :width="item.width"
            >
            <template slot-scope="{ row }">
                <div v-if="item.checkTime">
                <i class="el-icon-time"></i>
                {{ row[item.prop] | dateTimeFormat }}
                </div>
                <el-tag
                v-else-if="item.tabStatus"
                :type="row.status === 0 ? 'danger' : 'default'"
                >{{ row[item.prop] }}</el-tag
                >
                <div v-else>{{ row[item.prop] }}</div>
            </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" width="150">
            <template slot-scope="{ row }">
                <div class="handle_btn">
                    <el-button
                    type="text"
                    size="small"
                    icon="el-icon-tickets"
                    @click="handleRecord(row)"
                    >流转记录</el-button>
<!--                    <el-popconfirm-->
<!--                        title="是否确定撤回？"-->
<!--                        @confirm="handleSurveyTime(row)"-->
<!--                    >-->
<!--                        <el-button-->
<!--                        slot="reference"-->
<!--                        type="text"-->
<!--                        size="small"-->
<!--                        icon="el-icon-tickets"-->
<!--                        >撤回</el-button-->
<!--                        >-->
<!--                    </el-popconfirm>-->

                </div>
            </template>
            </el-table-column>
        </el-table>
        <div class="page_box">
            <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentPage"
            :pager-count="5"
            :current-page="page.pageNum"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="10"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
            >
            </el-pagination>
        </div>
        </div>
        <flow-doc ref="flowDoc"></flow-doc>
    </div>
</template>
<script>
import flowDoc from './components/flowDoc'
import {finishedList,revokeProcess} from '@/api/officeMan/doneTask'
export default {
components:{flowDoc},
data() {
    return {
    loading: false,
    searchForm: {
        name: "",
    },
    time:[],
    page: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
    },
    tableData: [],
    tableColumn: [
        {
            prop: "taskId",
            label: "任务编号",
            width: "300",
        },
        {
            prop: "procDefName",
            label: "流程名称",
            width: "200",
        },
        {
            prop: "taskName",
            label: "任务节点",
            width: "200",
        },
        {
            prop: "startUserName",
            label: "流程发起人",
            depTabStatus:true,
            width: "200",
        },
        {
            prop: "duration",
            label: "耗时",
            width: "",
        },
        {
            prop: "createTime",
            label: "接收时间",
            checkTime: true,
            width: "200",
        },
        {
            prop: "finishTime",
            label: "审批时间",
            checkTime: true,
            width: "200",
        },
    ],
    };
},
methods: {
    hideDialog() {
        this.loadData();
    },

    // 流转记录
    handleRecord(row){
        this.$refs.flowDoc.init(row)
    },

    // 撤回
    handleSurveyTime(row){
        let params = {
            procInsId:row.procInsId,
            taskId:row.taskId
        }
        this.loading = true
        revokeProcess(params).then((res)=>{
            this.$message({
                type:'success',
                message:'操作成功',
                duration:1500
            })
            this.loadData()
        }).catch(()=>{
            this.loading = false
        })
    },

    search(reset) {
        if (reset) {
            this.searchForm = {
                name: "",
            };
        }
        this.page.pageNum = 1;
        this.loadData();
    },

    loadData() {
        this.searchForm.beginTime = this.time ? this.time[0] : ''
        this.searchForm.endTime = this.time ? this.time[1] : ''
        let params = {
            ...this.searchForm,
            ...this.page
        }
        this.loading = true
        finishedList(params).then((res)=>{
            let resData = res.data
            this.tableData = resData.records
            this.page.total = resData.total
            this.loading = false
        }).catch(()=>{
            this.loading = false
        })
    },

    // 更改每页显示条数
    handleSizeChange(pageSize) {
        this.page.pageSize = pageSize;
        this.loadData();
    },

    // 选择页数/点击上一页/下一页
    handleCurrentPage(currentPage) {
        this.page.pageNum = currentPage;
        this.loadData();
    },
},
created() {
    this.loadData();
},
};
</script>
<style lang="less" scoped>
.done_task_main {
padding: 16px 12px 0;
}
</style>
