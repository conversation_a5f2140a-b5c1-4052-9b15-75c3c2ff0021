<template>
    <el-dialog
      :visible.sync="dialogVisible"
      title="通过"
      append-to-body
      @close="cancel"
      width="30%"
    >
      <div>是否确认该环节审批通过</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading"
          >确 定</el-button
        >
        <el-button @click="cancel"
          >取 消</el-button
        >
      </div>
    </el-dialog>

</template>

<script>
import { riskManageStatus } from "@/api/riskControl/projectRiskList";
export default {
  components:{},

  data(){
    return{
      dialogVisible:false,
      btnLoading:false,
      riskDataId:null,
    }
  },

  methods:{
    init(riskDataId){
        this.dialogVisible = true
        this.btnLoading = false,
        this.riskDataId = riskDataId
    },
    // 审核确定
    confirm() {
      let params = {
          id: this.riskDataId,
          status: 0,
      }
      this.btnLoading = true
      riskManageStatus(params).then((res) => {
          this.$modal.msgSuccess("操作成功");
          this.btnLoading = false;
          this.dialogVisible = false;
          this.$emit('loadData')
      }).catch(()=>{
        this.btnLoading = false
      })
    },

    cancel(){
      this.dialogVisible = false
    }
  },

}

</script>

<style scoped lang='less'>
</style>