<template>
    <el-dialog
        :title="orderInfo.productName"
        :visible.sync="dialogVisible"
        @close="cancel"
        width="40%"
        >
            <div class="container">
                <div class="def-row">
                    <p>令号：{{orderInfo.productCode}}</p>
                    <p>创建时间：{{orderInfo.crtTime | dateFormat}}</p>
                </div>

                <div class="def-row">
                    <p>容量：{{orderInfo.usr04}}万</p>
                    <p>定类：{{orderInfo.projDl}}</p>
                </div>
                <div class="def-row">
                    <p>产品号：{{orderInfo.prodId}}</p>
                </div>
            </div>
    </el-dialog>
</template>

<script>

export default {
  components:{},

  data(){
    return{
        dialogVisible:false,
        orderInfo:{},
    }
  },

  created(){},

  methods:{
    init(row){
        this.orderInfo = row
        this.dialogVisible = true
    },
    cancel(){
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped>
.def-row {
    display:flex;
    margin-bottom:10px;
}
.def-row>p {
    flex:1;
}
</style>