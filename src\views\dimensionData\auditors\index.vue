<template>
  <div class="new_process">

    <div class="search_form">
      <el-form :model="searchForm" ref="searchForm" :inline="true" size="small">
        <!-- 年份 -->
        <el-form-item label="年份">
          <el-date-picker v-model="searchForm.year" class="format_option" size="small" type="year" placeholder="请选择年份"
            value-format="yyyy" @change="confirmYearSelection"></el-date-picker>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input v-model="searchForm.processKey" class="format_option" size="small" placeholder="请输入手机号/姓名"
            clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="search(false)" size="small" class="btn search_btn" icon="el-icon-search"
            type="primary">搜索</el-button>
          <el-button @click="search(true)" size="small" class="btn reset_btn" icon="el-icon-refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="operation_btn">
      <el-button class="btn add_btn" size="small" icon="el-icon-plus" type="primary"
        @click="handleLaunch()">新增</el-button>
    </div>
    <div class="table_box">
      <el-table v-loading="loading" height="570" :data="tableData" row-key="id" style="width: 100%">
        <el-table-column type="index" label="序号" width="50" align="center">
        </el-table-column>
        <el-table-column prop="name" label="姓名" width="200" align="center">
        </el-table-column>
        <el-table-column prop="username" label="工号" width="200" align="center">
        </el-table-column>
        <el-table-column prop="year" label="年份" width="200" align="center">
        </el-table-column>
        <el-table-column prop="phone" label="联系方式" width="200" align="center">
        </el-table-column>
        <el-table-column prop="deptName" label="所属部门" align="center">
        </el-table-column>
        <el-table-column prop="deteledStatus" label="状态" align="center">
          <template slot-scope="{row}">
            <el-tag v-if="row.deteledStatus == 0" size="mini" type="success">未删除</el-tag>
            <el-tag v-if="row.deteledStatus == 1" size="mini" type="danger">已删除</el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="250">
          <template slot-scope="{row}">
            <div class="handle_btn">
              <el-button type="text" @click="handleDelete(row)" size="small" icon="el-icon-video-play">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="page_box" style="margin-top: 10px;">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentPage"
          :current-page="page.pageNum" :pager-count="5" :page-sizes="[10, 20, 30, 40]" :page-size="10"
          layout="total, sizes, prev, pager, next, jumper" :total="page.total">
        </el-pagination>
      </div>
    </div>

    <launch-process ref="launchProcess" @hidDialog="hidDialog"></launch-process>
    <flow-chart ref="flowChart"></flow-chart>
  </div>

</template>

<script>
import LaunchProcess from './components/launchProcess.vue';
import { processList, apiDelete } from '@/api/dimensionData/auditors'
import flowChart from './components/flowChart.vue';
export default {
  components: {
    LaunchProcess,
    flowChart
  },
  data() {
    return {
      loading: false,
      typeOptions: [],
      tableData: [],

      searchForm: {
        processKey: '',
        handleQuery: '',
        category: '',
        year: ''
      },

      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0
      },

      tableColumn: [
        {
          prop: 'processKey',
          label: '流程标识',
          width: '200'
        },
        {
          prop: 'processName',
          label: '流程名称',
          linkstatus: true,
          width: '200'
        },
        {
          prop: 'categoryName',
          label: '流程分类',
          width: '150'
        },
        {
          prop: 'version',
          label: '流程版本',
          versionStatus: true,
          width: ''
        },
        {
          prop: 'suspendedVal',
          label: '状态',
          tabStatus: true,
          width: '100'
        },
        {
          prop: 'deploymentTime',
          label: '部署时间',
          checkTime: true,
          width: '200'
        },
      ]
    };
  },

  beforeCreate() {

  },

  created() {
    this.loadData();
  },
  methods: {
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return apiDelete({ id: row.id });
        })
        .then(() => {
          this.loadData();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },

    // 搜索/重置
    search(reset) {
      if (reset) {
        this.searchForm = {
          processKey: '',
          handleQuery: '',
          category: '',
          year: ''
        };
      }
      this.page.pageNum = 1;
      this.loadData();
    },

    // 流程图
    handleProcessView(row) {
      this.$refs.flowChart.init(row)
    },

    // 发起
    handleLaunch(row) {
      this.$refs.launchProcess.onSelectNextUsers()
    },

    loadData() {
      this.loading = true
      let params = {
        ...this.page,
        ...this.searchForm
      }
      processList(params).then((res) => {
        let resData = res.data

        this.tableData = resData.records
        this.page.total = resData.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    hidDialog() {
      console.log("hidDialog");
      this.loadData();
    },
    // 更改每页显示条数
    handleSizeChange(pageSize) {
      this.page.pageSize = pageSize
      this.loadData()
    },

    // 选择页数/点击上一页/下一页
    handleCurrentPage(currentPage) {
      this.page.pageNum = currentPage
      this.loadData()
    },
    confirmYearSelection(val) {
      this.searchForm.year = val
    }

  }
};
</script>
<style lang="less">
.search_title {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.operation_btn {
  margin: 10px 0;
}

.new_process {
  padding: 16px 12px 0;
}

.page_box {
  display: flex;
  justify-content: flex-end;
}
</style>
