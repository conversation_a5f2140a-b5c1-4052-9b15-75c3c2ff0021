import request from '@/utils/request'
// 工序管理分页
export function getProductPage(param) {
    return request({
        url: '/mes-server/sm/product',
        method: 'get',
        params: param,
    })
}

// 工序管理新增
export function addProduct(param) {
    return request({
        url: '/mes-server/sm/product',
        method: 'post',
        data: param,
    })
}

// 工序管理修改
export function putProduct(param) {
    return request({
        url: '/mes-server/sm/product',
        method: 'put',
        data: param,
    })
}

// 工序管理批量删除
export function delProductList(param) {
    return request({
        url: '/mes-server/sm/product',
        method: 'delete',
        data: param,
    })
}

// 工序管理删除
export function delProductById(id) {
    return request({
        url: `/mes-server/sm/product/${id}`,
        method: 'delete',
    })
}

// 工序管理开关修改
export function putProductStatus(param) {
    return request({
        url: `/mes-server/sm/product/${param.id}/switch/${param.status}`,
        method: 'put',
        data: param,
    })
}

// 批量启用
export function putProductEnable(param) {
    return request({
        url: `/mes-server/sm/product/enable`,
        method: 'put',
        data: param,
    })
}