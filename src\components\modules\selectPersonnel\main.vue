<template>
    <div>
        <div class="el-select" @click="showSelectPersonnel" @mouseenter="isClear = value[defaultProps.valKey] ? true :false" @mouseleave="isClear = false">
            <div class="el-input el-input--suffix" :class="{'el-input--small':size === 'small'}">
                <div v-if="!highlightCurrentRow" class="el-select__tags">
                    <span>
                        <span class="el-tag el-tag--info el-tag--small el-tag--light">
                            <span class="el-select__tags-text"></span>
                            <i class="el-tag__close el-icon-close"></i>
                        </span>
                        <span class="el-tag el-tag--info el-tag--small el-tag--light">
                            <span class="el-select__tags-text"></span>
                        </span>
                    </span>
                </div>
                <input v-else type="text" readonly="readonly" autocomplete="off" :value="valueObj | checkVal(defaultProps.valLabel)" :placeholder="placeholder" class="el-input__inner" />
                <span class="el-input_suffix">
                    <i v-if="isClear" class="el-icon-circle-close" @click.stop="clearVal"></i>
                    <i v-else class="el-icon-plus"></i>
                </span>
            </div>
        </div>
        <el-dialog
            title="选择人员"
            :append-to-body='true'
            :visible.sync="dialogVisible"
            @close="cancel"
            width="50%"
            >
            <div class="dialog_con">
                <div class="search_form" style="margin-bottom: 20px;">
                    <el-row type="flex" :gutter="6" >

                        <el-col :span="8">
                            <el-input v-model="searchForm.query" class="format_option" size="small" placeholder="姓名/用户名/手机号/邮箱" clearable></el-input>
                        </el-col>

                        <el-col :span="8">
                            <div class="btn_box">
                                <el-button size="small" class="btn search_btn" icon="el-icon-search" type="primary" @click="search(false)">搜索</el-button>
                                <el-button size="small" class="btn reset_btn" icon="el-icon-refresh" @click="search(true)">重置</el-button>
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div class="table_box">
                    <el-table
                    ref="multipleTable"
                    :highlight-current-row="highlightCurrentRow"
                    @row-click="singleElection"
                    @row-dblclick="dbSingleElection"
                    @selection-change="handleSelectionChange"
                    v-loading="loading"
                    :data="tableData"
                    :row-key="defaultProps.value"
                    style="width: 100%">
                        <el-table-column
                            v-if="highlightCurrentRow"
                            type="radio"
                            width="30"
                            align="center">
                            <template slot-scope="{row}">
                                <el-radio class="radio" v-model="selectKeys" :label="row[defaultProps.value]"></el-radio>
                            </template>
                        </el-table-column>
                        <el-table-column
                            v-else
                            type="selection"
                            width="55"
                            align="center">
                        </el-table-column>

                        <el-table-column
                            type="index"
                            label="序号"
                            width="50"
                            align="center">
                        </el-table-column>

                        <el-table-column
                            v-for="(item,index) in config.tableColumn"
                            :key="index"
                            :prop="item.prop"
                            :label="item.label"
                            align="center"
                            :width="item.width">
                            <template slot-scope="{row}">
                                <el-tag v-if="item.tabStatus" :type="row[item.prop] | checkTargetVal(item.options)">{{row[item.prop] | checkDic(item.dicVal)}}</el-tag>
                                <div v-else>{{row[item.prop]}}</div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="page_box">
                        <el-pagination
                            background
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentPage"
                            @prev-click="handlePage"
                            @next-click="handlePage"
                            :current-page="page.page"
                            :pager-count="5"
                            :page-sizes="[10, 20,30,40]"
                            :page-size="10"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="page.total">
                        </el-pagination>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="confirm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props:{
        itemData:{
            type:Object,
            default(){
                return {}
            }
        },
        defaultProps:{
            type:Object,
            default(){
                return {}
            }
        },
        value:{
            type:Object,
            required: true,
            default(){
                return {}
            }
        },

        // 配置项 （包含表格配置项、请求的方法）
        config:{
            type:Object,
            required: true
        },

        size:{
            type:String,
            default(){
                return 'small'
            }
        },

        // 是否单选 默认单选
        highlightCurrentRow:{
            type:Boolean,
            default(){
                return true
            }
        },
        placeholder:{
            type:String,
            default(){
                return ''
            }
        }
    },
    data(){
        return {
            isClear:false,
            loading:false,
            dialogVisible:false,
            valueObj:{},    // 输入框中的对象
            targetObj:[],   // 回显选中的目标对象
            selectKeys:[],  // 选中数据key组成的数组
            tableData:[],   // 表格的数据
            searchForm:{
                query:''
            },
            page:{
                size:10,
                page:1,
                total:0
            },
        }
    },

    watch:{
        value:{
            handler(val){
                this.valueObj = val
                this.collbackSelected(val)
            },
            deep:true,
        }
    },
    filters:{
        checkVal(data,label){
            return data ? data[label] : ''
        },
        checkTargetVal(attr,obj){
            if(attr === '' || attr === null || attr === undefined)return '';
            let targetObj = obj.find((item)=>{
                return item.key === attr
            })
            return targetObj.label
        }
    },
    methods:{
        // 清空值
        clearVal(){
            this.valueObj[this.defaultProps.valLabel] = ''
            this.valueObj[this.defaultProps.valKey] = ''
            this.targetObj = null
            this.$emit('input',this.valueObj)
            this.$emit('change',{},this.itemData)
        },

        handleSelectionChange(){},

        // 点击确定
        confirm(){
            this.valueObj[this.defaultProps.valLabel] = this.targetObj[this.defaultProps.label]
            this.valueObj[this.defaultProps.valKey] = this.targetObj[this.defaultProps.value]
            this.$emit('input',this.valueObj)
            this.$emit('change',this.targetObj,this.itemData)
            this.selectKeys = []
            this.dialogVisible = false
        },

        // 取消
        cancel(){
            this.selectKeys = []
            this.dialogVisible = false
        },

        // 单击选中行
        singleElection(row) {
            this.selectKeys = row[this.defaultProps.value]
            this.targetObj = row
        },

        // 双击
        dbSingleElection(row){
            this.selectKeys = row[this.defaultProps.value]
            this.targetObj = row
            this.confirm()
        },

        // 搜索
        search(reset){
            if(reset){
                this.searchForm = {
                    query:""
                }
            }
            this.page.page = 1
            this.loadData()
        },

        // 显示弹框
        async showSelectPersonnel(){
            this.dialogVisible = true
            this.page.page = 1
            this.loadData()
        },

        // 获取列表数据
        async loadData(){
            this.loading = true
            let params = {
                ...this.searchForm,
                ...this.page
            }
            await this.config['method'](params).then((res)=>{
                let resData = res.data
                this.tableData = resData
                this.selectedArr()
                this.page.total = res.total
                this.loading = false
            }).catch(()=>{
                this.loading = false
            })
        },

        // 回显输入框数据
        collbackSelected(value){
            if(Array.isArray(value)){
                this.tableData.forEach((item)=>{
                    if(this.value.includes(item[this.defaultProps.value])){
                        this.targetObj.push(item)
                    }
                })
            }
            // else{
            //     console.log('this.tableData',this.tableData)
            //     this.targetObj = this.tableData.find((item)=>{
            //         return this.value == item[this.defaultProps.value]
            //     })
            //     console.log('targetObj',this.targetObj)
            // }
        },

        // 回显表格数据
        async selectedArr(){
            if(Array.isArray(this.value)){
                this.tableData.forEach((item)=>{
                    if(this.value.includes(item[this.defaultProps.value])){
                        this.$nextTick(()=>{
                            this.$refs.multipleTable.toggleRowSelection(item, true)
                        })
                    }
                })
            }else{
                this.selectKeys = this.value[this.defaultProps.valKey]
            }
        },

        // // 初始化下载数据
        // async initLoad(){
        //     await this.loadData()
        //     await this.selectedArr()
        // },

        // 更改每页显示条数
        handleSizeChange(size){
            this.page.size = size
            this.loadData()
        },

        // 选择页数
        handleCurrentPage(currentPage){
            this.page.page = currentPage
            this.loadData()
        },

        // 点击上一页/下一页
        handlePage(currentPage){
            this.page.page = currentPage
            this.loadData()
        },
    },
    created(){
        this.valueObj = this.value
    }
}
</script>
<style lang="less" scoped>
.dialog_con {
    .el-table {
        min-height:230px;
        /deep/.el-table__body-wrapper {
            .el-table__row {
                cursor:pointer;
            }
        }
    }
}
.el-select {
    width:100%;
    .el-input--small {
        line-height:32px;
    }
    .el-input {
        position:relative;
        .el-input_suffix {
            position:absolute;
            top:1px;
            right:11px;
            color:#DCDFE6;
            font-size:14px;
            cursor:pointer;
        }
    }
}
</style>
