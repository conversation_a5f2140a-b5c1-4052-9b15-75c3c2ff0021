import request from "@/utils/request";

// 分页查询供应商研发投入
export function getSupplierPage(data) {
  return request({
    url: "/sd-server/supplier/rd/page",
    method: "get",
    params: data,
  });
}

// 导出选中的供应商研发投入数据
export function exportSuppliers(data) {
  return request({
    url: "/sd-server/supplier/rd/export",
    method: "get",
    params: data,
    responseType: "blob",
  });
}

// 下载导入模板
export function downloadTemplateFile() {
  return request({
    url: "/sd-server/supplier/rd/template",
    method: "get",
    responseType: "blob",
  });
}

// 下载文件
export function downloadFile(fileId) {
  return request({
    url: `/sd-server/file/download/${fileId}`,
    method: "get",
    responseType: "blob",
  });
}

// 获取供应商详情
export function getSupplierDetail(id) {
  return request({
    url: `/sd-server/supplier/rd/${id}`,
    method: "get",
  });
}

// 更新行业影响力得分
export function updateIndustryScore(data) {
  return request({
    url: "/sd-server/supplier/rd/industry-score",
    method: "put",
    data,
  });
}

// 批量导入
export function importSuppliers(data) {
  return request({
    url: "/sd-server/supplier/rd/import",
    method: "post",
    data,
  });
}
