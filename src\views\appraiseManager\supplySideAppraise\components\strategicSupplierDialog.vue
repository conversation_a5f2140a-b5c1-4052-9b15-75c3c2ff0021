<template>
  <el-dialog title="战略供应商维护" :visible.sync="dialogVisible" width="900px" :before-close="handleClose" :top="'10vh'"
    v-loading="loading">
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" ref="searchForm">

        <el-form-item>
          <el-input v-model="searchForm.query" placeholder="供方编码/供方编号/供方名称" clearable class="search-input" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button plain icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="button-area">
      <el-button type="primary" icon="el-icon-plus" @click="openBatch" size="small">导入</el-button>
      <el-button type="primary" icon="el-icon-download" @click="openBatch" size="small">导出</el-button>
    </div>

    <el-table :data="tableData" border stripe style="width: 100%" v-loading="tableLoading">
      <el-table-column type="index" label="序号" width="60" align="center">
        <template slot-scope="scope">
          {{ page.current * page.size - page.size + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="supplierCode" label="供方编码" width="120" align="center" />
      <el-table-column prop="supplierNo" label="供方编号" width="120" align="center" />
      <el-table-column prop="supplierName" label="供方名称" min-width="200" align="center" />
      <el-table-column prop="scoreName" label="供方评级" width="120" align="center" />
    </el-table>

    <div class="pagination-container">
      <el-pagination :current-page="page.current" :page-sizes="[10, 20, 30, 50]" :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper" :total="page.total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { apiGetStrategyList } from '@/api/appraiseManager/supplySideAppraise'

export default {
  name: 'StrategicSupplierDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableLoading: false,
      searchForm: {
        query: '',
      },
      defaultSearchForm: {
        query: '',
      },
      tableData: [],
      page: {
        current: 1,
        size: 10,
        total: 0
      }
    }
  },

  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.fetchData()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },

  methods: {
    //功能开发中弹窗提示
    openBatch() {
      this.$modal.msgError('功能开发中');
    },
    async fetchData() {
      try {
        this.tableLoading = true
        const params = {
          page: this.page.current,
          size: this.page.size,
          query: this.searchForm.query,
        }
        const res = await apiGetStrategyList(params)
        this.tableData = res.data.records
        this.page.total = res.data.total
      } catch (error) {
        this.$message.error('获取数据失败')
      } finally {
        this.tableLoading = false
      }
    },

    handleSearch() {
      this.page.current = 1
      this.fetchData()
    },

    handleReset() {
      this.$refs.searchForm.resetFields()
      this.searchForm = Object.assign({}, this.defaultSearchForm)
      this.page.current = 1
      this.fetchData()
    },

    handleSizeChange(val) {
      this.page.size = val
      this.fetchData()
    },

    handleCurrentChange(val) {
      this.page.current = val
      this.fetchData()
    },

    handleImport() {
      // TODO: 实现导入功能
      this.$message.info('导入功能开发中')
    },

    handleExport() {
      // TODO: 实现导出功能
      this.$message.info('导出功能开发中')
    },

    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.search-area {
  margin-bottom: 16px;
}

.search-input {
  width: 300px;
}

.year-picker {
  width: 120px;
}

.button-area {
  margin-bottom: 16px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}
</style>
