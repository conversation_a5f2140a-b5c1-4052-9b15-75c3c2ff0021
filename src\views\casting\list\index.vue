<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="机组名称" prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="机组令号" prop="posid">
        <el-input v-model="queryParams.posid" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="部套名称" prop="dwgName">
        <el-input v-model="queryParams.dwgName" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="部套号" prop="dwgNo">
        <el-input v-model="queryParams.dwgNo" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="零件名称" prop="bismtName">
        <el-input v-model="queryParams.bismtName" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="零件图号" prop="bismt">
        <el-input v-model="queryParams.bismt" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="供应商名称" prop="supplierName">
        <el-input v-model="queryParams.supplierName" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="供应商编码" prop="supplierCode">
        <el-input v-model="queryParams.supplierCode" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="合同编号" prop="contractNo">
        <el-input v-model="queryParams.contractNo" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="签订人" prop="contractUserName">
        <el-input v-model="queryParams.contractUserName" placeholder="请输入" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="alarmStatus">
        <el-select v-model="queryParams.alarmStatus" placeholder="请选择" clearable>
          <el-option v-for="dict in status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="采购订单号" prop="ebeln">
        <el-input v-model="queryParams.ebeln" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="采购订单行号" prop="ebelp">
        <el-input v-model="queryParams.ebelp" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item style="margin-left: 40px;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="submitFormBatch">批量修改状态</el-button>
        <el-button type="primary" icon="el-icon-upload2" size="mini" plain :disabled="total == 0" @click="exportList">导出</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange" height="50vh">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" align="center" />
      <el-table-column label="物料编码" prop="matnr" width="150" align="center" />
      <el-table-column label="零件图号" prop="bismt" width="150" align="center" />
      <el-table-column label="零件名称" prop="bismtName" width="120" align="center" />
      <el-table-column label="当前节点" prop="nodeName" width="120" align="center" />
      <el-table-column label="当前节点备注" prop="nodeRemarks" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="节点计划时间" prop="planTime" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="厂商节点预计完成时间" prop="supplierPlanTime" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="交货时间" prop="lfdat" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="厂商预计完成时间" prop="zrsv08" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="采购组" prop="ekgrp" width="120" align="center" show-overflow-tooltip />
      <el-table-column label="供应商" prop="supplierName" width="220" align="center" show-overflow-tooltip />
      <el-table-column label="合同编号" prop="contractNo" width="120" align="center" />
      <el-table-column label="合同签订人" prop="contractUserName" width="120" align="center" >
        <template slot-scope="scope">
          <div v-if="scope.row.zrsv10">{{ scope.row.zrsv10 }}({{ scope.row.ernam }})</div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column label="部套编码" prop="dwgNo" width="120" align="center" />
      <el-table-column label="机组令号" prop="posid" width="120" align="center" />
      <el-table-column label="采购订单号" prop="ebeln" width="120" align="center" />
      <el-table-column label="采购订单行号" prop="ebelp" width="120" align="center" />
      <el-table-column label="状态" prop="alarmStatus" align="center">
        <template slot-scope="scope">
          <el-tag :type="{ 1: 'danger', 2: 'warning', 3: 'success', 4: 'info' }[scope.row.alarmStatus]">{{
            ["报警", "预警", "正常", "完成"][scope.row.alarmStatus - 1] }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="300" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="showItem(scope.row)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleUpdate(scope.row)">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit"
            @click.stop="handleUpdateProgress(scope.row)">修改进度</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit"
            @click.stop="handleUpdateNode(scope.row)">修改节点</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.size"
      @pagination="getList" />

    <!-- 修改进度 -->
    <el-dialog :visible.sync="openProgress" :title="title" width="500px" center>
      <el-form ref="formData" :model="updateProgressForm">
        <el-form-item label-width="80px" label="完成状态" prop="">
          <el-select v-model="updateProgressForm.status" filterable style="width: 320px" placeholder="请选择">
            <el-option v-for="item in moduleListProgress" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="openProgress = false">取 消</el-button>
        <el-button type="primary" @click="submitForm()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 部套详情 -->
    <el-dialog title="部套详情" :visible.sync="open" width="660px" append-to-body>
      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>铸锻件名称：<span>{{ dwgList.bismtName }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>铸锻件编码：<span>{{ dwgList.bismt }}</span></span>
          </div>
        </el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>类别：<span>{{ dwgList.zrsv03 }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>物料：<span>{{ dwgList.matnr }}</span></span>
          </div>
        </el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>物料凭证：<span>{{ dwgList.mblnr }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>物料编码：<span>{{ dwgList.idnrk }}</span></span>
          </div>
        </el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>物料描述：<span>{{ dwgList.maktx }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>采购组：<span>{{ dwgList.ekgrp }}</span></span>
          </div>
        </el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>计划交货时间：<span>{{ dwgList.plifz }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>库存地点：<span>{{ dwgList.lgort }}</span></span>
          </div>
        </el-col>
      </el-row>

      <el-divider> </el-divider>

      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>机组名称：<span>{{ dwgList.post1 }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>令号：<span>{{ dwgList.posid }}</span></span>
          </div>
        </el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>容量：<span>8{{ dwgList.usr04 }}MW</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>产品号：<span>{{ dwgList.projDl }}</span></span>
          </div>
        </el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>定类：<span>{{ dwgList.projDl }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>模块名称：<span>{{ dwgList.moduleName }}</span></span>
          </div>
        </el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>板块：<span>{{ dwgList.boardName }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>模块编码：<span>{{ dwgList.moduleCode }}</span></span>
          </div>
        </el-col>
      </el-row>
    </el-dialog>

    <!-- 项目名称详情弹框 -->
    <el-dialog title="项目详情" :visible.sync="itemOpen" width="600px" append-to-body>
      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>机组名称：<span>{{ posidList.post1 }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>令号：<span>{{ posidList.posid }}</span></span>
          </div>
        </el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>产品类型：<span>{{ posidList.prodType }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>机组容量：<span>{{ posidList.usr04 }}</span></span>
          </div>
        </el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>合同签订日期：<span>{{ posidList.zps0177 }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>定类：<span>{{ posidList.opowerPl }}</span></span>
          </div>
        </el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>所属电厂：<span>{{ posidList.opowerPl }}</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>部套名称：<span>{{ posidList.dwgName }}</span></span>
          </div>
        </el-col>
      </el-row>
    </el-dialog>
    <Detail :detailVisible.sync="detailVisible" :castingId="castingId" @maintenance="goMaintenance" />
    <UpdateNode :visible.sync="updateNodeVisible" :id="updateCastingId" @refresh="getList" />
  </div>
</template>

<script>
import {
  findAllPage,
  findAllModuleList,
  findAllUnitList,
  updateStatus,
  exportList,
  updateStatusBatch,
  findAllDwgList,
} from "@/api/cast/list";
import Detail from "./detail.vue";
import UpdateNode from "./UpdateNode.vue";
import { exportFile } from '@/utils/gloabUtile';
export default {
  components: {
    Detail,
    UpdateNode
  },
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      updateNodeVisible: false,
      updateCastingId: "",
      detailVisible: false,
      castingId: "",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      posid: '',
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openProgress: false,
      // 项目名称是否显示弹出层
      itemOpen: false,
      status: [
        { label: "报警", value: 1 },
        { label: "预警", value: 2 },
        { label: "正常", value: 3 },
        { label: "已完成", value: 4 },
      ],
      dataForm: {
        keyword: undefined,
      },
      dataList: {
        keyword: undefined,
        posid: undefined,
      },

      //机组详情
      posidList: {},
      //部套详情
      dwgList: {},
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        posid: '', //令号
        moduleCode: '', //模块编号
        alarmStatus: '', //部套状态
        query: '', //关键字
        projectName: '', //项目名称
        dwgName: '', //部套名称
        dwgNo: '', //零件名称
        bismtName: '', //零件名称
        bismt: '', //图号
        supplierName: '', //供应商名称
        supplierCode: '', //供应商编号
        contractNo: '', //合同号
        contractUserName: '', //合同签订人
        ebeln: '', //采购订单号
        ebelp: '', //采购订单行号
        ids: [], //选中ids
      },
      //机组列表
      engineeringList: [],
      //模块列表
      moduleList: [],
      dwgOptionList: [],
      moduleListProgress: [{ value: '0', label: '未完成' }, { value: '1', label: '已完成' }],
      updateProgressForm: {
        castingId: '',
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" },
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.$route.query.row && (this.queryParams.ebelp = this.$route.query.row.ebelp) && (this.queryParams.ebeln = this.$route.query.row.ebeln)
    this.getEngineeringList();
    this.getModuleList();
    this.getDwgList();
    this.getList();
  },
  mounted() {
    this.dataForm.keyword = this.queryParams.posid;
    this.dataList.posid = this.dataForm.keyword;
  },
  methods: {
    goMaintenance(row) {
      this.detailVisible = false;
      this.queryParams.ebelp = row.ebelp
      this.queryParams.ebeln = row.ebeln
      this.handleQuery()
    },
    // 修改节点
    handleUpdateNode(row) {
      this.updateNodeVisible = true;
      this.updateCastingId = row.id;
    },
    // 项目名称弹窗
    showItem(row) {
      this.castingId = row.id;
      this.detailVisible = true;
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      findAllPage(this.queryParams).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 导出
    exportList() {
      const _this = this
      this.$confirm('是否导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.queryParams.ids = this.ids.join(','),
        exportFile('/back-server/castingPurchase/list/export', this.queryParams)
      });
    },
    //机组列表
    getEngineeringList() {
      findAllUnitList(this.dataForm).then((res) => {
        res.data.forEach((element, index) => {
          this.engineeringList.push({
            value: element.value,
            label: element.text + "(" + element.value + ")",
          });
        });
      });
    },
    //机组下拉-模块联动
    changeEngineering(value) {
      this.posid = value
      this.getModuleList(value);
    }, //机组下拉-模块联动
    changeModule(value) {
      this.getDwgList(value);
    },

    //模块列表
    getModuleList(value) {
      this.moduleList = []
      findAllModuleList({ posid: value }).then((res) => {
        res.data.forEach((element, index) => {
          this.moduleList.push({
            value: element.value,
            label: element.text + "(" + element.value + ")",
          });
        });
      });
    },
    //部套列表
    getDwgList(value) {
      this.dwgOptionList = []
      findAllDwgList({ posid: this.posid, moduleCode: value }).then((res) => {
        res.data.forEach((element, index) => {
          this.dwgOptionList.push({
            value: element.value,
            label: element.text + "(" + element.value + ")",
          });
        });
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.posid = undefined;
      this.queryParams.moduleCode = undefined;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加岗位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.dwgList = row;
      this.open = true;
      this.title = "部套详情";
    },
    /** 修改按钮操作 */
    handleUpdateProgress(row) {
      this.openProgress = true;
      this.updateProgressForm.castingId = row.id;
      this.updateProgressForm.status = row.manualCompletionStatus + '';
      this.title = "修改进度";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.openProgress = false
      updateStatus(this.updateProgressForm).then(res => {
        if (res.code === 1) {
          this.getList()
        }
      })
    },
    /** 提交按钮 */
    submitFormBatch: function () {
      if (this.ids.length < 1) {
        this.$message.warning('请勾选需要修改状态的铸锻件')
        return
      }
      this.$confirm('是否确认将这些铸锻件标记完成?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.openProgress = false
          updateStatusBatch(this.ids).then(res => {
            if (res.code === 1) {
              this.getList()
            }
          })

        })
        .catch(() => { });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    // 项目名称弹出框
    item(row) {
      this.posidList = row;
      this.itemOpen = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.form-col {
  margin-bottom: 10px;
  padding: 0 !important;

  :deep(.el-form-item) {
    display: flex;
    margin: 0;

    .el-form-item__label {
      width: 120px !important;
      white-space: nowrap;
      padding-right: 8px;
      line-height: 32px;
      text-align: right;
    }

    .el-form-item__content {
      width: calc(100% - 120px) !important;
      margin-left: 0 !important;

      .el-input,
      .el-select {
        width: 100%;
      }
    }
  }
}

:deep(.el-row) {
  display: flex;
  flex-wrap: wrap;

  .el-col {

    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3),
    &:nth-child(4) {
      padding-right: 8px;
    }
  }
}

// 确保所有输入框宽度一致
:deep(.el-input__inner),
:deep(.el-select__inner) {
  width: 100% !important;
}

// 调整下拉箭头位置
:deep(.el-select .el-input) {
  width: 100%;
}
</style>
