<template>
  <div>
    <el-dialog
      title="指定QS"
      :visible.sync="dialogFormVisible"
      append-to-body
      width="600px"
      @close="cancel"
    >
      <el-form ref="form" :model="form">
        <el-form-item
          prop="id"
          :rules="[{ required: true, message: '请选择人员', trigger: 'blur' }]"
        >
          <el-radio-group v-model="form.id">
            <el-radio
              v-for="(item, index) in userList"
              :key="index"
              :label="item.id"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getHtcUserList } from "@/api/bigTestExperiment/personnel.js";
import { bmPlanSetQs,submitLastHtc,submitLastQs } from "@/api/bigTestExperiment/remoteSupervision.js";
export default {
  data() {
    return {
      personShow: false,
      title: "",
      formLabelWidth: "120px",
      btnLoading: false,
      dialogFormVisible: false,
      userList: [],
      form: {
        id: "",
      },
      rowData: {},
      htcStatus: null,
    };
  },
  methods: {
    //重置
    rest() {},
    //初始化
    init(val, htcStatus) {
      this.dialogFormVisible = true;
      this.rowData = val;
      this.htcStatus = htcStatus;
      this.form.id = "";
      this.getHtcUserList();
    },

    // 
    getHtcUserList() {
      getHtcUserList()
        .then((res) => {
          this.userList = res.data;
        })
        .catch(() => {});
    },

    changeRadio(val) {
      this.form.finalManagerDeptId = val.deptId;
    },

    // 最后节点HTC提交
    submitLastHtc(){
      let params = {
        htcStatus:1,
        id:this.rowData.id,
        nodeId:this.rowData.nodeId,
        planId:this.rowData.planId
      }
      submitLastHtc(params).then((res)=>{
        this.submitLastQs()
      }).catch(()=>{
        this.btnLoading = false;
      })
    },

    // 最后节点QS提交
    submitLastQs(){
      let params = {
        qsStatus:1,
        id:this.rowData.id,
        nodeId:this.rowData.nodeId,
        planId:this.rowData.planId
      }
      submitLastQs(params).then((res)=>{
        this.$parent.getDetail()
        this.dialogFormVisible = false
      }).catch(()=>{
        this.btnLoading = false;
      })
    },


    // 确定
    confirm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          bmPlanSetQs({
            planId: this.rowData.planId,
            qsId: this.form.id,
          }).then((res) => {
              this.submitLastHtc()
            })
            .catch(() => {
              this.btnLoading = false;
            });
        }
      });
    },

    cancel(){
      this.$refs.form.clearValidate();
      this.dialogFormVisible = false
    }
  },
};
</script>
<style scoped lang="scss">
.btn-box {
  margin-bottom: 8px;
  padding-left: 36px;
  display: flex;
  align-items: center;
  .btn-label {
    width: 85px;
  }
}
</style>
