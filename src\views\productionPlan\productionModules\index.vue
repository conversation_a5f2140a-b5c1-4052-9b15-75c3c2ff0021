<template>
  <div class="app-container">
    <div v-if="showListPage">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="搜索" prop="moduleName">
          <el-input
            v-model="queryParams.query"
            placeholder="请输入"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd()"
            >新增</el-button
          >
        </el-col>

        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleImp()"
            >导入</el-button
          >
        </el-col>

        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="loadData"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="loading"
        :data="tableData"
      >
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="名称" prop="modelName" align="center"/>
        <el-table-column label="备注" prop="remarks" align="center"/>
        <el-table-column
        width="160"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        >
          <template slot-scope="{row}">
            <el-button
              size="mini"
              type="text"
              @click="handleDetail(row)"
              >查看</el-button
            >

            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleAdd(row)"
              >修改</el-button
            >

            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDel(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="loadData"
      />
    </div>

    <detail-modules v-if="!showListPage" :detailId="detailId" ref="detailModules" @loadData="loadData" @resetQuery="resetQuery"></detail-modules>

    <add-modules ref="addModules" @loadData="loadData"></add-modules>

    <import-model ref="importModel" @loadData="loadData"></import-model>
  </div>
</template>

<script>
import { amountList,deletePartAmount } from "@/api/productionPlan/productionModules.js";
import addModules from './components/addModules'
import detailModules from './components/detailModules'
import importModel from './components/importModel'
export default {
  components: {addModules,detailModules,importModel},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      tableData: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query:''
      },
      //机组详情
      posidList: {},
      showListPage:true,
      detailId:0,
    };
  },
  created() {
    this.loadData();
  },
  methods: {

    /** 查看按钮操作 */
    handleDetail(row) {
      this.detailId = row.id
      this.showListPage = false
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.$refs.addModules.init(row);
    },

    // 删除
    handleDel(row){
      this.$confirm('是否确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = [row.id]
          this.loading = true
          deletePartAmount(params).then((res)=>{
            this.$message({
                type:'success',
                message:'操作成功',
                duration:1500
            })
            this.loadData()
          }).catch(()=>{
            this.loading = false
          })
        })
      
    },

    // 导入
    handleImp() {
      this.$refs.importModel.init();
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.loadData();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.showListPage = true
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 分页查询 */
    loadData() {
      this.loading = true;
      amountList(this.queryParams).then((response) => {
        this.tableData = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      }).catch(()=>{
        this.loading = false
      });
    },

    
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },

    

  },
};
</script>

<style scoped>

</style>
