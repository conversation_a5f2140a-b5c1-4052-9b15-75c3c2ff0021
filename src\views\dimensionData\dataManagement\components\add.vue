<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" destroy-on-close append-to-body width="800px"
    :before-close="handleClose">
    <el-form :model="form" :rules="rules" ref="form" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="资料名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入" :disabled="type == 'view'" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="资料备注" prop="remarks">
            <el-input type="textarea" v-model="form.remarks" placeholder="请输入" :disabled="type == 'view'" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="启用状态" prop="status">
            <el-switch v-model="form.status" :active-value="1" :inactive-value="0" :disabled="type == 'view'" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { apiAdd, apiGetOne, apiUpdate } from '@/api/dimensionData/dataManagement.js'
import { apiGetTypeNoList } from '@/api/appraiseManager/appraiseAll'
import { QualitySecondaryCategory, getQualityTipText } from '@/enums/qualityIndicator'
export default {
  name: 'AddDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      title: "新增",
      secondCategoryList: [],
      typeNoList: [],
      dialogVisible: false,
      form: {
        name: "",
        remarks: "",
        status: 0,
      },
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        remarks: [{ required: true, message: '请输入备注', trigger: 'blur' }],
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          if (this.row) {
            this.form = this.row
            this.title = { view: '查看', edit: '修改', add: '新增' }[this.type]
            this.getAllTypeNo()
          } else {
            this.title = "新增"
          }
          // this.getSecondCategory()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    getTipText() {
      return getQualityTipText(this.form.secondType)
    },
    // 获取详情
    getAllTypeNo() {
      apiGetOne({ id: this.row.id }).then((response) => {
        this.typeNoList = response.data
      })
    },
    // 获取二级分类
    getSecondCategory() {
      apiGetSecondCategory({ type: 1 }).then((response) => {
        this.secondCategoryList = response.data
      })
    },
    handleClose() {
      this.form = {}
      this.dialogVisible = false
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            if (this.form.id) {
              await apiUpdate(this.form)
            } else {
              await apiAdd(this.form)
            }
            this.$message.success('保存成功')
            this.$emit('refresh')
            this.handleClose()
          } catch (error) {
            this.$message.error(error.message || '保存失败')
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.tip-text {
  color: #409EFF;
  font-size: 12px;
  margin-bottom: 20px;
  padding: 0 20px;
  display: flex;
}

.text-nowrap {
  white-space: nowrap;
}

.el-form {
  padding: 0 20px;
}
</style>
