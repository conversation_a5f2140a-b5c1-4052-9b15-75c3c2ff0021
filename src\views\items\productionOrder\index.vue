<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="机组" prop="posid" label-width="40px">
        <el-select
          v-model="queryParams.posid"
          filterable
          placeholder="请选择"
          clearable
          @change="changeDwg"
        >
          <el-option
            v-for="(item,index) in engineeringList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="模块" prop="moduleCode" label-width="40px">
        <el-select
          v-model="queryParams.moduleCode"
          filterable
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(item,index) in moduleList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status" label-width="40px">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable>
          <el-option
            v-for="item in statusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="keyword" label-width="40px">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" type="index" width="50"> </el-table-column>
      <el-table-column label="上层部套名称" prop="dwgNameUp" />
      <el-table-column label="上层部套编码" prop="dwgNoUp">
        <template slot-scope="scope">
          <a
            @click="dwgDetail(scope.row)"
            style="color: #1890ff; cursor: pointer"
            >{{ scope.row.dwgNoUp }}</a
          >
        </template>
      </el-table-column>
      <el-table-column label="令号" prop="posid" />
      <el-table-column label="项目名称" prop="post1">
<template slot-scope="scope">
          <a
            @click="eudDetail(scope.row)"
            style="color: #1890ff; cursor: pointer"
            >{{ scope.row.post1 }}</a
          >
        </template>
      </el-table-column>
      <el-table-column label="部套名称" prop="dwgName" />
      <el-table-column label="部套编码" prop="dwgNo" />
      <el-table-column label="物料描述" prop="maktx" />
      <el-table-column label="物料编码" prop="matnr" />
      <el-table-column label="订单号" prop="id" />
      <el-table-column label="数量" prop="psmng" />
      <el-table-column label="订单状态" prop="status">
        <template slot-scope="scope">
          <span>{{ getStatus(scope.row.status) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="岗位名称" prop="postName">
          <el-input v-model="form.postName" placeholder="请输入岗位名称" />
        </el-form-item>
        <el-form-item label="岗位编码" prop="postCode">
          <el-input v-model="form.postCode" placeholder="请输入编码名称" />
        </el-form-item>
        <el-form-item label="岗位顺序" prop="postSort">
          <el-input-number
            v-model="form.postSort"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="岗位状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog> -->
  <!-- 部套详情 -->
    <el-dialog
      title="部套详情"
      :visible.sync="open"
      width="660px"
      append-to-body
    >
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>部套名称：<span>{{dwgList.dwgName}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>部套编码：<span>{{dwgList.dwgNo}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>分工：<span>{{dwgList.arrange}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>项目经理：<span>{{dwgList.zfstVernr}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>合同交期：<span>{{dwgList.zps0010}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>MM：<span>{{dwgList.moduleGrp}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>上次排期：<span>{{dwgList.zps0079Up}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>项目交期：<span>{{dwgList.zps0079}}</span></span>
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>进展情况：<span>{{dwgList.zprogress}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>预计完成时间：<span>{{dwgList.expDat}}</span></span>
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>未完原因：<span>{{dwgList.unReason}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>状态：<span>{{dwgList.alarmStatus}}</span></span>
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="24"
          ><div class="grid-content bg-purple">
            <span>状态说明：<span>{{dwgList.stRemark}}</span></span>
          </div></el-col
        >
        <!-- <el-col :span="12"><div class="grid-content bg-purple-light"><span>状态：<span>未完成</span></span></div></el-col> -->
      </el-row>
      <el-divider> </el-divider>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>机组名称：<span>{{dwgList.post1}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>令号：<span>{{dwgList.posid}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>容量：<span>8{{dwgList.usr04}}MW</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>产品号：<span>{{dwgList.projDl}}</span></span>
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>定类：<span>{{dwgList.projDl}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>模块名称：<span>{{dwgList.moduleName}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>板块：<span>{{dwgList.boardName}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>模块编码：<span>{{dwgList.moduleCode}}</span></span>
          </div></el-col
        >
      </el-row>
    </el-dialog>
       <!-- 项目名称详情弹框 -->
    <el-dialog
      title="项目详情"
      :visible.sync="itemOpen"
      width="600px"
      append-to-body
    >
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >机组名称：<span>{{ posidList.post1 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >令号：<span>{{ posidList.posid }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >产品类型：<span>{{ posidList.prodType }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >机组容量：<span>{{ posidList.usr04 }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >合同签订日期：<span>{{ posidList.zps0177 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >定类：<span>{{ posidList.projDl }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >所属电厂：<span>{{ posidList.opowerPl }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >板块名称：<span>{{ posidList.boardName }}</span></span
            >
          </div></el-col
        >
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { getEngineeringList, getModuleList } from "@/api/items/items";
import {
  getOrderList,
  getDwgDetail,
  getEuDetail,
  addPost,
  updatePost,
} from "@/api/items/productionOrder";

export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      engineeringList: [],
      moduleList: [],
      itemOpen:false,
      open:false,
      //机组详情
      posidList: {},
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        posid: undefined,
        moduleCode: undefined,
        status: undefined,
        keyword: undefined,
      },
      dwgList: {},
      statusList: [
        {
          label: "订单已创建",
          value: "CRTD",
        },
        {
          label: "订单下发",
          value: "REL",
        },
        {
          label: "已报工",
          value: "CNF",
        },
        {
          label: "已交货",
          value: "DLV",
        },
        {
          label: "手动关闭订单",
          value: "TECO",
        },
      ],
      // 表单参数
      form: {},
      dataForm: {
        keyword: undefined,
      },
      dataList: {
        keyword: undefined,
        posid: undefined,
      },
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" },
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getEngineeringList();
    this.getModuleList(), this.getList();
  },
  methods: {
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getOrderList(this.queryParams).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    //机组列表
    getEngineeringList() {
      getEngineeringList(this.dataForm).then((res) => {
        res.data.forEach((element, index) => {
          this.engineeringList.push({
            value: element.posid,
            label: element.post1+"("+element.posid+")",
          });
        });
      });
    },
    //模块列表
    getModuleList() {
      getModuleList(this.dataList).then((res) => {
        res.data.forEach((element, index) => {
          this.moduleList.push({
            value: element.moduleCode,
            label: element.moduleName+"("+element.moduleCode+")",
          });
        });
      });
    },

    //切换机组
    changeDwg(val){
    },
    //获取状态
    getStatus(stu) {
      let status = this.statusList.find((e) => e.value == stu);
      // console.log(status,11111);
      if (stu) {
        return status.label;
      } else {
        return "";
      }
    },

    //部套详情
    dwgDetail(row) {
      this.loading = true;
      getDwgDetail({ dwgNo: row.dwgNo,posid:row.posid }).then((res) => {
        this.dwgList = res.data;
        this.open=true
        this.loading = false;
      });
    },
      //机组详情
    eudDetail(row) {
      this.loading = true;
      getEuDetail({ posid : row.posid  }).then((res) => {
        this.posidList = res.data;
        this.itemOpen=true
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    // handleAdd() {
    //   this.reset();
    //   this.open = true;
    //   this.title = "添加岗位";
    // },
    /** 修改按钮操作 */
    // handleUpdate(row) {
    //   this.reset();
    //   const postId = row.postId || this.ids;
    //   getPost(postId).then((response) => {
    //     this.form = response.data;
    //     this.open = true;
    //     this.title = "修改岗位";
    //   });
    // },
    /** 提交按钮 */
    // submitForm: function () {
    //   this.$refs["form"].validate((valid) => {
    //     if (valid) {
    //       if (this.form.postId != undefined) {
    //         updatePost(this.form).then((response) => {
    //           this.$modal.msgSuccess("修改成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       } else {
    //         addPost(this.form).then((response) => {
    //           this.$modal.msgSuccess("新增成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       }
    //     }
    //   });
    // },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style  scoped>
::v-deep .el-dialog__body {
  padding-top: 25px !important;
}
::v-deep .el-dialog__body .grid-content{
  padding: 6px;
}
::v-deep .el-row--flex{
  margin-left: 16px;
}
::v-deep .el-dialog{
  margin-top: 30vh !important;
}
</style>

