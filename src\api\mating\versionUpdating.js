import request from '@/utils/request'

export function getList(param) {
  return request({
    url: '/user-server/versionController/findAllPage',
    method: 'get',
    params: param
  })
}

export function addVersion(data) {
  return request({
    url: '/user-server/versionController/add',
    method: 'post',
    data
  })
}

export function updateVersion(data) {
  return request({
    url: '/user-server/versionController/updateVersion',
    method: 'post',
    data
  })
}

export function deleteVersion(param) {
  return request({
    url: '/user-server/versionController/delete',
    method: 'get',
    params: param
  })
}

export function deletesVersion(param) {
  return request({
    url: '/user-server/versionController/deletes',
    method: 'get',
    params: param
  })
}

export function enableVersion(param) {
  return request({
    url: '/user-server/versionController/enableVersion',
    method: 'get',
    params: param
  })
}

