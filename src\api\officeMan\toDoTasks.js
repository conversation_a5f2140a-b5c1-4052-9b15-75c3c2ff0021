import request from '@/utils/request'
// 分页查询新建流程
export function todoList(query) {
    return request({
        url: `/flowable/workflow/process/todoList`,
        method:'get',
        params:query
    });
}

// 发起
export function startProcess(query,processDefId) {
    return request({
        url: `/flowable/workflow/process/start/${processDefId}`,
        method:'post',
        data:query
    });
}

// 审批通过
export function approve(query) {
    return request({
        url: `/flowable/workflow/task/complete`,
        method:'post',
        data:query
    });
}

// 委派
export function delegate(query) {
    return request({
        url: `/flowable/workflow/task/delegate`,
        method:'post',
        data:query
    });
}

// 转办
export function transfer(query) {
    return request({
        url: `/flowable/workflow/task/transfer`,
        method:'post',
        data:query
    });
}

// 退回
export function returnList(query) {
    return request({
        url: `/flowable/workflow/task/return`,
        method:'post',
        data:query
    });
}

// 拒绝
export function rejectTask(query) {
    return request({
        url: `/flowable/workflow/task/reject`,
        method:'post',
        data:query
    });
}









