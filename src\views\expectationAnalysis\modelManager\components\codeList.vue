<template>
    <el-dialog
        title="查看"
        :visible.sync="dialogVisible"
        :append-to-body="true"
        @close="cancel"
        width="40%"
        >
        <div class="container">
            <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%">
                <el-table-column
                    prop="posid"
                    label="令号"
                    align="center"
                    width="">
                </el-table-column>
                <el-table-column
                    prop="post1"
                    label="机组名称"
                    align="center"
                    width="">
                </el-table-column>
            </el-table>
        </div>
    </el-dialog>
</template>

<script>
import { tqListName } from '@/api/expectationAnalysis/modelManager'
export default {
  components:{},

  data(){
    return{
        dialogVisible:false,
        loading:false,
        tableData:[],
    }
  },

  created(){},

  methods:{
    init(row){  
        this.dialogVisible = true
        let codeList = row.codeList || []
        this.loadData(codeList)
    },
    loadData(codeList){
        this.loading = true
        tqListName(codeList).then((res)=>{
            let resData = res.data || []
            this.tableData = resData
            this.loading = false
        }).catch(()=>{
            this.loading = false
        })
    },
    cancel(){
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped lang='less'>
</style>