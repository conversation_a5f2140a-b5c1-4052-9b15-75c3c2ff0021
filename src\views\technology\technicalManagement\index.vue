<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="机组" prop="posid" label-width="40px">
        <el-select v-model="queryParams.posid" placeholder="请选择" clearable>
          <el-option
            v-for="dict in engineeringList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="节点" prop="status" label-width="40px">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->

      <el-form-item label="状态" prop="alarmStatus" label-width="40px">
        <el-select
          v-model="queryParams.alarmStatus"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in studes"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="keyword" label-width="40px">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column label="序号" type="index" />
      <el-table-column label="部套名称" prop="dwgName" />
      <el-table-column label="部套编码" prop="dwgNo" />
      <el-table-column label="令号" prop="posid" />
      <el-table-column label="项目名称" prop="post1">
        <template slot-scope="scope">
          <a @click="item(scope.row)" style="color: #1890ff; cursor: pointer">{{
            scope.row.post1
          }}</a>
        </template>
      </el-table-column>
      <el-table-column label="新设计" prop="newrd" />
      <el-table-column label="采购" prop="purF" />
      <el-table-column label="加工" prop="prodF" />
      <el-table-column label="项目交期" prop="zps0079" />
      <el-table-column label="节点完成数" prop="completed" />
      <el-table-column label="当前节点" prop="nodeName" />
      <el-table-column label="状态" prop="alarmStatus">
        <template slot-scope="scope">
          <span
            style="
              font-size: 12px;
              display: inline-block;
              width: 44px;
              height: 28px;
              line-height: 28px;
              border-radius: 3px;
              text-align: center;
            "
            :class="
              scope.row.alarmStatus == 1
                ? 'bao'
                : scope.row.alarmStatus == 2
                ? 'yu'
                : scope.row.alarmStatus == 3
                ? 'jin'
                : 'finish'
            "
            >{{
              scope.row.alarmStatus == 1
                ? "报警"
                : scope.row.alarmStatus == 2
                ? "预警"
                : scope.row.alarmStatus == 3
                ? "正常"
                : "完成"
            }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleUpdate(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="sayJie(scope.row.nodeList)"
            >查看节点</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="660px" append-to-body>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >部套名称：<span>{{ orderList.dwgName }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >部套编码：<span>{{ orderList.dwgNo }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >新设计：<span>{{ orderList.newrd }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >采购：<span>{{ orderList.purF }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >加工：<span>{{ orderList.prodF }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >项目交期：<span>{{ orderList.zps0079 }}</span></span
            >
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >计划完成时间：<span>{{ orderList.expDat }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >状态：<span>{{ orderList.alarmStatus }}</span></span
            >
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="24"
          ><div class="grid-content bg-purple">
            <span
              >设计备注：<span>{{ orderList.jhbz }}</span></span
            >
          </div></el-col
        >
        <!-- <el-col :span="12"><div class="grid-content bg-purple-light"><span>状态：<span>未完成</span></span></div></el-col> -->
      </el-row>
      <el-divider> </el-divider>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >机组名称：<span>{{ orderList.post1 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >令号：<span>{{ orderList.posid }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >容量：<span>{{ orderList.usr04 }}MW</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >大模块：<span>{{ orderList.moduleGrp }}M</span></span
            >
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >定类：<span>{{ orderList.projDl }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >产品号：<span>{{ orderList.prodType }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >板块编码：<span>{{ orderList.posid }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >板块名称：<span>{{ orderList.boardName }}</span></span
            >
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >模块编码：<span>{{ orderList.moduleCode }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >模块名称：<span>{{ orderList.moduleName }}</span></span
            >
          </div></el-col
        >
      </el-row>
    </el-dialog>
    <!-- 查看节点弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="opens"
      width="523px"
      append-to-body
    >
      <div class="block">
        <el-timeline>
          <el-timeline-item
            color="#1890FF"
            :icon="item.nodeStatus == 2 ? 'el-icon-check' : ''"
            v-for="(item, index) in nodeList"
            :key="index"
          >
            <el-card>
              <div class="font16 fontweight">{{ item.nodeName }}</div>
              <div class="node">
                <span>计划完成时间：{{ item.planTime }}</span>
                <span>实际完成时间：{{ item.finishTime }}</span>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>

    <!-- 项目名称详情弹框 -->
    <el-dialog
      title="项目详情"
      :visible.sync="itemOpen"
      width="660px"
      append-to-body
    >
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >机组名称：<span>{{ posidList.post1 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >令号：<span>{{ posidList.posid }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >产品类型：<span>{{ posidList.prodType }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >机组容量：<span>{{ posidList.usr04 }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >合同签订日期：<span>{{ posidList.zps0177 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >定类：<span>{{ posidList.projDl }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >所属电厂：<span>{{ posidList.opowerPl }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >板块名称：<span>{{ posidList.boardName }}</span></span
            >
          </div></el-col
        >
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { getEngineeringList } from "@/api/items/items";
import {
  getTechnicalDwgList,
  getPost,
  delPost,
  addPost,
  updatePost,
} from "@/api/technology/technicalManagement";

export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      engineeringList: [],
      nodeList: [],
      studes: [
        { label: "正常", value: 0 },
        { label: "报警", value: 1 },
        { label: "预警", value: 2 },
        { label: "完成", value: 3 },
      ],
      //机组详情
      posidList: {},
      orderList: {},
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 节点详情弹出层
      opens: false,
      // 项目名称
      itemOpen: false,
      dataForm: {
        keyword: undefined,
      },
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        posid: undefined,
        alarmStatus: undefined,
        keyword: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" },
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getEngineeringList();
    this.getList();
  },
  methods: {
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getTechnicalDwgList(this.queryParams).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    //机组列表
    getEngineeringList() {
      getEngineeringList(this.dataForm).then((res) => {
        res.data.forEach((element, index) => {
          this.engineeringList.push({
            value: element.posid,
            label: element.post1 + "(" + element.posid + ")",
          });
        });
      });
    },
    //获取状态
    getStatus(stu) {
      let status = this.studes.find((e) => e.value == stu);
      if (stu) {
        return status.label;
      } else {
        return "";
      }
    },
    // 项目名称弹窗
    item(row) {
      this.posidList = JSON.parse(JSON.stringify(row));
      this.itemOpen = true;
    },
    //查看节点
    sayJie(nodeList) {
      this.nodeList = nodeList;
      this.title = "节点详情";
      this.opens = true;
      for (let item of nodeList) {
        if (item.nodeStatus == 2) {
          return false;
        }
        item.nodeStatus = 2;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加岗位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.orderList = row;
      this.open = true;
      this.title = "技术准备详情";
    },
    /**查看节点*/
    handleLook(row) {
      this.opens = true;
      this.title = "节点详情";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style  scoped>
::v-deep .el-dialog__body {
  line-height: 30px;
}
::v-deep .el-card__body {
  line-height: normal;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}
.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
.node {
  font-size: 14px;
  color: #666;
  display: flex;
  flex-direction: column;
}
.font16 {
  font-size: 16px;
  color: #333;
  padding-bottom: 8px;
}
.fontweight {
  font-weight: 700;
}
::v-deep .el-dialog__body {
  padding-top: 25px !important;
}
::v-deep .el-dialog__body .grid-content {
  padding: 6px;
}
::v-deep .el-row--flex {
  margin-left: 16px;
}

::v-deep .el-card.is-always-shadow {
  box-shadow: none;
  border: none;
  background: #f3f7fd;
  border-radius: 5px;
}
</style>