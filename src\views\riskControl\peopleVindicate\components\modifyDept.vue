<template>
    <el-dialog title="新增" :visible.sync="dialogVisible" width="500px" append-to-body>
        <el-form
        :model="form"
        ref="form"
        size="small"
        :rules="rules"
        :inline="true"
        label-width="80px"
        >
            <el-form-item label="部门名称" prop="deptName">
                <el-input
                    v-model="form.deptName"
                    placeholder="请输入部门名称"
                    clearable
                />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="confirm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { deptName } from '@/api/riskControl/peopleVindicate'
export default {
  components:{},

  data(){
    return{
        dialogVisible:false,
        form:{
            deptName:''
        },
        rules:{
            deptName:[{ required: true, message: '请输入部门名称', trigger: 'blur' }] 
        }
    }
  },

  methods:{
    init(row){
        this.dialogVisible = true
        this.form.deptName = row.deptName || ''
        this.form.id = row.id
    },
    confirm(){
        this.$refs['form'].validate((valid) => {
            if(valid){
                let params = {
                    ...this.form
                }
                deptName(params).then((res)=>{
                    this.$modal.msgSuccess("操作成功");
                    this.dialogVisible = false;
                    this.$emit('getList');
                }).catch(()=>{

                })
            }
        })
    },
    cancel(){
        this.$refs['form'].resetFields()
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped>
::v-deep.el-form-item {
    width:100%;
}
::v-deep.el-form-item .el-form-item__content {
    width: calc(100% - 80px);
}
</style>