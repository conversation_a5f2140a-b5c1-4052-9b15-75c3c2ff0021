<template>
  <div>
    <el-dialog
      title="填写炉号"
      :visible.sync="dialogFormVisible"
      append-to-body
      width="500px"
      @close="cancel"
    >
      <el-form ref="form" :model="form">
        <el-form-item
          label="炉号"
          prop="heatNo"
          :rules="[{ required: true, message: '请输入炉号', trigger: 'blur' }]"
        >
          <el-input
            style="width: 400px"
            v-model="form.heatNo"
            placeholder="请输入炉号"
            :maxlength="32"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { putBmHeatNo } from "@/api/bigTestExperiment/remoteSupervision.js";
export default {
  data() {
    return {
      title: "",
      formLabelWidth: "100px",
      btnLoading: false,
      dialogFormVisible: false,
      form: {
        heatNo: "",
      },
      rowData: {},
    };
  },
  methods: {
    //重置
    rest() {},
    //初始化
    init(val) {
      this.dialogFormVisible = true;
      this.rowData = val;
      this.form.heatNo = val.heatNo;
    },

    // 确定
    confirm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          putBmHeatNo({
            heatNo: this.form.heatNo,
            planId: this.rowData.id,
          })
            .then((res) => {
              this.$modal.msgSuccess("操作成功");
              this.btnLoading = false;
              this.dialogFormVisible = false;
              this.$parent.getDetail();
            })
            .catch(() => {
              this.btnLoading = false;
            });
        }
      });
    },

    cancel() {
      this.$refs.form.clearValidate();
      this.dialogFormVisible = false;
    },
  },
};
</script>
<style scoped lang="scss">
.btn-box {
  margin-bottom: 8px;
  padding-left: 36px;
  display: flex;
  align-items: center;
  .btn-label {
    width: 85px;
  }
}
</style>
