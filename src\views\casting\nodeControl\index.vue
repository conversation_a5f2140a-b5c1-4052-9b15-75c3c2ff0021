<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="机组" prop="posid" label-width="40px">
        <el-select @change="changeEngineering" v-model="queryParams.posid" placeholder="请选择" clearable>
          <el-option
            v-for="(item, index) in engineeringList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="模块" prop="moduleCode" label-width="40px">
        <el-select
          @change="changeModule"
          v-model="queryParams.moduleCode"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(item, index) in moduleList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="部套" prop="moduleCode" label-width="40px">
        <el-select
          v-model="queryParams.dwgNo"
          filterable
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(item, index) in dwgOptionList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="序号" type="index" />
      <el-table-column label="铸锻件名称" prop="bismtName">
        <template slot-scope="scope">
          <a
            @click="items(scope.row)"
            style="color: #1890ff; cursor: pointer"
            >{{ scope.row.bismtName }}</a
          >
        </template>
      </el-table-column>
      <el-table-column label="铸锻件编码" prop="bismt" />
      <el-table-column label="令号" prop="posid" />
      <el-table-column label="项目名称" prop="post1">
        <template slot-scope="scope">
          <a @click="item(scope.row)" style="color: #1890ff; cursor: pointer">{{
            scope.row.post1
          }}</a>
        </template>
      </el-table-column>
      <el-table-column label="供应商名称" prop="name1" />
      <el-table-column label="供应商编码" prop="lifnr" />
      <el-table-column label="节点完成数" prop="finishNum" />
      <el-table-column label="当前节点" prop="currentNode" />
      <el-table-column label="记录数" prop="nodeRecordNum" />
      <el-table-column label="开始时间" prop="contractStartTime" />
      <el-table-column label="结束时间" prop="contractEndTime" />
      <el-table-column
        label="操作"
        align="center"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleLook(row)"
            >查看节点</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleStart(row)"
            >修改节点</el-button
          >
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:post:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="岗位名称" prop="postName">
          <el-input v-model="form.postName" placeholder="请输入岗位名称" />
        </el-form-item>
        <el-form-item label="岗位编码" prop="postCode">
          <el-input v-model="form.postCode" placeholder="请输入编码名称" />
        </el-form-item>
        <el-form-item label="岗位顺序" prop="postSort">
          <el-input-number
            v-model="form.postSort"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="岗位状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog> -->

    <!-- 项目名称详情弹框 -->
    <el-dialog
      title="项目详情"
      :visible.sync="itemOpen"
      width="600px"
      append-to-body
    >
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >机组名称：<span>{{ post1List.post1 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >令号：<span>{{ post1List.posid }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >产品类型：<span>{{ post1List.assemblyF }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >机组容量：<span>{{ post1List.usr04 }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >合同签订日期：<span>{{ post1List.zps0177 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >定类：<span>{{ post1List.projDl }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >所属电厂：<span>{{ post1List.name1 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >板块名称：<span>{{ post1List.boardName }}</span></span
            >
          </div></el-col
        >
      </el-row>
    </el-dialog>

    <!-- 铸锻件详情 -->
    <el-dialog title="铸锻件信息" :visible.sync="open" width="600px" append-to-body>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple">
            <span>铸锻件名称：<span>{{ dwgList.bismtName }}</span></span>
        </div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light">
            <span>铸锻件编码：<span>{{ dwgList.bismt }}</span></span>
        </div></el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple">
            <span>类型：<span>{{ dwgList.zrsv03 }}</span></span>
        </div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light">
            <span>物料：<span>{{ dwgList.matnr }}</span></span>
        </div></el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple">
            <span>物料凭证：<span>{{ dwgList.mblnr }}</span></span>
        </div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light">
            <span>物料编码：<span>{{ dwgList.idnrk }}</span></span>
        </div></el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple">
            <span>物料描述：<span>{{ dwgList.maktx }}</span></span>
        </div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light">
            <span>采购组：<span>{{ dwgList.ekgrp }}</span></span>
        </div></el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple">
            <span>计划交货时间：<span>{{ dwgList.plifz }}</span></span>
        </div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light">
            <span>库存地点：<span>{{ dwgList.lgort }}</span></span>
        </div></el-col>
      </el-row>

      <el-divider></el-divider>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >部套名称：<span>{{ dwgList.dwgName }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >部套编码：<span>{{ dwgList.dwgNo }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >分工：<span>{{ dwgList.arrangeName }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >项目经理：<span>{{ dwgList.zfstVernr }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >合同交期：<span>{{ dwgList.zps0010 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >MM：<span>{{ dwgList.moduleGrp }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>上次排期：<span>2022-05-30</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >项目交期：<span>{{ dwgList.zps0079 }}</span></span
            >
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >进展情况：<span>{{ dwgList.zprogressName }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>预计完成时间：<span>2022-05-30</span></span>
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >未完原因：<span>{{ dwgList.zps0079 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>状态：<span>未完成</span></span>
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="24"
          ><div class="grid-content bg-purple">
            <span
              >状态说明：<span>{{ dwgList.zps0079 }}</span></span
            >
          </div></el-col
        >
        <!-- <el-col :span="12"><div class="grid-content bg-purple-light"><span>状态：<span>未完成</span></span></div></el-col> -->
      </el-row>
      <el-divider></el-divider>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >机组名称：<span>{{ dwgList.post1 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >令号：<span>{{ dwgList.posid }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >容量：<span>{{ dwgList.zps0079 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >产品号：<span>{{ dwgList.prodId }}</span></span
            >
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >定类：<span>{{ dwgList.projDl }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >模块名称：<span>{{ dwgList.moduleName }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >板块：<span>{{ dwgList.boardName }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >模块编码：<span>{{ dwgList.moduleCode }}</span></span
            >
          </div></el-col
        >
      </el-row>
    </el-dialog>

    <!-- 查看节点弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="opens"
      width="523px"
      append-to-body
    >
      <div class="block">
        <el-timeline>
          <el-timeline-item
            color="#1890FF"
            :icon="item.nodeStatus == 2 ? 'el-icon-check' : ''"
            v-for="(item, index) in nodeInfoList"
            :key="index"
          >
            <el-card>
              <div class="font16 fontweight">{{ item.nodeName }}</div>
              <div class="node">
                <span>计划完成时间：{{ item.planTime }}</span>
                <span>实际完成时间：{{ item.finishTime }}</span>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>

    <!-- 修改节点 -->
    <el-dialog
      title="修改节点"
      :visible.sync="startVisible"
      width="40vw"
      @close="closeStart"
      append-to-body
    >
      <div class="block" v-loading="startControlLoading">
        <el-table
          class="start_control_table"
          :data="nodeList"
          style="width: 100%"
        >
          <el-table-column type="index" label="序号" width="50">
          </el-table-column>
          <el-table-column prop="nodeName" label="节点" align="left">
            <template slot-scope="{ row }">
              <div>{{ row.nodeName }}</div>
            </template>
          </el-table-column>

          <el-table-column prop="planTime" label="计划时间" align="left">
            <template slot-scope="{ row }">
              <el-date-picker
                v-model="row.planTime"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                :disabled="row.finishStatus == 1 ? true : false"
              >
              </el-date-picker>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="startControl">确定</el-button>
        <el-button @click="closeStart">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPost, delPost, addPost, updatePost } from "@/api/system/post";
import { findAllDwgList, findAllModuleList, findAllUnitList } from '@/api/cast/list'
import { getToken } from "@/utils/auth";
import {
  getCastingNodePage,
  exportData,
  findAllNodeByCastingId,
  findAllNodeByCastingIdForUpdate,
  batchUpdateNodePlanTime,
} from "@/api/cast/nodeControl";
export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      dwgList: {},
      post1List: {},
      startVisible: false, // 修改节点弹框
      startControlLoading: false,
      tartControlForm: {
        contractEndTime: "",
        contractStartTime: "",
      },
      nodeInfoList: [], // 查看节点
      lookLoading: false,
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { "X-Token": "Bearer " + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/back-server/outsourcingOrder/uploadNodeExcel",
      },
      nodeList: [], // 修改节点
      tartControlRow: null,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      posid: "",
      // 是否显示弹出层
      open: false,
      // 查看节点是否显示弹出层
      opens: false,
      // 项目名称是否显示弹出层
      itemOpen: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        moduleCode: undefined, //模块编号
        posid: undefined, //令号
        keyword: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" },
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        ],
      },
      engineeringList: [],
      moduleList: [],
      dwgOptionList: [],
    };
  },
  created() {
    this.getEngineeringList();
    this.getModuleList();
    this.getDwgList();
    this.getList();
  },
  methods: {
    //机组下拉-模块联动
    changeEngineering(value) {
      this.posid = value
      this.getModuleList(value);
    }, //机组下拉-模块联动
    changeModule(value) {
      this.getDwgList(value);
    },
    //部套列表
    getDwgList(value) {
      this.dwgOptionList = []
      findAllDwgList({ posid: this.posid, moduleCode: value  }).then((res) => {
        res.data.forEach((element, index) => {
          this.dwgOptionList.push({
            value: element.value,
            label: element.text + "(" + element.value + ")",
          });
        });
      });
    },
    //模块列表
    getModuleList(value) {
      this.moduleList = []
      findAllModuleList({ posid: value }).then((res) => {
        res.data.forEach((element, index) => {
          this.moduleList.push({
            value: element.value,
            label: element.text + "(" + element.value + ")",
          });
        });
      });
    },
    //机组列表
    getEngineeringList() {
      findAllUnitList(this.dataForm).then((res) => {
        res.data.forEach((element, index) => {
          this.engineeringList.push({
            value: element.value,
            label: element.text + "(" + element.value + ")",
          });
        });
      });
    },
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getCastingNodePage(this.queryParams).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },

    // 修改节点
    handleStart(row) {
      this.startVisible = true;
      findAllNodeByCastingIdForUpdate({ castingId: row.castingId }).then(
        (res) => {
          this.nodeList = res.data;
        }
      );
    },
    // 关闭修改节点弹框
    closeStart() {
      this.tartControlRow = null;
      this.startVisible = false;
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "节点导入";
      this.upload.open = true;
    },
    // 下载导入模板
    download() {
      exportData().then((res) => {
        const url = window.URL.createObjectURL(new Blob([res]));
        const link = document.createElement("a");
        link.target = "_blank";
        link.href = url;
        link.setAttribute("download", "管控模板.xlsx");
        document.body.appendChild(link);
        link.click();
      });
    },
    handleFileUploadProgress() {},

    // 上传成功
    handleFileSuccess(success) {
      this.nodeList = success.data;
      this.$message({
        type: "success",
        message: success.msg,
        duration: 1500,
      });
    },
    // 确认修改
    startControl() {
      batchUpdateNodePlanTime(this.nodeList).then((res) => {
        this.startVisible = false;
      });
    },

    // 上传文件
    submitFileForm() {
      this.$refs.upload.submit();

      this.upload.open = false;
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.orderId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    // handleAdd() {
    //   this.reset();
    //   this.open = true;
    //   this.title = "添加岗位";
    // },
    /**查看节点*/
    handleLook(row) {
      this.lookLoading = true;
      this.opens = true;
      this.title = "节点详情";
      findAllNodeByCastingId({ castingId: row.castingId })
        .then((res) => {
          this.nodeInfoList = res.data;
          this.lookLoading = false;
        })
        .catch(() => {
          this.lookLoading = false;
        });
    },
    /** 修改按钮操作 */
    // handleUpdate(row) {
    //   this.reset();
    //   const postId = row.postId || this.ids;
    //   getPost(postId).then((response) => {
    //     this.form = response.data;
    //     this.open = true;
    //     this.title = "修改岗位";
    //   });
    // },
    /** 提交按钮 */
    // submitForm: function () {
    //   this.$refs["form"].validate((valid) => {
    //     if (valid) {
    //       if (this.form.postId != undefined) {
    //         updatePost(this.form).then((response) => {
    //           this.$modal.msgSuccess("修改成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       } else {
    //         addPost(this.form).then((response) => {
    //           this.$modal.msgSuccess("新增成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       }
    //     }
    //   });
    // },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    // 项目名称弹出框
    item(item) {
      this.post1List = item;
      this.itemOpen = true;
    },
    // 部套名称
    items(item) {
      this.dwgList = item;
      this.open = true;
    },
  },
};
</script>
<style  lang="scss" scoped>
::v-deep .el-dialog__body {
  line-height: 30px;
}
.node {
  display: flex;
  flex-direction: column;
}
.font16 {
  font-size: 16px;
}
.fontweight {
  font-weight: 700;
}
.btn_box {
  margin-bottom: 10px;
}
.data_box {
  margin-bottom: 10px;
  .el-date-editor {
    margin-right: 10px;
  }
}
::v-deep .el-card.is-always-shadow {
  box-shadow: none;
  border: none;
  background: #f3f7fd;
  border-radius: 5px;
}
</style>
