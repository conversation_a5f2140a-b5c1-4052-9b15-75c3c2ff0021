<template>
  <div class="app-container">
    <el-form
      :model="searchForm"
      ref="searchForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
      
    > 
      <el-form-item label="状态" label-width="40px">
        <el-select v-model="searchForm.alarmStatus" placeholder="请选择">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="搜索" label-width="40px">
        <el-input
          class="big-input"
          v-model="searchForm.query"
          placeholder="机组名/令号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery(false)"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="handleQuery(true)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-upload2"
          size="mini"
          plain
          @click="handleExp"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="loadData"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
    >
      <el-table-column 
      label="序号" 
      align="center"
      type="index">
      </el-table-column>

      <el-table-column 
      label="令号" 
      align="center"
      prop="productCode">
      </el-table-column>

      <el-table-column 
      label="机组名称" 
      align="center"
      prop="productName">
        <template slot-scope="{row}">
          <el-link type="primary" :underline="false" @click="checkGroup(row)">{{row.productName}}</el-link>
        </template>
      </el-table-column>

      <el-table-column 
      label="部套数量" 
      align="center"
      prop="">
        <el-table-column 
        label="正常" 
        align="center"
        prop="finishNum">
          <template slot-scope="{row}">
            <el-link type="primary" :underline="false" @click="checkNum(row.finishList)">{{row.finishNum}}</el-link>
          </template>
        </el-table-column>
        <el-table-column 
        label="预警" 
        align="center"
        prop="earlyAlarmNum">
          <template slot-scope="{row}">
            <el-link type="primary" :underline="false" @click="checkNum(row.earlyAlarmList)">{{row.earlyAlarmNum}}</el-link>
          </template>
        </el-table-column>
        <el-table-column 
        label="报警" 
        align="center"
        prop="alarmNum">
          <template slot-scope="{row}">
            <el-link type="primary" :underline="false" @click="checkNum(row.alarmList)">{{row.alarmNum}}</el-link>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column 
        label="模型名称" 
        align="center"
        prop="modelName">
        
      </el-table-column>
      <el-table-column 
      label="当前状态" 
      align="center"
      prop="">
        <template slot-scope="{row}">
          <p :class="row.alarmStatus | filterStatus('colorClass',statusOptions)">{{row.alarmStatus | filterStatus('label',statusOptions)}}</p>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="searchForm.pageNum"
      :limit.sync="searchForm.pageSize"
      @pagination="loadData"
    />
    
    <group-info ref="groupInfo"></group-info>
    <part-num ref="partNum" @loadData="loadData"></part-num>
  </div>
</template>

<script>
import groupInfo from './components/groupInfo'
import partNum from './components/partNum'
import { amountPage } from '@/api/expectationAnalysis/expectationQuery'
export default {
  components:{groupInfo,partNum},
  data() {
    return {
      total: 0,
      showSearch:true,
      loading:false,
      statusOptions:[
        {
          label:'正常',
          colorClass:'default',
          value:3
        },
        {
          label:'预警',
          colorClass:'warning',
          value:2
        },
        {
          label:'报警',
          colorClass:'danger',
          value:1
        }
      ],
      tableData:[],
      searchForm:{
        alarmStatus:'',
        query:'',
        pageNum:1,
        pageSize:10,
      },
    };
  },
  created() {
      this.loadData()
  },
  mounted() {
   
  },
  filters:{
    filterStatus(value,attr,statusOptions){
      if(value === null ||value === undefined || value === ''){return ''}
      let option = statusOptions.find((item)=>{
        return item.value === value
      })
      return option[attr]
    },
  },
  methods: {
    handleQuery(flag){
      if(flag){
        this.searchForm.alarmStatus = ''
        this.searchForm.query = ''
        this.searchForm.pageNum = 1
      }
      this.loadData()
    },

    // 导出
    handleExp(){

    },

    // 查看机组
    checkGroup(row){
      this.$refs.groupInfo.init(row)
    },

    // 查看数量
    checkNum(row){
      let tableData = row || []
      this.$refs.partNum.init(tableData)
    },

    loadData(){
      this.loading = true
      let params = {
        ...this.searchForm
      }
      amountPage(params).then((res)=>{
        let {records,total}  = res.data || {}
        this.tableData = records

        this.total = total
        this.loading = false
      }).catch(()=>{
        this.loading = false
      })
    },
  },
};
</script>

<style scoped>
.big-input {
  width:400px;  
}
.default {
  color:#67C23A;
}
.warning {
  color:#E6A23C;
}
.danger {
  color:#F56C6C;
}
</style>
