import request from '@/utils/request'

// 分页查询新建流程
export function processList(query) {
    return request({
        url: `/flowable/workflow/process/list`,
        method:'get',
        params:query
    });
}

// 发起
export function startProcess(query,processDefId,ortherMap) {
    return request({
        url: `/flowable/workflow/process/start/${processDefId}`,
        method:'post',
        data:query,
        params:ortherMap
    });
}

// 获取流程部署关联表单信息
export function getProcessForm(query) {
    return request({
        url: `/flowable/workflow/process/getProcessForm`,
        method:'get',
        params:query
    });
}


// 读取xml文件
export function getBpmnXml(processDefId) {
    return request({
        url: `/flowable/workflow/process/bpmnXml/${processDefId}`,
        method:'get',
    });
}

