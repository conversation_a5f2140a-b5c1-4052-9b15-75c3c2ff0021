<template>
    <el-dialog
        title="低压模块部套详情"
        :visible.sync="dialogVisible"
        :append-to-body="true"
        @close="cancel"
        width="65%"
        >
        <div class="container">
            <el-form ref="searchForm" :model="searchForm" :inline="true" label-width="50px">
                <el-form-item label="搜索">
                    <el-input v-model="searchForm.query" size="small" placeholder="部套名/部套编码"></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="small"
                    @click="handleQuery(false)"
                    >搜索</el-button
                    >
                    <el-button icon="el-icon-refresh" 
                    size="small" @click="handleQuery(true)"
                    >重置</el-button
                    >
                </el-form-item>
            </el-form>

            <div class="table-box">
                <el-table
                :data="tableData"
                style="width: 100%">
                    <el-table-column 
                    label="序号" 
                    align="center"
                    type="index">
                    </el-table-column>

                    <el-table-column 
                    label="机组类别" 
                    prop="modelName" 
                    align="center">
                        <template>
                            <div>{{moduleNameList}}</div>
                        </template>
                    </el-table-column>

                    <el-table-column
                    prop="name"
                    label="部套名称"
                    align="center"
                    width="150">
                    </el-table-column>

                    <el-table-column
                    prop="partCode"
                    label="编码规则"
                    align="center"
                    width="150">
                    </el-table-column>

                    <el-table-column
                        prop="ytMonth"
                        label="技术准备日期"
                        align="center"
                        width=""
                    >
                        <template slot-scope="{row}">
                        <div v-if="String(row.ytMonth)">{{row.ytMonth}}天</div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="bidMonth"
                        label="定标周期"
                        align="center"
                        width=""
                    >
                        <template slot-scope="{row}">
                        <div v-if="String(row.bidMonth)">{{row.bidMonth}}天</div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="produceCycleMonth"
                        label="采购周期"
                        align="center"
                        width=""
                    >
                        <template slot-scope="{row}">
                        <div v-if="String(row.produceCycleMonth)">{{row.produceCycleMonth}}天</div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="produceMonth"
                        label="生产周期"
                        align="center"
                        width=""
                    >
                        <template slot-scope="{row}">
                        <div v-if="String(row.produceMonth)">{{row.produceMonth}}天</div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </el-dialog>
</template>

<script>

export default {
  components:{},

  data(){
    return{
        dialogVisible:false,
        moduleNameList:'',
        searchForm:{
            query:''
        },
        partList:[],
        tableData:[]
    }
  },

  created(){},

  methods:{
    init(row,moduleNameList){
        this.moduleNameList = moduleNameList
        this.partList = row.partList || []
        this.tableData = row.partList || []
        this.dialogVisible = true
    },
    handleQuery(flag){
        if(flag){
            this.searchForm.query = ''
        }
        let query = this.searchForm.query
        this.tableData = this.partList.filter((item)=>{
            return item.name.includes(query) || item.partCode.includes(query)
        })
    },
    cancel(){
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped>
.select-txt {
    margin-left:10px;
}
</style>