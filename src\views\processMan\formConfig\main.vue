<template>
    <div>
      <form-config-main ref="formConfiMain" v-if="showFormConfig"></form-config-main>
      <tool ref="tool" v-else></tool>
    </div>
</template>

<script>
import formConfigMain from './components/formConfigMain'
// import tool from '@/components/modules/tool/build/index'
import tool from '@/views/tool/build/index'
export default {
components: { formConfigMain,tool },
  data() {
    return {
      showFormConfig:true
    }
  },
  methods:{

  },
  created() {

  },
};
</script>

<style lang="less" scoped>
</style>
