<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="机组" prop="posid" label-width="40px">
        <el-select
          v-model="queryParams.posid"
          filterable
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(dict, index) in engineeringList"
            :key="index"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="模块" prop="moduleCode" label-width="40px">
        <el-select
          v-model="queryParams.moduleCode"
          filterable
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(dict, index) in moduleList"
            :key="index"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status" label-width="40px">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable>
          <el-option
            v-for="dict in status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="负责部门" prop="arrange" label-width="80px">
        <el-select v-model="queryParams.arrange" placeholder="请选择" clearable>
          <el-option
            v-for="dict in arrangeList"
            :key="dict.value"
            :label="dict.text"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="el-icon-upload2"
          size="mini"
          plain
          @click="handleImp"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" />
      <el-table-column label="部套名称" prop="dwgName" />
      <el-table-column label="部套编码" prop="dwgNo" />
      <el-table-column label="令号" prop="posid" />
      <el-table-column label="项目名称" prop="post1">
        <template slot-scope="scope">
          <a @click="item(scope.row)" style="color: #1890ff; cursor: pointer">{{
            scope.row.post1
          }}</a>
        </template>
      </el-table-column>
      <el-table-column label="项目经理" prop="zfstVernr" />
      <el-table-column label="项目交期" prop="zps0079" />
      <el-table-column label="进展情况" prop="zprogress" />
      <el-table-column label="状态说明" prop="stRemark" />
      <el-table-column label="状态" prop="alarmStatus">
        <template slot-scope="scope">
          <span
            style="
              font-size: 12px;
              display: inline-block;
              width: 44px;
              height: 28px;
              line-height: 28px;
              border-radius: 3px;
              text-align: center;
            "
            :class="
              scope.row.alarmStatus == 1
                ? 'bao'
                : scope.row.alarmStatus == 2
                ? 'yu'
                : scope.row.alarmStatus == 3
                ? 'jin'
                : 'finish'
            "
            >{{
              scope.row.alarmStatus == 1
                ? "报警"
                : scope.row.alarmStatus == 2
                ? "预警"
                : scope.row.alarmStatus == 3
                ? "正常"
                : "完成"
            }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleUpdate(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 部套详情 -->
    <el-dialog
      title="部套详情"
      :visible.sync="open"
      width="660px"
      append-to-body
    >
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >部套名称：<span>{{ dwgList.dwgName }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >部套编码：<span>{{ dwgList.dwgNo }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >分工：<span>{{ dwgList.arrange }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >项目经理：<span>{{ dwgList.zfstVernr }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >合同交期：<span>{{ dwgList.zps0010 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >MM：<span>{{ dwgList.moduleGrp }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >上次排期：<span>{{ dwgList.zps0079Up }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >项目交期：<span>{{ dwgList.zps0079 }}</span></span
            >
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >进展情况：<span>{{ dwgList.zprogress }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >预计完成时间：<span>{{ dwgList.expDat }}</span></span
            >
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >未完原因：<span>{{ dwgList.unReason }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >状态：
              <span
                style="font-size: 14px"
                :class="
                  dwgList.alarmStatus == 1
                    ? 'bao1'
                    : dwgList.alarmStatus == 2
                    ? 'yu1'
                    : dwgList.alarmStatus == 3
                    ? 'jin1'
                    : 'finish1'
                "
                >{{
                  dwgList.alarmStatus == 1
                    ? "报警"
                    : dwgList.alarmStatus == 2
                    ? "预警"
                    : dwgList.alarmStatus == 3
                    ? "正常"
                    : "完成"
                }}</span
              ></span
            >
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="24"
          ><div class="grid-content bg-purple">
            <span
              >状态说明：<span>{{ dwgList.stRemark }}</span></span
            >
          </div></el-col
        >
        <!-- <el-col :span="12"><div class="grid-content bg-purple-light"><span>状态：<span>未完成</span></span></div></el-col> -->
      </el-row>
      <el-divider> </el-divider>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >机组名称：<span>{{ dwgList.post1 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >令号：<span>{{ dwgList.posid }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >容量：<span>8{{ dwgList.usr04 }}MW</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >产品号：<span>{{ dwgList.projDl }}</span></span
            >
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >定类：<span>{{ dwgList.projDl }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >模块名称：<span>{{ dwgList.moduleName }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >板块：<span>{{ dwgList.boardName }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >模块编码：<span>{{ dwgList.moduleCode }}</span></span
            >
          </div></el-col
        >
      </el-row>
    </el-dialog>

    <!-- 项目名称详情弹框 -->
    <el-dialog
      title="项目详情"
      :visible.sync="itemOpen"
      width="600px"
      append-to-body
    >
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >机组名称：<span>{{ posidList.post1 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >令号：<span>{{ posidList.posid }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >产品类型：<span>{{ posidList.prodType }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >机组容量：<span>{{ posidList.usr04 }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >合同签订日期：<span>{{ posidList.zps0177 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >定类：<span>{{ posidList.opowerPl }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >所属电厂：<span>{{ posidList.opowerPl }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >板块名称：<span>{{ posidList.boardName }}</span></span
            >
          </div></el-col
        >
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDwgListForWeb,
  getEngineeringList,
  getModuleList,
  getArrangeList,
  getPost,
  delPost,
  addPost,
  updatePost,
  exportData,
} from "@/api/items/items";

export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 项目名称是否显示弹出层
      itemOpen: false,
      status: [
        { label: "报警", value: 1 },
        { label: "预警", value: 2 },
        { label: "进行中", value: 3 },
        { label: "已完成", value: 4 },
      ],
      dataForm: {
        keyword: undefined,
      },
      dataList: {
        keyword: undefined,
        posid: undefined,
      },

      //机组详情
      posidList: {},
      //部套详情
      dwgList: {},
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        posid: undefined, //令号
        moduleCode: undefined, //模块编号
        status: undefined, //部套状态
        keyword: undefined, //关键字
        arrange: undefined, // 部门
      },
      //机组列表
      engineeringList: [],
      //模块列表
      moduleList: [],
      // 部门列表
      arrangeList: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" },
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    if (localStorage.getItem("posid") || localStorage.getItem("moduleCode")) {
      this.queryParams.posid = localStorage.getItem("posid");
      this.queryParams.moduleCode = localStorage.getItem("moduleCode");
    }

    this.getEngineeringList();
    this.getModuleList(), this.getList();
    this.getArrangeList();
    localStorage.removeItem("posid");
    localStorage.removeItem("moduleCode");
  },
  mounted() {
    this.dataForm.keyword = this.queryParams.posid;
    this.dataList.posid = this.dataForm.keyword;
  },
  methods: {
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getDwgListForWeb(this.queryParams).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    //机组列表
    getEngineeringList() {
      getEngineeringList(this.dataForm).then((res) => {
        res.data.forEach((element, index) => {
          this.engineeringList.push({
            value: element.posid,
            label: element.post1 + "(" + element.posid + ")",
          });
        });
      });
    },
    //模块列表
    getModuleList() {
      getModuleList(this.dataList).then((res) => {
        res.data.forEach((element, index) => {
          this.moduleList.push({
            value: element.moduleCode,
            label: element.moduleName + "(" + element.moduleCode + ")",
          });
        });
      });
    },
    // 部门列表
    getArrangeList() {
      getArrangeList().then((res) => {
        this.arrangeList = res.data;
      });
    },
    // 导出
    handleImp() {
      const _this = this;
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          ids: this.ids,
          ...this.queryParams,
        };
        exportData(params).then((res) => {
          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement("a");
          link.target = "_blank";
          link.href = url;
          link.setAttribute("download", "部套数据.xlsx");
          document.body.appendChild(link);
          link.click();
        });
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.posid = undefined;
      this.queryParams.moduleCode = undefined;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加岗位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.dwgList = row;
      this.open = true;
      this.title = "部套详情";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    // 项目名称弹出框
    item(row) {
      this.posidList = row;
      this.itemOpen = true;
    },
  },
};
</script>

<style scoped>
::v-deep .el-dialog__body {
  line-height: 30px;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}
.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
.yu1 {
  color: #fac116;
}
.bao1 {
  color: #e47985;
}
.jin1 {
  color: #389aff;
}
.finish1 {
  color: #65d299;
}
::v-deep .el-row--flex {
  margin-left: 22px;
}
::v-deep .el-dialog {
  margin-top: 30vh !important;
}
</style>
