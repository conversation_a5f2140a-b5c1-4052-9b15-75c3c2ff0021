<template>
    <el-dialog :title="rowData ? '修改' : '新增'" :visible.sync="dialogVisible" width="50%">
        <div class="dialog_con" v-loading="loading">
            <el-form :model="form" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm">
                <el-row>
                    <el-col :span="12" v-if="isAdd || rowData.parentId !== '0'">
                        <el-form-item label="上级组织" prop="parentId">
                            <el-cascader clearable v-model="form.parentId" size="small" class="format_option"
                                placeholder="上级组织" :options="treeOptions" :props="defaultProps" @change="handleChange" filterable>
                            </el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="编码" prop="no">
                            <el-input class="format_option" placeholder="编码" size="small" v-model="form.no"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="名称" prop="name">
                            <el-input class="format_option" placeholder="名称" size="small" v-model="form.name"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="负责人">
                            <!-- <el-select class="format_option" placeholder="负责人" size="small"  v-model="form.principalUserId" clearable>
                                <el-option
                                v-for="item in userOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                                >
                                </el-option>
                            </el-select> -->
                            <select-personnel v-model="form.principalUser" size="small" :defaultProps="personnelProps"
                                :highlightCurrentRow="true" :placeholder="'直属领导'" :config="config" @change="changePrincipalUser"></select-personnel>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12" v-if="!isAdd">
                        <el-form-item label="创建人">
                            <el-input class="format_option" placeholder="创建人" :disabled="!isAdd" size="small"
                                v-model="form.creator" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="!isAdd">
                        <el-form-item label="状态">
                            <el-select class="format_option" placeholder="状态" size="small" v-model="form.status" clearable>
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="排序">
                            <el-input class="format_option" placeholder="排序" size="small" v-model="form.indexSort"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>

                    <!-- <el-col :span="12" class="defin_el_col">
                        <el-form-item label="图标">
                            <upload-icon v-model="form.icon">
                                <i class="icon el-icon-plus"></i>
                            </upload-icon>
                        </el-form-item>
                    </el-col> -->

                    <el-col :span="12" v-if="!isAdd">
                        <el-form-item label="创建时间">
                            <el-input class="format_option" placeholder="创建时间" :disabled="true" size="small"
                                v-model="form.createTime" clearable></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12" v-if="!isAdd">
                        <el-form-item label="更新时间">
                            <el-input class="format_option" placeholder="更新时间" :disabled="true" size="small"
                                v-model="form.updateTime" clearable></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="备注">
                            <el-input class="format_option" type="textarea" :rows="4" resize="none" clearable
                                placeholder="备注" v-model="form.remark">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button size="small" @click="dialogVisible = false">取 消</el-button>
            <el-button size="small" type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        </span>
    </el-dialog>
</template>
<script>
import { deptTree, addDept, editDept } from '@/api/organizationChart/organizationalMan.js'
import { allUserList } from '@/api/organizationChart/userMan.js'
import { dateTimeFormat } from '@/utils/gloabUtileExport.js'
import selectPersonnel from '@/components/modules/selectPersonnel/main.vue'
// import uploadIcon from '@/components/modules/uploadIcon/uploadIcon.vue'
export default {
    components: { selectPersonnel },
    data() {
        return {
            loading: false,
            isAdd: false,    // 是否为新增
            btnLoading: false,
            dialogVisible: false,
            rowData: {},
            treeOptions: [], // 上级组织
            userOptions: [], // 负责人
            defaultProps: {
                children: 'children',
                label: 'name',
                value: 'id',
                emitPath: false,
                checkStrictly: true,
                expandTrigger: 'hover'
            },
            personnelProps: {
                label: 'name',
                value: 'id',
                valLabel:'principalName',
                valKey:'principalUserId'
            },
            options: [
                {
                    value: 0,
                    label: '禁用',
                },
                {
                    value: 1,
                    label: '正常'
                }
            ],
            form: {
                icon: '',
                parentId: '',
                name: '',    // 名称
                no: '',
                sortIndex: '',
                remark: '',
                principalUser: {}
            },
            config:{
                method:allUserList,
                tableColumn:[
                    {
                        prop:'username',
                        label:'账号',
                        width:''
                    },
                    {
                        prop:'name',
                        label:'姓名',
                        width:''
                    },
                    {
                        prop:'octetCode',
                        label:'员工编号',
                        width:''
                    },
                    {
                        prop:'phone',
                        label:'手机号',
                        width:'120'
                    },
                    // {
                    //     prop:'leaderName',
                    //     label:'直属领导',
                    //     width:'80'
                    // },
                    {
                        prop:'deptName',
                        label:'所属机构',
                        width:''
                    },
                    {
                        prop:'enable',
                        label:'状态',
                        tabStatus:true,
                        dicVal:'peopleStatus',
                        options:[
                            {
                                key:0,
                                label:'danger',
                            },
                            {
                                key:1,
                                label:'success',
                            },
                        ],
                        width:''
                    }
                ]
            },

            rules: {
                parentId: [
                    { required: true, message: '请输入上级组织', trigger: 'blur' }
                ],
                name: [
                    { required: true, message: '请输入名称', trigger: 'blur' }
                ],
                no: [
                    { required: true, message: '请输入编码', trigger: 'blur' }
                ]
            }
        }
    },
    methods: {
        async init(row) {
            this.dialogVisible = true
            await this.initList()
            this.rowData = row
            this.isAdd = row ? false : true
            this.$nextTick(() => {
                if (row) {
                    this.form = JSON.parse(JSON.stringify(row))
                    this.form.parentId = String(row.parentId)
                    this.form.createTime = dateTimeFormat(this.form.createTime)
                    this.form.updateTime = dateTimeFormat(this.form.updateTime)
                    this.form.principalUser = {principalUserId:row.principalUserId,principalName:row.principalName}
                } else {
                    this.form = {
                        icon: '',
                        parentId: '',
                        name: '',    // 名称
                        no: '',
                        sortIndex: '',
                        remark: '',
                        principalUser:{}
                    }
                }
                this.$refs['form'].resetFields();
            })

        },

        changePrincipalUser(principalUser){
            this.form.principalUserId = principalUser.id
            this.form.principalName = principalUser.name
        },

        // 获取上级组织
        async deptTree() {
            await deptTree().then((res) => {
                this.treeOptions = res.data
            }).catch(() => {
                this.loading = false
            })
        },
        // 获取负责人
        allUserList() {
            allUserList().then((res) => {
                this.userOptions = res.data
            }).catch(() => {
                this.loading = false
            })
        },
        async initList() {
            this.loading = true
            await this.deptTree()
            this.loading = false
        },
        confirm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    this.btnLoading = true
                    if (this.isAdd) {
                        let params = {
                            ...this.form
                        }
                        delete params.principalUser
                        addDept(params).then((res) => {
                            this.$message({
                                type: 'success',
                                message: '操作成功',
                                duration: 1500
                            })
                            this.btnLoading = false
                            this.dialogVisible = false
                            this.$emit('hideDialog', 'addDepartment', this.rowData)
                        }).catch(() => {
                            this.btnLoading = false
                        })
                    } else {
                        this.form.parentId = this.rowData.id === '1' ? '0' : this.form.parentId
                        let params = {
                            ...this.form
                        }
                        editDept(params).then((res) => {
                            this.$message({
                                type: 'success',
                                message: '操作成功',
                                duration: 1500
                            })
                            this.btnLoading = false
                            this.dialogVisible = false
                            this.$emit('hideDialog', 'addDepartment', this.rowData)
                        }).catch(() => {
                            this.btnLoading = false
                        })
                    }
                }
            });
        },
        handleChange(id) {
            this.form.parentId = id
        }
    }
}
</script>
<style lang="less" scoped>
.demo-ruleForm {
    .status_row {
        width: 100%;
    }
}

.dialog-footer {
    display: block;
}
</style>
