/* 改变主题色变量 */
/* 改变 icon 字体路径变量，必需 */
@import "~bpmn-js-token-simulation/assets/css/bpmn-js-token-simulation.css";
@import "~bpmn-js-token-simulation/assets/css/font-awesome.min.css";
@import "~bpmn-js-token-simulation/assets/css/normalize.css";
@import "~bpmn-js/dist/assets/diagram-js.css";
@import "~bpmn-js/dist/assets/bpmn-font/css/bpmn.css";
@import "~bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css";
.process-drawer .el-drawer__header {
  padding: 16px 16px 8px 16px;
  margin: 0;
  line-height: 24px;
  font-size: 18px;
  color: #303133;
  box-sizing: border-box;
  border-bottom: 1px solid #e8e8e8;
}
div[class^="el-drawer"]:focus,
span:focus {
  outline: none;
}
.process-drawer .el-drawer__body {
  box-sizing: border-box;
  padding: 16px;
  width: 100%;
  overflow-y: auto;
}
.process-design .el-table td,
.process-design .el-table th {
  color: #333;
}
.process-design .el-dialog__header {
  padding: 16px 16px 8px 16px;
  box-sizing: border-box;
  border-bottom: 1px solid #e8e8e8;
}
.process-design .el-dialog__body {
  padding: 16px;
  max-height: 80vh;
  box-sizing: border-box;
  overflow-y: auto;
}
.process-design .el-dialog__footer {
  padding: 16px;
  box-sizing: border-box;
  border-top: 1px solid #e8e8e8;
}
.process-design .el-dialog__close {
  font-weight: 600;
}
.process-design .el-select {
  width: 100%;
}
.process-design .el-divider:not(.el-divider--horizontal) {
  margin: 0 8px ;
}
.process-design .el-divider.el-divider--horizontal {
  margin: 16px 0;
}
.djs-palette {
  background: var(--palette-background-color);
  border: solid 1px var(--palette-border-color) !important;
  border-radius: 2px;
}
.my-process-designer {
  padding: 5px 0 10px 10px;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.my-process-designer .my-process-designer__header {
  width: 100%;
  min-height: 36px;
}
.my-process-designer .my-process-designer__header .el-button {
  text-align: center;
}
.my-process-designer .my-process-designer__header .el-button-group {
  margin: 4px;
}
.my-process-designer .my-process-designer__header .el-tooltip__popper .el-button {
  width: 100%;
  text-align: left;
  padding-left: 8px;
  padding-right: 8px;
}
.my-process-designer .my-process-designer__header .el-tooltip__popper .el-button:hover {
  background: rgba(64, 158, 255, 0.8);
  color: #ffffff;
}
.my-process-designer .my-process-designer__header .align {
  position: relative;
}
.my-process-designer .my-process-designer__header .align i:after {
  content: "|";
  position: absolute;
  transform: rotate(90deg) translate(200%, 60%);
}
.my-process-designer .my-process-designer__header .align.align-left i {
  transform: rotate(90deg);
}
.my-process-designer .my-process-designer__header .align.align-right i {
  transform: rotate(-90deg);
}
.my-process-designer .my-process-designer__header .align.align-top i {
  transform: rotate(180deg);
}
.my-process-designer .my-process-designer__header .align.align-bottom i {
  transform: rotate(0deg);
}
.my-process-designer .my-process-designer__header .align.align-center i {
  transform: rotate(90deg);
}
.my-process-designer .my-process-designer__header .align.align-center i:after {
  transform: rotate(90deg) translate(0, 60%);
}
.my-process-designer .my-process-designer__header .align.align-middle i {
  transform: rotate(0deg);
}
.my-process-designer .my-process-designer__header .align.align-middle i:after {
  transform: rotate(90deg) translate(0, 60%);
}
.my-process-designer .my-process-designer__container {
  display: inline-flex;
  width: 100%;
  flex: 1;
}
.my-process-designer .my-process-designer__container .my-process-designer__canvas {
  flex: 1;
  height: 100%;
  position: relative;
  background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+") repeat !important;
}
.my-process-designer .my-process-designer__container .my-process-designer__canvas div.toggle-mode {
  display: none;
}
.my-process-designer .my-process-designer__container .my-process-designer__property-panel {
  height: 100%;
  overflow: scroll;
  overflow-y: auto;
  z-index: 10;
}
.my-process-designer .my-process-designer__container .my-process-designer__property-panel * {
  box-sizing: border-box;
}
.my-process-designer .my-process-designer__container svg {
  width: 100%;
  height: 100%;
  min-height: 100%;
  overflow: hidden;
}
.djs-palette.open .djs-palette-entries div[class^="bpmn-icon-"]:before,
.djs-palette.open .djs-palette-entries div[class*="bpmn-icon-"]:before {
  line-height: unset;
}
.djs-palette.open .djs-palette-entries div.entry {
  position: relative;
}
.djs-palette.open .djs-palette-entries div.entry:hover::after {
  width: max-content;
  content: attr(title);
  vertical-align: text-bottom;
  position: absolute;
  right: -10px;
  top: 0;
  bottom: 0;
  overflow: hidden;
  transform: translateX(100%);
  font-size: 0.5em;
  display: inline-block;
  text-decoration: inherit;
  font-variant: normal;
  text-transform: none;
  background: #fafafa;
  box-shadow: 0 0 6px #eeeeee;
  border: 1px solid #cccccc;
  box-sizing: border-box;
  padding: 0 16px;
  border-radius: 4px;
  z-index: 100;
}
pre {
  margin: 0;
  height: 100%;
  overflow: hidden;
  max-height: calc(80vh - 32px);
  overflow-y: auto;
}
.hljs {
  word-break: break-word;
  white-space: pre-wrap;
}
.hljs * {
  font-family: Consolas, Monaco, monospace;
}
.process-design .process-panel__container {
  box-sizing: border-box;
  padding: 0 8px;
  border-left: 1px solid #eeeeee;
  box-shadow: 0 0 8px #cccccc;
  max-height: 100%;
  overflow-y: scroll;
}
.process-design .panel-tab__title {
  font-weight: 600;
  padding: 0 8px;
  font-size: 1.1em;
  line-height: 1.2em;
}
.process-design .panel-tab__title i {
  margin-right: 8px;
  font-size: 1.2em;
}
.process-design .panel-tab__content {
  width: 100%;
  box-sizing: border-box;
  border-top: 1px solid #eeeeee;
  padding: 8px 16px;
}
.process-design .panel-tab__content .panel-tab__content--title {
  display: flex;
  justify-content: space-between;
  padding-bottom: 8px;
}
.process-design .panel-tab__content .panel-tab__content--title span {
  flex: 1;
  text-align: left;
}
.process-design .element-property {
  width: 100%;
  display: flex;
  align-items: flex-start;
  margin: 8px 0;
}
.process-design .element-property .element-property__label {
  display: block;
  width: 90px;
  text-align: right;
  overflow: hidden;
  padding-right: 12px;
  line-height: 32px;
  font-size: 14px;
  box-sizing: border-box;
}
.process-design .element-property .element-property__value {
  flex: 1;
  line-height: 32px;
}
.process-design .element-property .el-form-item {
  width: 100%;
  margin-bottom: 0;
  padding-bottom: 18px;
}
.process-design .list-property {
  flex-direction: column;
}
.process-design .list-property .element-listener-item {
  width: 100%;
  display: inline-grid;
  grid-template-columns: 16px auto 32px 32px;
  grid-column-gap: 8px;
}
.process-design .list-property .element-listener-item + .element-listener-item {
  margin-top: 8px;
}
.process-design .listener-filed__title {
  display: inline-flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  margin-top: 0;
}
.process-design .listener-filed__title span {
  width: 200px;
  text-align: left;
  font-size: 14px;
}
.process-design .listener-filed__title i {
  margin-right: 8px;
}
.process-design .element-drawer__button {
  margin-top: 8px;
  width: 100%;
  display: inline-flex;
  justify-content: space-around;
}
.process-design .element-drawer__button > .el-button {
  width: 100%;
}
.process-design .el-collapse-item__content {
  padding-bottom: 0;
}
.process-design .el-input.is-disabled .el-input__inner {
  color: #999999;
}
.process-design .el-form-item.el-form-item--mini {
  margin-bottom: 0;
}
.process-design .el-form-item.el-form-item--mini + .el-form-item {
  margin-top: 16px;
}
.process-viewer {
  position: relative;
  border: 1px solid #EFEFEF;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+') repeat !important;
}
.process-viewer .success-arrow {
  fill: #4eb819;
  stroke: #4eb819;
}
.process-viewer .success-conditional {
  fill: white;
  stroke: #4eb819;
}
.process-viewer .fail-arrow {
  fill: #E6A23C;
  stroke: #E6A23C;
}
.process-viewer .fail-conditional {
  fill: white;
  stroke: #E6A23C;
}
.process-viewer .success.djs-connection .djs-visual path {
  stroke: #4eb819 !important;
  marker-end: url(#sequenceflow-end-white-success) !important;
}
.process-viewer .success.djs-connection.condition-expression .djs-visual path {
  marker-start: url(#conditional-flow-marker-white-success) !important;
}
.process-viewer .success.djs-shape .djs-visual rect {
  stroke: #4eb819 !important;
  fill: #4eb819 !important;
  fill-opacity: 0.15 !important;
}
.process-viewer .success.djs-shape .djs-visual polygon {
  stroke: #4eb819 !important;
}
.process-viewer .success.djs-shape .djs-visual path:nth-child(2) {
  stroke: #4eb819 !important;
  fill: #4eb819 !important;
}
.process-viewer .success.djs-shape .djs-visual circle {
  stroke: #4eb819 !important;
  fill: #4eb819 !important;
  fill-opacity: 0.15 !important;
}
.process-viewer .primary.djs-shape .djs-visual rect {
  stroke: #409EFF !important;
  fill: #409EFF !important;
  fill-opacity: 0.15 !important;
}
.process-viewer .primary.djs-shape .djs-visual polygon {
  stroke: #409EFF !important;
}
.process-viewer .primary.djs-shape .djs-visual circle {
  stroke: #409EFF !important;
  fill: #409EFF !important;
  fill-opacity: 0.15 !important;
}
.process-viewer .warning.djs-connection .djs-visual path {
  stroke: #E6A23C !important;
  marker-end: url(#sequenceflow-end-white-fail) !important;
}
.process-viewer .warning.djs-connection.condition-expression .djs-visual path {
  marker-start: url(#conditional-flow-marker-white-fail) !important;
}
.process-viewer .warning.djs-shape .djs-visual rect {
  stroke: #E6A23C !important;
  fill: #E6A23C !important;
  fill-opacity: 0.15 !important;
}
.process-viewer .warning.djs-shape .djs-visual polygon {
  stroke: #E6A23C !important;
}
.process-viewer .warning.djs-shape .djs-visual path:nth-child(2) {
  stroke: #E6A23C !important;
  fill: #E6A23C !important;
}
.process-viewer .warning.djs-shape .djs-visual circle {
  stroke: #E6A23C !important;
  fill: #E6A23C !important;
  fill-opacity: 0.15 !important;
}
.process-viewer .danger.djs-shape .djs-visual rect {
  stroke: #F56C6C !important;
  fill: #F56C6C !important;
  fill-opacity: 0.15 !important;
}
.process-viewer .danger.djs-shape .djs-visual polygon {
  stroke: #F56C6C !important;
}
.process-viewer .danger.djs-shape .djs-visual circle {
  stroke: #F56C6C !important;
  fill: #F56C6C !important;
  fill-opacity: 0.15 !important;
}
.process-viewer .cancel.djs-shape .djs-visual rect {
  stroke: #909399 !important;
  fill: #909399 !important;
  fill-opacity: 0.15 !important;
}
.process-viewer .cancel.djs-shape .djs-visual polygon {
  stroke: #909399 !important;
}
.process-viewer .cancel.djs-shape .djs-visual circle {
  stroke: #909399 !important;
  fill: #909399 !important;
  fill-opacity: 0.15 !important;
}
.process-viewer .djs-tooltip-container,
.process-viewer .djs-overlay-container,
.process-viewer .djs-palette {
  display: none;
}
