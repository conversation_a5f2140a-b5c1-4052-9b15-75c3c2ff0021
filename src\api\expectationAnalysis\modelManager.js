import request from '@/utils/request'
// 新增
export function qtModel(params) {
    return request({
        url: '/mes-server/qt/model',
        method: 'post',
        data: params,
    })
}

// 列表查询
export function modelPage(params) {
    return request({
        url: '/mes-server/qt/model/page',
        method: 'get',
        params: params,
    })
}

// 列表查询
export function codeList(query) {
    return request({
        url: `/mes-server/qt/model/code/${id}/list`,
        method: 'get',
        params: query
    })
}


// 删除
export function delModel(id) {
    return request({
        url: `/mes-server/qt/model/${id}`,
        method: 'delete',
    })
}

// 关联令号时查询机组名称
export function tqListName(query) {
    return request({
        url: `/user-server/engineeringUnit/tq/list/name`,
        method: 'post',
        data: query
    })
}

// 关联令号
export function modelCode(query, id) {
    return request({
        url: `/mes-server/qt/model/${id}/code`,
        method: 'post',
        data: query
    })
}

