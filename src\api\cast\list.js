import request from '@/utils/request'
// 分页查询列表
export function findAllPage(param) {
    return request({
        url: '/back-server/castingPurchase/findAllPage',
        method: 'get',
        params: param,
    })
}
// 模块下拉
export function findAllModuleList(param) {
    return request({
        url: '/back-server/castingPurchase/findAllModuleList',
        method: 'get',
        params: param,
    })
}
export function findAllDwgList(param) {
    return request({
        url: '/back-server/castingPurchase/findAllDwgList',
        method: 'get',
        params: param,
    })
}
// 机组下拉
export function findAllUnitList() {
    return request({
        url: '/back-server/castingPurchase/findAllUnitList',
        method: 'get',
    })
}

// 手工标记完成
export function updateStatus(param) {
  return request({
    url: '/back-server/castingPurchase/updateStatus',
    method: 'get',
    params: param,
  })
}
// 导出铸锻件数据
export function exportList(param) {
  return request({
    url: '/back-server/castingPurchase/list/export',
    method: 'get',
    params: param,
  })
}
// 批量手工标记完成
export function updateStatusBatch(param) {
  return request({
    url: `/back-server/castingPurchase/updateStatusBatch?castingIds=${param}`,
    method: 'get'
  })
}
