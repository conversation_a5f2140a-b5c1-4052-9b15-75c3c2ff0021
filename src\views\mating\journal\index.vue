<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="40px">
      <el-form-item label="搜索" prop="keyword">
        <el-input v-model="queryParams.keyword" placeholder="机组/令号" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:post:edit']"
        >修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDeletes">删除
        </el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:post:export']"
        >导出</el-button>
      </el-col>  -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <!-- <el-table-column label="序号" prop="postId" /> -->
      <el-table-column label="令号" prop="posid" />
      <el-table-column label="机组名称" prop="post1">
        <template slot-scope="scope">
          <a @click="item(scope.row)" style="color: rgb(24, 144, 255); cursor: pointer">{{
              scope.row.post1
          }}</a>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createUserName" />
      <el-table-column label="创建时间" prop="createTime" />
      <el-table-column label="提交内容" prop="remarks" />
      <el-table-column label="图片" prop="imageList">
        <template slot-scope="scope">
          <a @click="itemImg(scope.row.imageList)" style="color: rgb(24, 144, 255); cursor: pointer">{{
              scope.row.imageList.length
          }}</a>
        </template>
      </el-table-column>

      <el-table-column label="语音" prop="postSort" />
      <!-- <el-table-column label="铸锻件成套部套数" align="center" prop="postSort" /> -->
      <!-- <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.size"
      @pagination="getList" />

    <!-- 添加或修改岗位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="机组名称">
              <el-select ref="flowere" v-model="form.post1" placeholder="请选择" filterable clearable @change="postChange">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>


          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="令号" prop="posid">
              <el-input v-model="form.posid" placeholder="请输入" />
            </el-form-item>
          </el-col> -->
        </el-row>
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label="创建人" prop="createUserName">
              <el-input v-model="form.createUserName" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker v-model="form.createTime" type="datetime" placeholder="选择日期时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="24">
            <el-form-item class="remarks" label="提交内容" prop="remarks">
              <el-input v-model="form.remarks" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="图片" prop="postName">
              <el-upload action="#" :on-remove="handleRemove" :http-request="onExportPhoto" :file-list="pictureList"
                list-type="picture-card" accept=".jpeg,.png,.jpg,.bmp,.gif" :limit="9" multiple>
                <i slot="default" class="el-icon-plus icon_pic"></i>
                <div style="margin-top: -15px">{{ this.imgsArr.length }}/9</div>
              </el-upload>
              <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="" />
              </el-dialog>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 项目名称详情弹框 -->
    <el-dialog title="项目详情" :visible.sync="itemOpen" width="600px" append-to-body>
      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>机组名称：<span>榆能杨伙盘1#机组项目</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>令号：<span>118076</span></span>
          </div>
        </el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>产品类型：<span>汽轮机</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>机组容量：<span>66</span></span>
          </div>
        </el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>合同签订日期：<span>2022-05-30</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>定类：<span>A</span></span>
          </div>
        </el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <span>所属电厂：<span>汕头华电发电有限公司</span></span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple-light">
            <span>板块名称：<span>直发现场部套</span></span>
          </div>
        </el-col>
      </el-row>
    </el-dialog>
    <!-- 任务图片弹窗 -->
    <el-dialog title="图片" :visible.sync="taskPicture" width="37%">
      <el-row :gutter="20">
        <el-col v-for="(item, index) in imgs" :key="index" :span="8"><img class="img" :src="(process.env.VUE_APP_IMAGE_API+item.newName)" alt="" /></el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="taskPicture = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  findAllPage,
  unitLogAdd,
  unitLogDeleteLog,
  unitLogDeleteLogs,
  unitLogGetLogByPosid,
  unitLogUpdateLog,
  findAllUnits,//获取机组列表
} from "@/api/mating/journal";
import { uploadImagesFiles } from "@/api/alarmList"
export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      taskPicture:false,
      imgsArr: [],
      imgs:[],
      dialogImageUrl: "",
      dialogVisible: false,
      pictureList: [],
      imageUrl: "",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 项目名称
      itemOpen: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        keyword: "",
      },
      // 表单参数
      form: {
        imageList: [],
      },
      // 新增选择机组数据
      options: [],
      // 表单校验
      rules: {
        // postName: [
        //   { required: true, message: "岗位名称不能为空", trigger: "blur" },
        // ],
        // postCode: [
        //   { required: true, message: "岗位编码不能为空", trigger: "blur" },
        // ],
        // postSort: [
        //   { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        // ],
      },
    };
  },
  created() {
    this.getList();
    this.getfindAllUnits()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      findAllPage(this.queryParams).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        posid: undefined, //令号
        post1: undefined, //项目名称
        createUserName: undefined, //提交人
        createTime: 0, //创建时间
        imageList: [], //图片列表
        remark: undefined, //提交内容
      };
      this.pictureList = []
      this.imgsArr = []
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = JSON.parse(JSON.stringify(row));
      this.open = true;
      this.title = "修改";

    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            unitLogUpdateLog(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          }
          else {
            if (this.imgsArr.length > 0) {
              let formDataImgs = new FormData();
              for (var i = 0; i < this.imgsArr.length; i++) {
                formDataImgs.append("multipartFiles", this.imgsArr[i]); //文件流
              }
              let images = await uploadImagesFiles(formDataImgs);
              this.form.imageList.push(...images.data);
            }
            unitLogAdd(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.imgsArr = []
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const logId = row.id;
      this.$modal
        .confirm('是否确认删除编号为"' + logId + '"的数据项？')
        .then(function () {
          return unitLogDeleteLog(logId);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 批量删除按钮操作 */
    handleDeletes() {
      const logId = this.ids;

      this.$modal
        .confirm('是否确认删除编号为"' + logId + '"的数据项？')
        .then(function () {
          return unitLogDeleteLogs(logId);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    // 项目名称
    item(row) {
      this.itemOpen = true;
      unitLogGetLogByPosid().then(res => {

      })
    },
    // 图片弹窗
    itemImg(item) {
      this.taskPicture = true;
      this.imgs = item;
    },

    //删除图片
    handleRemove(file) {
      // console.log(file);
      if (!file.raw) {
        for (var i = 0; i < this.form.imageList.length; i++) {
          if (this.form.imageList[i] == file.name) {
            this.form.imageList.splice(i, 1);
          }
        }
      } else {
        for (var i = 0; i < this.imgsArr.length; i++) {
          if (this.imgsArr[i] == file.raw) {
            this.imgsArr.splice(i, 1);
          }
        }
      }
    },
    // 获取机组列表
    getfindAllUnits() {
      findAllUnits().then(res => {
        res.data.forEach(element => {
          this.options.push({
            label: element.post1,
            value: element.posid
          })
        });
      })
    },
    // 下拉选择确认值
    postChange(val) {
      this.form.posid = val;
      // 注意！！！！：一定要是在nextTick数据更新后才会有值
      this.$nextTick(() => {
        this.form.post1 = this.$refs["flowere"].selected.label;
      });
    },
    //图片上传
    onExportPhoto(file) {
      let fileObj = file.file;
      this.imgsArr.push(fileObj);
    },
  },
};
</script>
<style scoped>
::v-deep .el-dialog__body {
  line-height: 30px;
}

::v-deep .el-form-item--medium .el-form-item__content {
  width: 58% !important;
}

::v-deep .remarks .el-form-item__content {
  width: 79% !important;
}

.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.img {
  width: 100%;
  height: 208px;
}
::v-deep .el-upload-list__item,
::v-deep .el-upload--picture-card {
  width: 80px;
  height: 80px;
  position: relative;
}

::v-deep .icon_pic {
  position: absolute;
  top: 15px;
  left: 25px;
}
</style>
