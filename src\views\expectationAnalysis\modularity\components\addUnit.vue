<template>
  <el-dialog
    title="新增"
    :visible.sync="dialogVisible"
    @close="cancel"
    width="70%"
  >
    <div class="container">
      <el-form
        class="form"
        :inline="true"
        ref="form"
        :model="form"
        label-width="80px"
        :rules="rules"
      >
        <el-form-item class="form-item" label="模块名称" prop="name">
          <el-input
            v-model="form.name"
            size="small"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-form>

      <p class="title">从已有部套中选择</p>

      <el-form
        ref="searchform"
        :model="searchForm"
        :inline="true"
        label-width="80px"
      >
        <el-form-item label="搜索">
          <el-input
            v-model="searchForm.query"
            size="small"
            placeholder="部套名/部套编码"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="small"
            @click="handleQuery(false)"
            >搜索</el-button
          >
          <el-button
            icon="el-icon-refresh"
            size="small"
            @click="handleQuery(true)"
            >重置</el-button
          >
          <span class="select-txt">已选 <span class="bold">{{selectData.length}}</span> 条</span>
        </el-form-item>
      </el-form>

      <div class="table-box">
        <el-table
          ref="multipleTable"
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>

          <el-table-column label="序号" align="center" type="index">
          </el-table-column>

          <el-table-column
            prop="name"
            label="部套名称"
            align="center"
            width="150"
          >
          </el-table-column>

          <el-table-column
            prop="partCode"
            label="编码规则"
            align="center"
            width="150"
          >
          </el-table-column>

          <el-table-column
            prop="ytMonth"
            label="技术准备日期"
            align="center"
            width=""
          >
            <template slot-scope="{row}">
              <div v-if="String(row.ytMonth)">{{row.ytMonth}}天</div>
            </template>
          </el-table-column>

          <el-table-column
            prop="bidMonth"
            label="定标周期"
            align="center"
            width=""
          >
            <template slot-scope="{row}">
              <div v-if="String(row.bidMonth)">{{row.bidMonth}}天</div>
            </template>
          </el-table-column>

          <el-table-column
            prop="produceCycleMonth"
            label="采购周期"
            align="center"
            width=""
          >
            <template slot-scope="{row}">
              <div v-if="String(row.produceCycleMonth)">{{row.produceCycleMonth}}天</div>
            </template>
          </el-table-column>

          <el-table-column
            prop="produceMonth"
            label="生产周期"
            align="center"
            width=""
          >
            <template slot-scope="{row}">
              <div v-if="String(row.produceMonth)">{{row.produceMonth}}天</div>
            </template>
          </el-table-column>

        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="searchForm.pageNum"
          :limit.sync="searchForm.pageSize"
          @pagination="loadData"
        />
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="confirm" :loading="btnLoading"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { partPage } from "@/api/expectationAnalysis/assemblyUnit";
import { qtModule } from "@/api/expectationAnalysis/modularity";
export default {
  data() {
    return {
      dialogVisible: false,
      btnLoading: false,
      loading: false,
      total: 0,
      tableData: [],
      selectData: [],
      partList: [],
      form: {
        name: "",
      },
      searchForm: {
        query: "",
        pageSize: 10,
        pageNum: 1,
      },
      rules: {
        name: [{ required: true, message: "请输入模块名称", trigger: "blur" }],
      },
    };
  },

  methods: {
    init(row) {
      this.dialogVisible = true;
      this.btnLoading = false;
      this.searchForm.pageNum = 1
      if (row) {
        this.form.name = row.name;
        this.partList = row.partList || [];
        this.selectData = row.partList.map((item) => {
          return item.id;
        });
      } else {
        this.partList = [];
        this.selectData = [];
        this.form.name = "";
      }
      this.loadData();
    },

    handleSelectionChange(selectData) {
      this.selectData = selectData.map((item) => {
        return item.id;
      });
    },

    handleQuery(flag) {
      if (flag) {
        this.searchForm.query = "";
        this.searchForm.pageNum = 1;
      }
      this.loadData();
    },

    loadData() {
      this.loading = true;
      let params = {
        status: 1,
        ...this.searchForm,
      };
      partPage(params)
        .then((res) => {
          let { records, total } = res.data || {};
          this.tableData = records;
          this.partList.forEach((item) => {
            let index = this.tableData.findIndex((i) => i.id == item.id);
            if (index > -1) {
              this.$nextTick(() => {
                this.$refs.multipleTable.toggleRowSelection(
                  this.tableData[index]
                );
              });
            }
          });

          this.total = total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    confirm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.selectData.length === 0) {
            this.$message({
              type: "warning",
              message: "请选择操作的数据",
              duration: 2000,
            });
            return;
          }
          this.btnLoading = true;
          let params = {
            ...this.form,
            partIdList: this.selectData,
          };
          qtModule(params)
            .then((res) => {
              this.$message({
                type: "success",
                message: "操作成功",
                duration: 1500,
              });
              this.btnLoading = false;
              this.dialogVisible = false;
              this.$emit("hideDialog");
            })
            .catch(() => {
              this.btnLoading = false;
            });
        }
      });
    },

    cancel() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style scoped>
.bold {
  font-weight: bold;
}
.form {
  display: flex;
}
.select-txt {
  margin-left: 10px;
}
.title {
  color: #409eff;
  font-size: 15px;
  margin-bottom: 10px;
}
.form-item {
  width: 70%;
}
.form-item ::v-deep .el-form-item__content {
  width: 60%;
}

.def-row {
  display: flex;
  margin: 10px 0;
}
.def-row > .def-col {
  width: 50%;
  font-size: 14px;
  text-align: left;
}
.def-col.text {
  padding-left: 12px;
  box-sizing: border-box;
}
.big-size {
  font-size: 18px;
  font-weight: 500;
}
/* .el-table ::v-deep.el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
        height:30px !important;
    } */
/* ::v-deep.el-table--medium .el-table__cell {
        padding:0 !important;
    } */
</style>
