import request from "@/utils/request";

// 供应商完成情况列表
export function getSupplierCompletionList(params) {
  return request({
    url: "/back-server/castingPurchase/ekgrp/list",
    method: "get",
    params,
  });
}

//供应商完成情况汇总
export function getSupplierCompletionSummary(params) {
  return request({
    url: "/back-server/castingPurchase/ekgrp/summary",
    method: "get",
    params,
  });
}
// 供应商完成情况详情
export function getSupplierCompletionDetail(params) {
  return request({
    url: "/back-server/castingPurchase/ekgrp/supplier/list",
    method: "get",
    params,
  });
}
