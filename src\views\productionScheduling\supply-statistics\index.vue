<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="创建时间">
        <el-date-picker v-model="queryParams.createTimeRange" type="daterange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" />
      </el-form-item>
      <el-form-item label="登录日期">
        <el-date-picker v-model="queryParams.loginTimeRange" type="daterange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" />
      </el-form-item>
      <el-form-item label="维护日期">
        <el-date-picker v-model="queryParams.maintainTimeRange" type="daterange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" />
      </el-form-item>
      <el-form-item label="搜索">
        <el-input v-model="queryParams.query" placeholder="请输入关键字" clearable style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :span="8">
        <el-card shadow="hover" class="statistics-card">
          <div class="card-title">厂商登录总次数</div>
          <div class="card-number">{{ summaryData.loginCount }}<span class="unit">次</span></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="statistics-card">
          <div class="card-title">新注册厂商数</div>
          <div class="card-number">{{ summaryData.registerCount }}<span class="unit">个</span></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="statistics-card">
          <div class="card-title">维护节点数据厂商数量</div>
          <div class="card-number">{{ summaryData.supplierCount }}<span class="unit">个</span></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="tableData" style="width: 100%; margin-top: 20px">
      <el-table-column label="序号" type="index" width="60" align="center">
        <template slot-scope="scope">
          {{ queryParams.pageNum * 10 - queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="厂商名称" prop="supplierName" align="center" />
      <el-table-column label="厂商编号" prop="supplierCode" align="center" />
      <el-table-column label="登录次数" prop="loginCount" align="center" />
      <el-table-column label="维护生产调度数量" prop="purchaseCount" align="center" />
      <el-table-column label="最后登录时间" prop="lastLoginTime" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.lastLoginTime">{{ scope.row.lastLoginTime }}</div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" prop="crtTime" align="center" />
    </el-table>

    <!-- 添加分页组件 -->
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
      :current-page="queryParams.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="total" style="margin-top: 20px; text-align: right;">
    </el-pagination>
  </div>
</template>

<script>
import { getSupplierList, getSupplierSummary } from '@/api/supplier/productionScheduling'

export default {
  name: 'SupplyStatistics',
  data() {
    return {
      loading: false,
      queryParams: {
        createTimeRange: [],
        loginTimeRange: [],
        maintainTimeRange: [],
        query: '',
        pageNum: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      summaryData: {
        loginCount: 0,    // 厂商登录总次数
        registerCount: 0,  // 新注册厂商数
        supplierCount: 0   // 维护节点数据厂商数量
      }
    }
  },
  created() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      // 获取统计数据
      this.getSummaryData()
      const params = { ...this.queryParams }

      // 处理日期范围并从params中删除原始时间范围
      if (params.createTimeRange && params.createTimeRange.length === 2) {
        params.createTimeStart = params.createTimeRange[0]
        params.createTimeEnd = params.createTimeRange[1]
        delete params.createTimeRange
      }
      if (params.loginTimeRange && params.loginTimeRange.length === 2) {
        params.loginTimeStart = params.loginTimeRange[0]
        params.loginTimeEnd = params.loginTimeRange[1]
        delete params.loginTimeRange
      }
      if (params.maintainTimeRange && params.maintainTimeRange.length === 2) {
        params.updateTimeStart = params.maintainTimeRange[0]
        params.updateTimeEnd = params.maintainTimeRange[1]
        delete params.maintainTimeRange
      }

      // 调用接口
      getSupplierList(params).then(response => {
        this.tableData = response.data.records
        this.total = response.data.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      // 手动重置所有时间范围
      this.queryParams = {}
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.handleQuery()
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.handleQuery()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.handleQuery()
    },
    // 获取统计数据
    getSummaryData() {
      const params = {

      }
      // 处理时间范围参数
      if (this.queryParams.createTimeRange?.length === 2) {
        params.createTimeStart = this.queryParams.createTimeRange[0]
        params.createTimeEnd = this.queryParams.createTimeRange[1]
      }
      if (this.queryParams.loginTimeRange?.length === 2) {
        params.loginTimeStart = this.queryParams.loginTimeRange[0]
        params.loginTimeEnd = this.queryParams.loginTimeRange[1]
      }
      if (this.queryParams.maintainTimeRange?.length === 2) {
        params.updateTimeStart = this.queryParams.maintainTimeRange[0]
        params.updateTimeEnd = this.queryParams.maintainTimeRange[1]
      }

      getSupplierSummary(params).then(res => {

        this.summaryData.loginCount = res.data.loginCount
        this.summaryData.registerCount = res.data.registerCount
        this.summaryData.supplierCount = res.data.supplierCount
      })
    }
  }
}
</script>

<style scoped>
.statistics-cards {
  margin-bottom: 10px;
}

.statistics-card {
  background-color: #fff;
  border-radius: 4px;
  text-align: center;
  padding: 10px;
}

.card-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
  text-align: center;
}

.card-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  text-align: center;
}

.unit {
  font-size: 14px;
  margin-left: 4px;
}
</style>
