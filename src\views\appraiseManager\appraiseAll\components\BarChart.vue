<template>
  <div class="chart-container">
    <div class="chart-header">
      <div class="chart-title">各事件分类</div>
    </div>
    <date-range-picker class="chart-header" :use-default-date="true" @date-change="handleDateChange" />
    <div ref="barChart" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import DateRangePicker from './DateRangePicker.vue'
import { apiGetEventCategoryList } from '@/api/appraiseManager/appraiseAll'
export default {
  name: 'BarChart',
  components: {
    DateRangePicker
  },
  data() {
    return {
      chart: null,
      eventCategoryList: [],
      startTime: '',
      endTime: ''
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', this.resizeChart)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    initChart() {
      if (!this.$refs.barChart) return
      this.getEventCategoryList()
      this.chart = echarts.init(this.$refs.barChart)
      const option = {
        grid: {
          left: '3%',
          right: '10%',
          top: '5%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: {
          type: 'category',
          data: ['质量', '交货', '服务', '技术', '价格', '响应度', '供货量']
        },
        series: [{
          type: 'bar',
          data: [],
          itemStyle: {
            color: '#409EFF',
            borderRadius: [0, 15, 15, 0]
          },
          label: {
            show: true,
            position: 'right',
            color: '#606266'
          },
          barWidth: '60%'
        }]
      }
      this.chart.setOption(option)
    },
    getEventCategoryList() {
      apiGetEventCategoryList({
        startTime: this.startTime,
        endTime: this.endTime
      }).then((response) => {
        const data = response.data
        const seriesData = [
          data.qualityCount,
          data.deliveryCount,
          data.serveCount,
          data.technologyCount,
          data.priceCount,
          data.bidCount,
          data.supplyCount
        ]

        this.chart.setOption({
          series: [{
            data: seriesData
          }]
        })
      })
    },
    resizeChart() {
      this.chart && this.chart.resize()
    },
    handleDateChange(dateInfo) {
      console.log('选择的日期信息：', dateInfo)
      this.startTime = dateInfo.dateRange[0];
      this.endTime = dateInfo.dateRange[1];
      this.getEventCategoryList();
// dateInfo.type: 选择的类型（year/quarter/month）
// dateInfo.dateRange: 实际的���期范围
      // dateInfo.display: 显示文本
    }
  }
}
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.chart {
  width: 100%;
  height: 300px;
}
</style>
