<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogFormVisible"
      append-to-body
      width="600px"
    >
      <el-form ref="form" :model="form">
        <el-form-item
          prop="id"
          :rules="[{ required: true, message: '请选择人员', trigger: 'blur' }]"
        >
          <el-radio-group v-model="form.id">
            <el-radio
              v-for="(item, index) in userList"
              :key="index"
              :label="String(item.userId)"
              @change="changeRadio(item)"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showTip">确 定</el-button>
        <el-button @click="dialogFormVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="提示" :visible.sync="tipVisible" width="500px">
      <span
        >您选择的HTC是：{{
          form.name
        }}
        点击确认即无法修改，如需修改请联系管理员</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="tipVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getHtcUserList } from "@/api/bigTestExperiment/personnel.js";
import {
  bmPlanSetQs,
  bmPlanSetHtc,
} from "@/api/bigTestExperiment/remoteSupervision.js";
export default {
  data() {
    return {
      personShow: false,
      title: "",
      formLabelWidth: "120px",
      btnLoading: false,
      dialogFormVisible: false,
      tipVisible: false,
      userList: [],
      planId: "",
      form: {
        id: "",
        name: "",
      },
    };
  },
  methods: {
    //重置
    rest() {},
    //初始化
    init(val) {
      this.dialogFormVisible = true;
      this.planId = val.id;
      this.title = "指定HTC";
      this.form.id = val.htcId;
      this.form.name = val.htcUserName;
      this.$nextTick(() => {
        this.getHtcUserList();
        this.$refs.form.clearValidate();
      });
    },

    getHtcUserList() {
      getHtcUserList()
        .then((res) => {
          this.userList = res.data;
        })
        .catch(() => {});
    },

    changeRadio(val) {
      this.form.finalManagerDeptId = val.deptId;
      this.form.name = val.name;
    },

    showTip() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.tipVisible = true;
        }
      });
    },

    // 确定
    confirm() {
      this.btnLoading = true;
      bmPlanSetHtc({
        planId: this.planId,
        htcId: this.form.id,
      })
        .then((res) => {
          this.$modal.msgSuccess("操作成功");
          this.btnLoading = false;
          this.tipVisible = false;
          this.dialogFormVisible = false;
          this.$parent.getDetail();
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
  },
};
</script>
<style scoped lang="scss">
.btn-box {
  margin-bottom: 8px;
  padding-left: 36px;
  display: flex;
  align-items: center;
  .btn-label {
    width: 85px;
  }
}

.dialog-content {
  height: 60vh;
  display: flex;
  justify-content: space-between;
  .content {
    width: 30%;
  }
  .block {
    flex: 1;
    padding: 8px;
    overflow-y: scroll;
    .timeline-item-top {
      display: flex;
      justify-content: space-between;
      background-color: #e3f0ff;
      padding: 4px 12px;
      border-radius: 4px;
      .top-right {
        font-size: 12px;
        color: #666;
        text-align: right;
      }
      .top-title {
        font-size: 16px;
        color: #1890ff;
        font-weight: 700;
      }
      .top-content {
        font-size: 12px;
        color: #666;
        :nth-child(2) {
          margin-left: 24px;
        }
      }
    }
    .timeline-item-card {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 8px;
      background-color: #f6f7f9;
      padding: 4px 12px;
      border-radius: 4px;
      .card-left {
        display: flex;
        flex-direction: column;
        .card-address {
          font-size: 12px;
          color: #85929b;
        }
        .img-box {
          width: 100%;
          display: flex;
          img {
            width: 100px;
            height: 100px;
            margin-right: 12px;
          }
        }
      }
    }
  }
}
</style>
