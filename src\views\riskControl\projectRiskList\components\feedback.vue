<template>
  <el-dialog
    title="项目详情"
    :visible.sync="dialogVisible"
    width="760px"
    append-to-body
  >
    <div v-loading="loading">
      <el-row>
        <el-col :span="8"
          ><div class="grid-content bg-purple">
            <span>
              <span class="content-label">责任方： </span
              >{{ riskData.dutyName }}</span
            >
          </div></el-col
        >
        <el-col :span="8"
          ><div class="grid-content bg-purple-light">
            <span
              ><span class="content-label">涉及产品：</span>
              {{ riskData.productName }}</span
            >
          </div></el-col
        >
        <el-col :span="8"
          ><div class="grid-content bg-purple-light">
            <span>
              <span class="content-label">风险登记：</span>
              <span>{{
                listFind(riskLevelList, riskData.riskLevel)
                  ? listFind(riskLevelList, riskData.riskLevel).text
                  : ""
              }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row class="row-bg">
        <el-col :span="24"
          ><div class="grid-content bg-purple">
            <div class="content-label">风险点描述：</div>
            <span>{{ riskData.riskDesc }}</span>
          </div></el-col
        >
        <el-col :span="24"
          ><div class="grid-content bg-purple-light">
            <div class="content-label">原因分析：</div>
            <span>{{ riskData.riskReason }}</span>
          </div></el-col
        >
        <el-col :span="24"
          ><div class="grid-content bg-purple-light">
            <div class="content-label">保证措施：</div>
            <span>{{ riskData.measuresDesc }}</span>
          </div></el-col
        >
      </el-row>

      <div class="block">
        <div class="content-label">反馈内容：</div>
        <el-timeline>
          <el-timeline-item
            color="#1890FF"
            v-if="riskData.mesProjectRiskDeptList"
          >
            <div class="timeline-item-top">
              <span>责任部门反馈</span>
              <!-- <span>已完成</span> -->
            </div>

            <div>
              <div
                class="timeline-item-dept"
                v-for="(item, index) in riskData.mesProjectRiskDeptList"
                :key="index"
              >
                <div class="card-left">
                  <div>
                    <p>{{ item.dutyDeptName }}（{{ item.dutyUserName }}）</p>
                    <!-- <p v-if="item.rejectDesc || item.leaderRejectDesc">
                      驳回原因：{{ item.rejectDesc || item.leaderRejectDesc }}
                    </p> -->
                    <p v-if="item.feedbackDesc">回复：{{ item.feedbackDesc || "待回复" }}</p>

                    <div
                      class="file-box"
                      v-if="item.userPermission != 1 && item.feedbackFile"
                    >
                      <div
                        v-for="(item, index) in item.feedbackFile"
                        :key="index"
                      >
                        <el-button type="text" @click="handlePreview(item)">{{
                          item.name
                        }}</el-button>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card-left">
                  <div>
                    <p class="user-info">
                      <span
                        >{{ item.dutyUserLeaderDeptName }}（{{
                          item.dutyUserLeaderName
                        }}）</span
                      >
                      <i
                        class="icon el-icon-success"
                        v-if="item.leaderStatus === 1"
                      ></i>
                    </p>
                    <p v-if="item.leaderRejectDesc">
                      驳回原因：{{ item.leaderRejectDesc }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </el-timeline-item>

          <el-timeline-item
            color="#1890FF"
            v-if="riskData.mesProjectRiskSupplierList"
          >
            <div class="timeline-item-top">
              <span>厂商反馈</span>
            </div>
            <div class="">
              <div class="timeline-item-card" v-for="(aItem,aIndex) in riskData.mesProjectRiskSupplierList" :key="aIndex">
                <div class="card-left">
                  <span>{{
                    aItem.supplierName
                  }}</span>
                  <span
                    >回复：{{
                      aItem.feedbackDesc
                    }}</span
                  >

                  <!-- <div v-if="imageList" class="img-box">
                      <div v-for="(item, index) in imageList" :key="index">
                        <img
                          :src="item.url"
                          alt=""
                        />
                      </div>
                    </div> -->

                  <!-- <el-dialog :visible.sync="imgDialogVisible" append-to-body>
                      <img width="100%" :src="dialogImageUrl" alt="" />
                    </el-dialog> -->

                   <div class="file-box" v-if="fileList">
                    <div v-for="(item, index) in aItem.feedbackFileArray" :key="index">
                      <el-button type="text" @click="handlePreview(item)">{{
                        item.name
                      }}</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <div class="btn-box" v-if="riskData.status === 3">
      <el-button
        @click="handlePass()"
        type="success"
        size="small"
        icon="el-icon-success"
        plain
        >通过</el-button
      >
      <el-button
        @click="handleReject()"
        type="danger"
        size="small"
        icon="el-icon-success"
        plain
        >驳回</el-button
      >
    </div>

    <project-pass ref="projectPass" @loadData="loadData"></project-pass>
    <project-reject ref="projectReject" @loadData="loadData"></project-reject>
  </el-dialog>
</template>

<script>
import { getProjectRisk } from "@/api/riskControl/projectRiskList";
import projectPass from "./projectPass";
import projectReject from "./projectReject";
export default {
  components: { projectPass, projectReject },

  data() {
    return {
      dialogVisible: false,
      imgDialogVisible: false,
      loading: false,
      riskData: {},
      rowData: null,
      imageList: [],
      fileList: [],
      riskLevelList: [
        {
          text: "严重",
          value: 0,
        },
        {
          text: "一般",
          value: 1,
        },
        {
          text: "轻微",
          value: 2,
        },
      ],
    };
  },

  created() {},

  methods: {
    init(row) {
      this.fileList = [];
      this.dialogVisible = true;
      this.rowData = row;
      this.loadData();
    },

    // 通过
    handlePass() {
      this.$refs.projectPass.init(this.riskData.id);
    },

    // 驳回
    handleReject() {
      this.$refs.projectReject.init(this.riskData.id);
    },

    // 点击文件
    handlePreview(file) {
      window.open(
        process.env.VUE_APP_FILE_API + file.response.data.newName
      );
    },

    listFind(list = [], value) {
      let index = list.findIndex((item) => {
        return item.value == value;
      });
      if (index > -1) {
        return list[index];
      }
    },
    loadData() {
      this.loading = true;
      getProjectRisk(this.rowData.id)
        .then((res) => {
          this.riskData = res.data;

          if (this.riskData.mesProjectRiskDeptList) {
            this.riskData.mesProjectRiskDeptList.forEach((item) => {
              if (item.feedbackFile) {
                let fileAry = JSON.parse(item.feedbackFile);
                item.feedbackFile = fileAry.map((item) => {
                  return {
                    name: item.name,
                    response: {
                      data: {
                        name: item.name,
                        newName: item.newName,
                        url: "/file/",
                      },
                    },
                  };
                });
              } else {
                item.feedbackFile = [];
              }
            });
          }

          if (this.riskData.mesProjectRiskSupplierList) {
            this.riskData.mesProjectRiskSupplierList.forEach((aItem,aIndex)=>{
              if(aItem.feedbackFile){
                this.riskData.mesProjectRiskSupplierList[aIndex]['feedbackFileArray'] = JSON.parse(aItem.feedbackFile).map((item) => {
                  return {
                    name: item.name,
                    response: {
                      data: {
                        name: item.name,
                        newName: item.newName,
                        url: "/file/",
                      },
                    },
                  };
                });
              }

              if(aItem.feedbackImage){
                this.riskData.mesProjectRiskSupplierList[aIndex]['feedbackImageArray'] = JSON.parse(aItem.feedbackImage).map((item) => {
                  return {
                    url: process.env.VUE_APP_IMAGE_API + item,
                    response: {
                      data: {
                        newName: item,
                      },
                    },
                  };
                });
              }
            })
            this.riskData = JSON.parse(JSON.stringify(this.riskData))
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped lang='scss'>
::v-deep .el-radio__label {
  display: none;
}
.img-box {
  display: flex;
  img {
    cursor: pointer;
    width: 146px;
    height: 146px;
    margin-right: 12px;
    border-radius: 8px;
  }
}
.over-text {
  font-size: 13px;
  width: 290px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.content-label {
  font-weight: 700;
}
::v-deep .el-dialog__body {
  line-height: 36px;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}
.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
::v-deep .el-row--flex {
  margin-left: 22px;
}
// ::v-deep .el-dialog {
//   margin-top: 30vh !important;
// }
.timeline-item-top {
  display: flex;
  justify-content: space-between;
  background-color: #f3f6fd;
  padding: 4px 12px;
  border-radius: 4px;
}
.input-box {
  margin-top: 8px;
}
.timeline-item-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  background-color: #f6f7f9;
  padding: 4px 12px;
  border-radius: 4px;
  .card-left {
    display: flex;
    flex-direction: column;
  }
}
.add-btn {
  margin-top: 8px;
  text-align: right;
}

.timeline-item-dept {
  display: flex;
  margin-top: 8px;
  height: auto;

  .card-left {
    width: calc((100% - 10px) / 2);
    background-color: #f6f7f9;
    padding: 4px 12px;
    border-radius: 4px;
    margin-right: 10px;
    min-height: 80px;
    box-sizing: border-box;
    height: inherit;
    align-items: stretch;
    .user-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .icon {
        font-size: 14px;
        color: #65d299;
      }
    }
  }
  .card-left:last-of-type {
    margin-right: 0;
  }
}
</style>
