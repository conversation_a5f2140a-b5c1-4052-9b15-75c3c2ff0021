import hljs from "highlight.js";
import xml from "highlight.js/lib/languages/xml";
import json from "highlight.js/lib/languages/json";

hljs.registerLanguage("xml", xml);
hljs.registerLanguage("json", json);

// 创建 Vue 插件
export const vuePlugin = {
  install(Vue) {
    // 注册全局指令
    Vue.directive('highlight', {
      deep: true,
      bind(el) {
        const blocks = el.querySelectorAll('pre code');
        blocks.forEach((block) => {
          hljs.highlightElement(block);
        });
      },
      componentUpdated(el) {
        const blocks = el.querySelectorAll('pre code');
        blocks.forEach((block) => {
          hljs.highlightElement(block);
        });
      }
    });
  }
};

export default hljs;
