<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogFormVisible"
      append-to-body
      width="840px"
    >
      <el-form
        :model="dialogQueryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="搜索" prop="keyword">
          <el-input
            v-model="dialogQueryParams.keyword"
            placeholder="请输入姓名"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="search"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>

      <el-table
        v-loading="dialogLoading"
        :data="dialogTableList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" />
        <el-table-column label="人员姓名" prop="name" />
        <el-table-column label="所属部门" prop="alarmStatus" />
        <el-table-column label="手机号" prop="phone" />
        <el-table-column label="邮箱" prop="email" />
      </el-table>

      <pagination
        v-show="dialogTotal > 0"
        :total="dialogTotal"
        :page.sync="dialogQueryParams.page"
        :limit.sync="dialogQueryParams.size"
        @pagination="getListUser"
      />

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="dialogFormVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listUser } from "@/api/system/user.js";
import { addBmUser } from "@/api/bigTestExperiment/personnel.js";
export default {
  data() {
    return {
      personShow: false,
      title: "新增",
      formLabelWidth: "120px",
      dialogLoading: false,
      btnLoading: false,
      dialogFormVisible: false,
      dialogQueryParams: {
        keyword: "",
        page: 1,
        size: 10,
      },
      dialogTotal: 0,
      dialogTableList: [],
      selectedList: [],
    };
  },
  methods: {
    //重置
    rest() {},
    search() {
      this.dialogQueryParams.page = 1;
      this.getListUser();
    },
    //初始化
    init(row) {
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.selectedList = [];
        this.getListUser();
      });
    },

    getListUser() {
      this.dialogLoading = true;
      listUser(this.dialogQueryParams).then((res) => {
        this.dialogLoading = false;
        this.dialogTableList = res.data;
        this.dialogTotal = res.total;
      });
    },

    // 表格多选
    handleSelectionChange(val) {
      this.selectedList = val.map((item) => {
        return {
          userId: item.id,
          name: item.name,
        };
      });
    },
    // 确定
    confirm() {
      addBmUser(this.selectedList)
        .then((response) => {
          this.$modal.msgSuccess("操作成功");
          this.dialogFormVisible = false;
          this.$parent.getList();
        })
        .catch(() => {});
    },
  },
};
</script>
<style scoped lang="scss">
.btn-box {
  margin-bottom: 8px;
  padding-left: 36px;
  display: flex;
  align-items: center;
  .btn-label {
    width: 85px;
  }
}
</style>
