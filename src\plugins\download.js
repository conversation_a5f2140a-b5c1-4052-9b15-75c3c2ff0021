import axios from 'axios'
import { Message } from 'element-ui'
import { saveAs } from 'file-saver'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { blobValidate } from "@/utils/ruoyi";

const baseURL = process.env.VUE_APP_BASE_API
function downloadFileFromUrl(url, filename) {
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}
export default {
  filePath(filePath) {
    downloadFileFromUrl('/htc/user-server/file/getFile?filePath='+ filePath,filePath)
    // return
    // let url ='/api/user-server/file/getFile?filePath='+ filePath
    // // let url ='/api/user-server/file/download/resource?filePath='+ filePath
    // axios({
    //   method: 'get',
    //   url: url,
    //   responseType: 'blob',
    //   headers: { 'Authorization': 'Bearer ' + getToken() }
    // }).then(async (res) => {
    //   const isLogin = await blobValidate(res.data);
    //   if (isLogin) {
    //     console.log("res.data",res)
    //     const blobs = new Blob([res.data],)
    //     // const blob = res.data
    //     // downloadFileFromBlob(blob, res.headers['download-filename'])
    //     this.saveAs(blobs, res.headers['download-filename'])
    //   } else {
    //     this.printErrMsg(res.data);
    //   }
    // }).catch((r) => {
    //   console.error(r)
    //   Message.error('下载文件出现错误，请联系管理员！')
    // })
  },
  name(name, isDelete = true) {
    var url = baseURL + "/common/download?fileName=" + encodeURI(name) + "&delete=" + isDelete
    axios({
      method: 'get',
      url: url,
      responseType: 'blob',
      headers: { 'Authorization': 'Bearer ' + getToken() }
    }).then(async (res) => {
      const isLogin = await blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data])
        this.saveAs(blob, decodeURI(res.headers['download-filename']))
      } else {
        this.printErrMsg(res.data);
      }
    })
  },
  resource(resource) {
    var url = baseURL + "/common/download/resource?resource=" + encodeURI(resource);
    axios({
      method: 'get',
      url: url,
      responseType: 'blob',
      headers: { 'Authorization': 'Bearer ' + getToken() }
    }).then(async (res) => {
      const isLogin = await blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data])
        this.saveAs(blob, decodeURI(res.headers['download-filename']))
      } else {
        this.printErrMsg(res.data);
      }
    })
  },
  zip(url, name) {
    var url = baseURL + url
    axios({
      method: 'get',
      url: url,
      responseType: 'blob',
      headers: { 'Authorization': 'Bearer ' + getToken() }
    }).then(async (res) => {
      const isLogin = await blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data], { type: 'application/zip' })
        this.saveAs(blob, name)
      } else {
        this.printErrMsg(res.data);
      }
    })
  },
  saveAs(text, name, opts) {
    saveAs(text, name, opts);
  },
  async printErrMsg(data) {
    const resText = await data.text();
    const rspObj = JSON.parse(resText);
    const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
    Message.error(errMsg);
  }
}

