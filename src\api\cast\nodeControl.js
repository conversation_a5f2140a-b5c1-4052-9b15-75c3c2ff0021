import request from '@/utils/request'
// 分页查询列表
export function getCastingNodePage(param) {
    return request({
        url: '/back-server/castingPurchase/getCastingNodePage',
        method: 'get',
        params: param,
    })
}
// 查看节点
export function findAllNodeByCastingId(param) {
    return request({
        url: '/back-server/castingNode/findAllNodeByCastingId',
        method: 'get',
        params: param,
    })
}
// 查询修改节点
export function findAllNodeByCastingIdForUpdate(param) {
    return request({
        url: '/back-server/castingNode/findAllNodeByCastingIdForUpdate',
        method: 'get',
        params: param,
    })
}
// 修改节点
export function batchUpdateNodePlanTime(param) {
    return request({
        url: '/back-server/castingNode/batchUpdateNodePlanTime',
        method: 'post',
        data: param,
    })
}