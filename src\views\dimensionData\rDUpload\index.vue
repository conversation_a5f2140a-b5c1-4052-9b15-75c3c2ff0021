<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="年份" prop="year">
        <el-date-picker
          v-model="queryParams.year"
          format="yyyy年"
          value-format="yyyy"
          type="year"
          placeholder="选择年"
        >
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="部门" prop="deptNo">
        <el-cascader @change="cascaderChange" :options="deptTree" :show-all-levels="false" placeholder="请选择部门" clearable
          :props="{ expandTrigger: 'click ', value: 'id', label: 'name', children: 'children' }"></el-cascader>
      </el-form-item> -->
      <el-form-item label="状态" prop="auditStatus">
        <el-select
          v-model="queryParams.auditStatus"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="审核中" value="0"></el-option>
          <el-option label="审核通过" value="1"></el-option>
          <el-option label="被驳回" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="query">
        <el-input
          v-model="queryParams.query"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          style="margin-left: 8px"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
          style="margin-left: 8px"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div class="mb8 justify-between">
      <el-row :gutter="10" class="item-center">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd('add')"
            v-if="queryParams.userType == 2"
            >新增</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="handleAdd('draft')"
            v-if="queryParams.userType == 2"
            >查看草稿</el-button
          >
          <el-popover
            placement="bottom"
            v-model="openUploadPopover"
            v-if="techDataButtonShow == 0"
            trigger="click"
          >
            <div>
              <el-date-picker
                v-model="year"
                format="yyyy年"
                value-format="yyyy"
                type="year"
                placeholder="选择研发上传开启年份"
              >
              </el-date-picker>
              <div class="openUploadPopoverBottomBox">
                <el-button type="primary" size="mini" @click="openUpload()"
                  >确认</el-button
                >
                <el-button
                  type="info"
                  size="mini"
                  @click="(openUploadPopover = false), (year = '')"
                  >取消</el-button
                >
              </div>
            </div>
            <!-- <el-button style="margin-left:10px" slot="reference" v-if="techDataButtonShow == 0" type="primary"
              size="mini">研发上传开启</el-button> -->
          </el-popover>
        </el-col>
      </el-row>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </div>
    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
      max-height="600"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="120" />
      <el-table-column label="指标年限" prop="year" width="120" />
      <el-table-column label="分类编号" prop="typeNo" width="120" />
      <el-table-column label="审核部门" prop="deptName" width="120" />
      <el-table-column label="提交日期" prop="createTime" width="120">
        <template slot-scope="scope">
          {{ scope.row.createTime ? scope.row.createTime.split("T")[0] : "" }}
        </template>
      </el-table-column>

      <el-table-column label="状态" prop="" width="100">
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.auditStatus === 0"
            type="warning"
            effect="plain"
            >审核中</el-tag
          >
          <el-tag
            v-if="scope.row.auditStatus === 1"
            type="success"
            effect="plain"
            >已通过</el-tag
          >
          <el-tag
            v-if="scope.row.auditStatus === 2"
            type="danger"
            effect="plain"
            >被驳回</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="审核备注" prop="auditRemark" width="120" />
      <el-table-column
        label="上年度研发投入（万元）"
        prop="researchCost"
        width="180"
      />
      <el-table-column
        label="上年度营业收入（万元）"
        prop="revenueCost"
        width="180"
      />
      <el-table-column label="上年度研发占比" prop="researchProportion">
        <template slot-scope="scope">
          {{ scope.row.researchProportion ? scope.row.researchProportion : 0 }}%
        </template>
      </el-table-column>
      <el-table-column label="证明文件" width="100" align="center">
        <template slot-scope="scope">
          <i
            class="el-icon-search view-icon"
            v-if="scope.row.deptFileList && scope.row.deptFileList.length > 0"
            @click="viewFiles(scope.row.deptFileList)"
          ></i>
          <span v-else> - </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-view"
            @click="handleOperation(scope.row, 'view')"
            >查看</el-button
          >
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleOperation(scope.row, 'edit')"
            v-if="scope.row.auditStatus === 2"
            >修改</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <add ref="addDialog" @refresh="resetQuery"></add>
    <edit ref="editDialog" @refresh="resetQuery"></edit>
  </div>
</template>

<script>
import add from "./components/add.vue";
import edit from "./components/edit.vue";
import {
  getTechDataPageSupplier,
  getDeptTree,
  getTechDataButton,
  getTechDataOpen,
} from "@/api/dimensionData/rDUpload.js";

export default {
  components: {
    add,
    edit,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 查询参数
      dateRange: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: undefined,
        auditStatus: undefined, //状态
        year: undefined, // 年份
        deptNo: undefined, // 部门
        userType: "",
      },
      //部门树
      deptTree: [],
      //机组详情
      posidList: {},
      year: "", //开启研发上传年份
      openUploadPopover: false,
      techDataButtonShow: "", //开启研发上传按钮显示权限
      userInfo: {},
    };
  },
  created() {
    this.queryParams.year = new Date(Date.now()).getFullYear().toString();
    getDeptTree().then((res) => {
      this.deptTree = res.data;
    });
    this.userInfo = JSON.parse(localStorage.getItem("userInfo"));
    this.queryParams.userType = this.userInfo.userType;
    getTechDataButton({ userType: this.userInfo.userType }).then((res) => {
      this.techDataButtonShow = res.data;
    });
    this.getList();
  },
  methods: {
    viewFiles(files) {
      console.log(files);
    },
    cascaderChange(event) {
      if (event && event.length >= 1) {
        this.queryParams.deptNo = event[event.length - 1];
      } else {
        this.queryParams.deptNo = undefined;
      }
    },
    //开启研发上传
    openUpload() {
      if (!this.year) {
        this.$message.error("请选择年份");
        return;
      }
      getTechDataOpen({ year: Number(this.year) }).then((res) => {
        this.openUploadPopover = false;
      });
    },
    /** 分页查询 */
    getList() {
      this.loading = true;
      getTechDataPageSupplier(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    // 处理新增/修改/查看操作
    handleOperation(row, mode) {
      this.$refs.editDialog.show(row, mode);
    },
    /** 新增按钮操作 */
    handleAdd(mode) {
      this.$refs.addDialog.show(null, mode);
    },
    /** 查看草稿操作 */
    handleViewDraft() {
      this.$refs.addDialog.loadDraft();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  line-height: 36px;
}

.view-icon {
  color: #1890ff; // 更改为浅蓝色
  font-size: 18px; // 稍微调大一点
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

.item-center {
  display: flex;
  align-items: center;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.font-size-14 {
  font-size: 14px;
}

.grey {
  color: #999;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}

.openUploadPopoverBottomBox {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}
</style>
