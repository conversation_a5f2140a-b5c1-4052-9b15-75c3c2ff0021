<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="机组" prop="status" label-width="40px">
        <el-select v-model="queryParams.posid" placeholder="请选择" clearable>
          <el-option
            v-for="(dict,index) in engineeringList"
            :key="index"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="模块" prop="status" label-width="40px">
        <el-select v-model="queryParams.moduleCode" placeholder="请选择" clearable>
          <el-option
            v-for="(dict,index) in moduleList"
            :key="index"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status" label-width="40px">
        <el-select v-model="queryParams.nodeStatus" placeholder="请选择" clearable>
          <el-option
            v-for="(dict,index) in studes"
            :key="index"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="搜索">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
    >
      <el-table-column label="序号" width="55" type="index" />
      <el-table-column label="部套名称" prop="dwgName">
        <template slot-scope="scope">
          <a @click="items(scope.row)" style="color: #1890FF; cursor: pointer">{{
            scope.row.dwgName
          }}</a>
        </template>
      </el-table-column>
      <el-table-column label="部套编码" prop="dwgNo" />
      <el-table-column label="令号" prop="posid" />
       <el-table-column label="项目名称" prop="post1">
        <template slot-scope="scope">
          <a @click="item(scope.row)" style="color: #1890FF; cursor: pointer">{{
            scope.row.post1
          }}</a>
        </template>
      </el-table-column>
      <el-table-column label="节点名称" prop="nodeName" />
      <el-table-column label="节点状态" prop="nodeControlStatus">
        <template slot-scope="scope">
          <span
            style="
              font-size: 12px;
              display: inline-block;
              width: 44px;
              height: 28px;
              line-height: 28px;
              border-radius: 3px;
              text-align: center;
            "
            :class="scope.row.nodeStatus == 1 ? 'finish':'bao'"
            >{{ scope.row.nodeStatus == 0 ? '未开始': scope.row.nodeStatus == 1 ? '已完成': scope.row.nodeStatus == 2 ? '进行中': ''
                }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="节点开始时间" prop="planTime" />
      <el-table-column label="完成时间" prop="expDat" />
      <el-table-column label="记录数" prop="nodeRecordNum" />
      <el-table-column label="版本状态" prop="zps0075"></el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleLook(scope.row)"
            >查看记录</el-button
          >
           <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:post:edit']"
            >设为已完成</el-button
          > -->
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:post:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 查看节点弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="opens"
      width="600px"
      append-to-body
    >
      <div class="block" v-loading="lookLoading">
        <el-timeline>

          <el-timeline-item v-for="(item,index) in nodeInfoList" :key="index" :timestamp="item.finishTime">
            <el-card>
            <p class="font16 fontweight ">{{item.nodeName}}</p>
            <div  class="node">
              <span >计划完成时间：{{item.planTime}}</span>
              <span>实际完成时间：{{item.finishTime}}</span>
            </div>
            </el-card>
          </el-timeline-item>

        </el-timeline>
      </div>

    </el-dialog>

    <!-- 项目名称详情弹框 -->
    <el-dialog
      title="项目详情"
      :visible.sync="itemOpen"
      width="600px"
      append-to-body
    >
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>机组名称：<span>{{post1List.post1}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>令号：<span>{{post1List.posid}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>产品类型：<span>{{post1List.assemblyF}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>机组容量：<span>{{post1List.usr04}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>合同签订日期：<span>{{post1List.zps0177}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>定类：<span>{{post1List.projDl}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>所属电厂：<span>{{post1List.name1}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>板块名称：<span>{{post1List.boardName}}</span></span>
          </div></el-col
        >
      </el-row>
    </el-dialog>


     <!-- 部套详情 -->
    <el-dialog title="部套详情" :visible.sync="open" width="600px" append-to-body>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple"><span>部套名称：<span>{{dwgList.dwgName}}</span></span></div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light"><span>部套编码：<span>{{dwgList.dwgNo}}</span></span></div></el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple"><span>分工：<span>{{dwgList.arrangeName}}</span></span></div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light"><span>项目经理：<span>{{dwgList.zfstVernr}}</span></span></div></el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple"><span>合同交期：<span>{{dwgList.zps0010}}</span></span></div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light"><span>MM：<span>{{dwgList.moduleGrp}}</span></span></div></el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple"><span>上次排期：<span>2022-05-30</span></span></div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light"><span>项目交期：<span>{{dwgList.zps0079}}</span></span></div></el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple"><span>进展情况：<span>{{dwgList.zprogressName}}</span></span></div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light"><span>预计完成时间：<span>2022-05-30</span></span></div></el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple"><span>未完原因：<span>{{dwgList.zps0079}}</span></span></div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light"><span>状态：<span>未完成</span></span></div></el-col>
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="24"><div class="grid-content bg-purple"><span>状态说明：<span>{{dwgList.zps0079}}</span></span></div></el-col>
        <!-- <el-col :span="12"><div class="grid-content bg-purple-light"><span>状态：<span>未完成</span></span></div></el-col> -->
      </el-row>
      <el-divider></el-divider>


      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple"><span>机组名称：<span>{{dwgList.post1}}</span></span></div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light"><span>令号：<span>{{dwgList.posid}}</span></span></div></el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple"><span>容量：<span>{{dwgList.zps0079}}</span></span></div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light"><span>产品号：<span>{{dwgList.prodId}}</span></span></div></el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple"><span>定类：<span>{{dwgList.projDl}}</span></span></div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light"><span>模块名称：<span>{{dwgList.moduleName}}</span></span></div></el-col>
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"><div class="grid-content bg-purple"><span>板块：<span>{{dwgList.boardName}}</span></span></div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple-light"><span>模块编码：<span>{{dwgList.moduleCode}}</span></span></div></el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
// import {
//   listPost,
//   getPost,
//   delPost,
//   addPost,
//   updatePost,
// } from "@/api/system/post";
import {
  getEngineeringList,
  getModuleList,
} from "@/api/items/items";
import {getNodeList} from '@/api/items/management'

export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      dwgList:{},
      post1List:{},
      // 遮罩层
      loading: false,
      lookLoading:false,
      nodeInfoList:[],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
    // 是否显示弹出层
      open: false,
      // 查看节点是否显示弹出层
      opens: false,
      // 项目名称弹窗
      itemOpen:false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        posid:'',
        moduleCode:'',
        nodeStatus:'',
        keyword:''
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" },
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        ],
      },
      engineeringList:[],
      moduleList:[],
      studes: [
        { label: "未管控", value: 0 },
        { label: "已管控", value: 1 },
      ],
    };
  },
  created() {
    this.getModuleList(),
    this.getEngineeringList();
    this.getList();
  },
  methods: {
    //模块列表
    getModuleList() {
      getModuleList(this.dataList).then((res) => {
        res.data.forEach((element, index) => {
          this.moduleList.push({
            value: element.moduleCode,
            label: element.moduleName+"("+element.moduleCode+")",
          });
        });
      });
    },

    //机组列表
    getEngineeringList() {
      getEngineeringList(this.dataForm).then((res) => {
        res.data.forEach((element, index) => {
          this.engineeringList.push({
            value: element.posid,
            label: element.post1+"("+element.posid+")",
          });
        });
      });
    },

    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getNodeList(this.queryParams).then((response) => {
        if(response.success){
          this.postList = response.data;
          this.total = response.total;
        }else{
          this.$message({
            type:'error',
            message:response.msg,
            duration:1500
          })
        }
        this.loading = false;
      }).catch(()=>{
        this.loading = false
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加岗位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const postId = row.postId || this.ids;
      getPost(postId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改岗位";
      });
    },
    /** 查看记录 跳转  带参数去 请求 */
    handleLook(row){
      this.$router.push({path:'/items/items/record/index',query:{nodeId:row.nodeId}});
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
      // 项目名称弹出框
    item(item){
      this.post1List=item
      this.itemOpen=true
    },
    // 部套名称
    items(item){
      this.dwgList=item
      this.open=true
    }
  },
};
</script>
<style scoped>
::v-deep .el-dialog__body{
  line-height: 30px
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #E8F4FF;
  border: 1px solid rgba(56,154,255,0.4);
  color: #389AFF;
}
.finish {
  background: #E7FAF0;
  border: 1px solid rgba(101,210,153,0);
  color: #65D299;
}
</style>
