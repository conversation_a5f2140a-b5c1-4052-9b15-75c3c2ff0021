// 用户体系-用户查询-工种
const hpUserSystemSpecialstate = [
    {
        label: "安全员",
        value: 1
    },
    {
        label: "质量员",
        value: 2
    },
    {
        label: "考勤员",
        value: 3
    },
    {
        label: "绩效员",
        value: 4
    },
    {
        label: "设备员",
        value: 5
    },
]

// 用户体系-部门配置-异常状态
const hpUserDeptExceptStatus = [
    {
        label: "绩效专员不全",
        value: 0
    },
    {
        label: "打分正职不全",
        value: 1
    },
    {
        label: "班组长不全",
        value: 2
    },
]

// 用户体系-部门配置-忽略状态
const hpUserDeptIgnoreOptions = [
    {
        label: "不忽略",
        value: 0
    },
    {
        label: "忽略",
        value: 1
    },
]

// 员工-绩效任务-绩效表配置-状态
const hpStaffPerfPerfFormStatus = [
    {
        label: "待提交",
        value: 0
    },
    {
        label: "待一审",
        value: 1
    },
    {
        label: "待终审",
        value: 2
    },
    {
        label: "驳回",
        value: 3
    },
    {
        label: "通过",
        value: 4
    },
    {
        label: "进度待审批",
        value: 5
    },
]

// 员工-绩效任务-绩效表配置-树形指标 
const hpStaffPerfPerfFormTarget = [
    {
        label: "个人指标",
        value: 0
    },
    {
        label: "关键指标",
        value: 1
    },
    {
        label: "领导指派",
        value: 2
    },
]

// 员工-绩效任务-工作成果报告-审批状态
const hpStaffPerfWorkReportStatus = [
    {
        label: "未提交",
        value: 0
    },
    {
        label: "待审核",
        value: 1
    },
    {
        label: "审核通过",
        value: 2
    },
    {
        label: "驳回",
        value: 3
    },
]

// 员工-绩效任务-部门奖惩-审批状态
const hpStaffPerfDeptRewardStatus = [
    {
        value: 0,
        label: '待审批'
    },
    {
        value: 1,
        label: '已通过'
    },
    {
        value: 2,
        label: '不通过'
    },
]
// 员工-绩效任务-部门奖惩-类型
const hpStaffPerfDeptRewardType = [
    {
        value: 0,
        label: '惩罚'
    },
    {
        value: 1,
        label: '奖励'
    }
]
// 员工-绩效任务-部门奖惩-时间排序
const hpStaffPerfDeptRewardTimeIndex = [
    {
        label: "正序",
        value: 0
    },
    {
        label: "倒序",
        value: 1
    },
]

// 员工-绩效任务-奖惩结果-类型
const hpStaffPerfRewardResultType = [
    {
        value: 0,
        label: '惩罚'
    },
    {
        value: 1,
        label: '奖励'
    }
]
// 员工-绩效任务-部门奖惩-时间排序
const hpStaffPerfRewardResultTimeIndex = [
    {
        label: "正序",
        value: 0
    },
    {
        label: "倒序",
        value: 1
    },
]
// 员工-绩效任务-部门奖惩-审批状态
const hpStaffPerfRewardResultStatus = [
    {
        label: "待审批",
        value: 0
    },
    {
        label: "已通过",
        value: 1
    },
    {
        label: "不通过",
        value: 2
    },
]

// 员工-绩效考核-月度评分-排序
const hpStaffPerfMonthGradeSort = [
    {
        label: "工号降序",
        value: 0
    },
    {
        label: "工号升序",
        value: 1
    }
]
// 员工-绩效考核-月度评分-状态
const hpStaffPerfMonthGradeStatus = [
    {
        label: "待评分",
        value: 0
    },
    {
        label: "已评分",
        value: 1
    }
]
// 员工-绩效考核-年终评分-状态
const hpStaffPerfYearScoreStatus = [
    {
        label: "待评分",
        value: 0
    },
    {
        label: "已评分",
        value: 1
    }
]

// 员工-绩效考核-年终评分-排序
const hpStaffPerfYearScoreSort = [
    {
        label: "工号降序",
        value: 0
    },
    {
        label: "工号升序",
        value: 1
    }
]

// 班组长-绩效任务-绩效表审批-审批状态
const hpShiftPerfFormAuditStatus = [
    {
        value: 0,
        label: '未开始'
    },
    {
        value: 1,
        label: '一审通过'
    },
    {
        value: 2,
        label: '二审通过'
    },
    {
        value: 3,
        label: '驳回'
    },
    {
        value: 3,
        label: '通过'
    }
]

// 班组长-绩效任务-绩效表配置-人员-状态
const hpShiftPerfPerfFormStatus = [
    {
        value: 0,
        label: '待提交'
    },
    {
        value: 1,
        label: '待审核'
    },
    {
        value: 2,
        label: '通过'
    },
    {
        value: 3,
        label: '驳回'
    }
]

// 班组长-绩效任务-绩效表配置-树形指标 
const hpShiftPerfPerfFormTarget = [
    {
        label: "个人指标",
        value: 0
    },
    {
        label: "关键指标",
        value: 1
    },
    {
        label: "领导指派",
        value: 2
    },
]

// 班组长-绩效任务-部门奖惩管理-类型
const hpShiftPerfDeptRewardType = [
    {
        value: 0,
        label: '惩罚'
    },
    {
        value: 1,
        label: '奖励'
    }
]
// 班组长-绩效任务-部门奖惩管理-时间排序
const hpShiftPerfDeptRewardsSort = [
    {
        label: "正序",
        value: 0
    },
    {
        label: "倒序",
        value: 1
    },
]
// 班组长-绩效任务-部门奖惩管理-审批状态
const hpShiftPerfDeptRewardStatus = [
    {
        label: "待审批",
        value: 0
    },
    {
        label: "已通过",
        value: 1
    },
    {
        label: "不通过",
        value: 2
    },
]

// 班组长-绩效任务-工作报告成果审核-状态
const hpShiftPerfworkReportAuditStatus = [
    {
        label: "未提交",
        value: 0
    },
    {
        label: "待审核",
        value: 1
    },
    {
        label: "审核通过",
        value: 2
    },
    {
        label: "驳回",
        value: 3
    }
]

// 班组长-绩效考核-月度评分-评分状态
const hpShiftPerfMonthGradeStatus = [
    {
        label: "进行中",
        value: 0
    },
    {
        label: "已完成",
        value: 1
    }
]

// 班组长-绩效考核-月度评分-详情-评分状态
const hpShiftPerfMonthGradePeopleStatus = [
    {
        label: "待评分",
        value: 0
    },
    {
        label: "已评分",
        value: 1
    }
]
// 班组长-绩效考核-月度评分-详情-排序
const hpShiftPerfMonthGradePeopleSort = [
    {
        label: "工号降序",
        value: 0
    },
    {
        label: "工号升序",
        value: 1
    }
]


// 工长-绩效任务-绩效表审批-审批状态
const hpForemanPerfFormAuditStatus = [
    {
        value: 0,
        label: '未开始'
    },
    {
        value: 1,
        label: '一审通过'
    },
    {
        value: 2,
        label: '二审通过'
    },
    {
        value: 3,
        label: '驳回'
    },
    {
        value: 3,
        label: '通过'
    }
]


// 工长-绩效任务-绩效表配置-人员-状态
const hpForemanPerfPerfFormStatus = [
    {
        value: 0,
        label: '待提交'
    },
    {
        value: 1,
        label: '待审核'
    },
    {
        value: 2,
        label: '通过'
    },
    {
        value: 3,
        label: '驳回'
    }
]

// 工长-绩效任务-绩效表配置-树形指标 
const hpForemanPerfPerfFormTarget = [
    {
        label: "个人指标",
        value: 0
    },
    {
        label: "关键指标",
        value: 1
    },
    {
        label: "领导指派",
        value: 2
    },
]

// 工长-绩效任务-部门奖惩管理-类型
const hpForemanPerfDeptRewardType = [
    {
        value: 0,
        label: '惩罚'
    },
    {
        value: 1,
        label: '奖励'
    }
]
// 工长-绩效任务-部门奖惩管理-时间排序
const hpForemanPerfDeptRewardsSort = [
    {
        label: "正序",
        value: 0
    },
    {
        label: "倒序",
        value: 1
    },
]
// 工长-绩效任务-部门奖惩管理-审批状态
const hpForemanPerfDeptRewardStatus = [
    {
        label: "待审批",
        value: 0
    },
    {
        label: "已通过",
        value: 1
    },
    {
        label: "不通过",
        value: 2
    },
]

// 工长-绩效任务-工作报告审核-状态
const hpForemanPerfWorkReportAuditStatus = [
    {
        label: "未提交",
        value: 0
    },
    {
        label: "待审核",
        value: 1
    },
    {
        label: "审核通过",
        value: 2
    },
    {
        label: "驳回",
        value: 3
    }
]

// 工长-绩效考核-月度评分-评分状态
const hpForemanPerfMonthGradeStatus = [
    {
        label: "进行中",
        value: 0
    },
    {
        label: "已完成",
        value: 1
    }
]
// 工长-绩效考核-月度评分-详情-评分状态
const hpForemanPerfMonthGradePeopleStatus = [
    {
        label: "待评分",
        value: 0
    },
    {
        label: "已评分",
        value: 1
    }
]
// 工长-绩效考核-月度评分-详情-排序
const hpForemanPerfMonthGradePeopleSort = [
    {
        label: "工号降序",
        value: 0
    },
    {
        label: "工号升序",
        value: 1
    }
]


// 部门副职-绩效任务-绩效表审批-审批状态
const hpSupervisorPerfFormAuditStatus = [
    {
        value: 0,
        label: '未开始'
    },
    {
        value: 1,
        label: '一审通过'
    },
    {
        value: 2,
        label: '二审通过'
    },
    {
        value: 3,
        label: '驳回'
    },
    {
        value: 3,
        label: '通过'
    }
]

// 部门副职-绩效任务-绩效表配置-人员-状态
const hpSupervisorPerfPerfFormStatus = [
    {
        value: 0,
        label: '待提交'
    },
    {
        value: 1,
        label: '待审核'
    },
    {
        value: 2,
        label: '通过'
    },
    {
        value: 3,
        label: '驳回'
    }
]

// 部门副职-绩效任务-绩效表配置-树形指标 
const hpSupervisorPerfPerfFormTarget = [
    {
        label: "个人指标",
        value: 0
    },
    {
        label: "关键指标",
        value: 1
    },
    {
        label: "领导指派",
        value: 2
    },
]

// 部门副职-绩效任务-部门奖惩管理-类型
const hpSupervisorPerfDeptRewardType = [
    {
        value: 0,
        label: '惩罚'
    },
    {
        value: 1,
        label: '奖励'
    }
]
// 部门副职-绩效任务-部门奖惩管理-时间排序
const hpSupervisorPerfDeptRewardsSort = [
    {
        label: "正序",
        value: 0
    },
    {
        label: "倒序",
        value: 1
    },
]
// 部门副职-绩效任务-部门奖惩管理-审批状态
const hpSupervisorPerfDeptRewardStatus = [
    {
        label: "待审批",
        value: 0
    },
    {
        label: "已通过",
        value: 1
    },
    {
        label: "不通过",
        value: 2
    },
]

// 部门副职-绩效任务-工作报告审核-状态
const hpSupervisorPerfWorkReportAuditStatus = [
    {
        label: "未提交",
        value: 0
    },
    {
        label: "待审核",
        value: 1
    },
    {
        label: "审核通过",
        value: 2
    },
    {
        label: "驳回",
        value: 3
    }
]

// 部门副职-绩效考核-月度评分-排序
const hpSupervisorPerfMonthSort = [
    {
        label: "工号降序",
        value: 0
    },
    {
        label: "工号升序",
        value: 1
    }
]

// 部门副职-绩效考核-月度评分-状态
const hpSupervisorPerfMonthStatus = [
    {
        label: "待评分",
        value: 0
    },
    {
        label: "已评分",
        value: 1
    }
]

// 部门正职-绩效任务-工作报告审核-状态
const hpDeptPerfWorkReportAuditStatus = [
    {
        label: "未提交",
        value: 0
    },
    {
        label: "待审核",
        value: 1
    },
    {
        label: "审核通过",
        value: 2
    },
    {
        label: "驳回",
        value: 3
    }
]

// 部门正职-绩效任务-部门奖惩管理-类型
const hpDeptPerfDeptRewardType = [
    {
        value: 0,
        label: '惩罚'
    },
    {
        value: 1,
        label: '奖励'
    }
]
// 部门正职-绩效任务-部门奖惩管理-时间排序
const hpDeptPerfDeptRewardsSort = [
    {
        label: "正序",
        value: 0
    },
    {
        label: "倒序",
        value: 1
    },
]
// 部门正职-绩效任务-部门奖惩管理-审批状态
const hpDeptPerfDeptRewardStatus = [
    {
        label: "待审批",
        value: 0
    },
    {
        label: "已通过",
        value: 1
    },
    {
        label: "不通过",
        value: 2
    },
]

// 部门正职-绩效任务-公司奖惩审核-排序
const hpDeptPerfCompanySort = [
    {
        label: "时间倒序",
        value: 0
    },
    {
        label: "时间升序",
        value: 1
    }
]

// 部门正职-绩效任务-公司奖惩审核-状态
const hpDeptPerfCompanyStatus = [
    {
        label: "待审批",
        value: 0
    },
    {
        label: "已驳回",
        value: 1
    },
    {
        label: "已通过",
        value: 2
    },
    {
        label: '已删除',
        value: 3
    }
]

// 部门正职-绩效任务-公司奖惩审核-类型
const hpDeptPerfCompanyType = [
    {
        label: "惩罚",
        value: 0
    },
    {
        label: "奖励",
        value: 1
    }
]

// 部门正职-绩效考核-月度评分-状态
const hpDeptPerfMonthGradeStatus = [
    {
        label: "进行中",
        value: 0
    },
    {
        label: "已完成",
        value: 1
    }
]

// 部门正职-绩效考核-月度评分-评分状态
const hpDeptPerfMonthGradePeopleStatus = [
    {
        label: "待评分",
        value: 0
    },
    {
        label: "已评分",
        value: 1
    }
]

// 部门正职-绩效应用-优秀员工-排序
const hpDeptPerfOutstandStaffSort = [
    {
        label: "时间倒叙",
        value: 0
    },
    {
        label: "时间升序",
        value: 1
    }
]

// 绩效专员-绩效任务-部门配置-特殊人群
const hpPerfTaskConfigSpeciall = [
    {
        label: "安全员",
        value: 1
    },
    {
        label: "质量员",
        value: 2
    },
    {
        label: "考勤员",
        value: 3
    },
    {
        label: "绩效员",
        value: 4
    },
    {
        label: "设备员",
        value: 5
    }
]

// 绩效专员-绩效任务-部门配置-状态
const hpPerfTaskConfigStatus = [
    {
        label: "未启用",
        value: 0
    },
    {
        label: "已启用",
        value: 1
    }
]

// 绩效专员-绩效任务-绩效表管理-状态
const hpPerfTaskPerfFormStatus = [
    {
        label: "待发布",
        value: -1
    },
    {
        label: "待配置",
        value: 0
    },
    {
        label: "已启动",
        value: 1
    },
    {
        label: "审核失败",
        value: 2
    },
    {
        label: "已通过",
        value: 3
    }
]

// 绩效专员-绩效任务-绩效表管理-查看配置-状态
const hpPerfTaskPerfFormPeopleStatus = [
    {
        label: "待提交",
        value: 0
    },
    {
        label: "待一审",
        value: 1
    },
    {
        label: "待二审",
        value: 2
    },
    {
        label: "驳回",
        value: 3
    },
    {
        label: "通过",
        value: 4
    }
]

// 绩效专员-绩效任务-部门奖惩管理-类型
const hpPerfPerfDeptRewardType = [
    {
        value: 0,
        label: '惩罚'
    },
    {
        value: 1,
        label: '奖励'
    }
]
// 绩效专员-绩效任务-部门奖惩管理-时间排序
const hpPerfPerfDeptRewardsSort = [
    {
        label: "正序",
        value: 0
    },
    {
        label: "倒序",
        value: 1
    },
]
// 绩效专员-绩效任务-部门奖惩管理-审批状态
const hpPerfPerfDeptRewardStatus = [
    {
        label: "待审批",
        value: 0
    },
    {
        label: "已通过",
        value: 1
    },
    {
        label: "不通过",
        value: 2
    },
]

// 绩效专员-绩效任务-年终奖惩-排序
const hpPerfPerfYearEndRewardsSort = [
    {
        label: "正序",
        value: 0
    },
    {
        label: "倒序",
        value: 1
    }
]
// 绩效专员-绩效任务-年终奖惩-类型
const hpPerfPerfYearEndRewardsType = [
    {
        label: "惩罚",
        value: 0
    },
    {
        label: "奖励",
        value: 1
    }
]
// 绩效专员-绩效任务-考勤管理-排序
const hpPerfPerfAttendanceSort = [
    {
        label: "正序",
        value: 0
    },
    {
        label: "倒序",
        value: 1
    }
]

// 绩效专员-绩效任务-考勤管理-扣分类型
const hpPerfPerfAttendanceType = [
    {
        label: "惩罚",
        value: 0
    },
    {
        label: "奖励",
        value: 1
    }
]

// 绩效专员-绩效考核-月度评分-排序
const hpPerfPerfExamineSort = [
    {
        label: "分数降序",
        value: 0
    },
    {
        label: "分数升序",
        value: 1
    }
]

// 绩效专员-绩效考核-月度评分-状态
const hpPerfPerfExamineStatus = [
    {
        label: "进行中",
        value: 0
    },
    {
        label: "已完成",
        value: 1
    }
]

// 绩效专员-绩效考核-年终互评-排序
const hpPerfPerfYearScoreSort = [
    {
        label: "时间倒叙",
        value: 0
    },
    {
        label: "时间升序",
        value: 1
    }
]
// 绩效专员-绩效考核-年终互评-评分进度-排序
const hpPerfPerfYearScorePeopleSort = [
    {
        label: "总分降序",
        value: 0
    },
    {
        label: "总分升序",
        value: 1
    },
    {
        label: "工号降序",
        value: 2
    },
    {
        label: "工号升序",
        value: 3
    }
]
// 绩效专员-绩效应用-优秀员工-排序
const hpPerfPerfOutstandStaffSort = [
    {
        label: "时间倒叙",
        value: 0
    },
    {
        label: "时间升序",
        value: 1
    }
]

// 绩效专员-绩效应用-优秀员工-级别
const hpPerfPerfOutstandStaffLevels = [
    {
        label: "A",
        value: 'A'
    },
    {
        label: "B",
        value: 'B'
    },
    {
        label: "C",
        value: 'C'
    },
    {
        label: "D",
        value: 'D'
    },
]


// 公司奖惩-公司奖惩管理-排序
const hpCompanyRewardSort = [
    {
        label: "时间倒序",
        value: 0
    },
    {
        label: "时间升序",
        value: 1
    }
]

// 公司奖惩-公司奖惩管理-状态
const hpCompanyRewardStatus = [
    {
        label: "已删除",
        value: -1
    },
    {
        label: "待审批",
        value: 0
    },
    {
        label: "已驳回",
        value: 1
    },
    {
        label: "部门正职-已通过",
        value: 2
    },
    {
        label: "公司人力-已通过",
        value: 3
    }
]

// 公司奖惩-公司奖惩管理-状态
const hpCompanyRewardType = [
    {
        label: "惩罚",
        value: 0
    },
    {
        label: "奖励",
        value: 1
    }
]

// 公司奖惩-公司奖惩审批-排序
const hpCompanyAuditSort = [
    {
        label: "时间倒序",
        value: 0
    },
    {
        label: "时间升序",
        value: 1
    }
]

// 公司奖惩-公司奖惩审批-状态
const hpCompanyAuditStatus = [
    {
        label: "待审批",
        value: 0
    },
    {
        label: "已驳回",
        value: 1
    },
    {
        label: "已通过",
        value: 2
    },
    {
        label: '已删除',
        value: 3
    }
]

// 公司奖惩-公司奖惩审批-状态
const hpCompanyAuditSts = [
    {
        label: "驳回",
        value: 1
    },
    {
        label: '通过',
        value: 2
    }
]

// 公司奖惩-公司奖惩审批-类型
const hpCompanyAuditType = [
    {
        label: "惩罚",
        value: 0
    },
    {
        label: "奖励",
        value: 1
    }
]

// 公司人力-绩效任务-考勤管理-排序
const hpManPerfAttendanceSort = [
    {
        label: "正序",
        value: 0
    },
    {
        label: "倒序",
        value: 1
    }
]

// 公司人力-绩效考核-月度考核（评分）-排序
const hpManPerfMonthIndex = [
    {
        label: "时间倒叙",
        value: 0
    },
    {
        label: "时间升序",
        value: 1
    }
]

// 公司人力-绩效考核-月度考核（评分）-状态
const hpManPerfMonthStatus = [
    {
        label: "进行中",
        value: 0
    },
    {
        label: "已完成",
        value: 1
    }
]

// 公司人力-绩效考核-月度考核（评分）- 被打分人类型
const hpManPerfMonthType = [
    {
        label: "员工",
        value: 0
    },
    {
        label: "班组长",
        value: 1
    }
]

// 公司人力-绩效考核-月度考核（评分）- 排序
const hpManpowerPerfMonthSort = [
    {
        label: "时间倒叙",
        value: 0
    },
    {
        label: "时间升序",
        value: 1
    }
]

// 公司人力-绩效考核-年终互评-排序
const hpManpowerPerfYearScoreSort = [
    {
        label: "工号降序",
        value: 0
    },
    {
        label: "工号升序",
        value: 1
    }
]

// 公司人力-绩效考核-年终互评-展期日期
const hpManpowerPerfYearScoreData = [
    {
        label: "一周【7天】",
        value: 7
    },
    {
        label: "两周【14天】",
        value: 14
    },
    {
        label: "三周【21天】",
        value: 21
    },
    {
        label: "四周【28天】",
        value: 28
    }
]

// 公司人力-绩效考核-年终互评-展期日期
const hpManpowerPerfYearScorePeopleSort = [
    {
        label: "总分降序",
        value: 0
    },
    {
        label: "总分升序",
        value: 1
    },
    {
        label: "工号降序",
        value: 2
    },
    {
        label: "工号升序",
        value: 3
    }
]

// 公司人力-绩效考核-年度考核-排序
const hpManpowerPerfYearEvaluateSort = [
    {
        label: "时间倒叙",
        value: 1
    },
    {
        label: "时间升序",
        value: 2
    }
]

// 公司人力-绩效考核-年度考核-人员查看-排序
const hpManpowerPerfYearEvaluatePeopleSort = [
    {
        label: "综合分数降序",
        value: 0
    },
    {
        label: "综合分数升序",
        value: 1
    },
    {
        label: "员工工号降序",
        value: 2
    },
    {
        label: "员工工号升序",
        value: 3
    }
]

// 公司人力-绩效应用-优秀员工-排序
const hpManpowerPerfOutstandStaffSort = [
    {
        label: "年度降序",
        value: 0
    },
    {
        label: "年度升序",
        value: 1
    }
]


export default {
    hpUserDeptExceptStatus,
    hpUserDeptIgnoreOptions,
    hpCompanyRewardSort,
    hpCompanyRewardStatus,
    hpCompanyRewardType,
    hpCompanyAuditSort,
    hpCompanyAuditStatus,
    hpCompanyAuditType,
    hpCompanyAuditSts,
    hpDeptPerfCompanySort,
    hpDeptPerfCompanyStatus,
    hpDeptPerfCompanyType,
    hpManpowerPerfMonthSort,
    hpPerfTaskConfigSpeciall,
    hpPerfTaskConfigStatus,
    hpUserSystemSpecialstate,
    hpManPerfMonthIndex,
    hpManPerfMonthStatus,
    hpManPerfMonthType,
    hpPerfPerfExamineStatus,
    hpPerfPerfExamineSort,
    hpDeptPerfMonthGradeStatus,
    hpDeptPerfMonthGradePeopleStatus,
    hpSupervisorPerfMonthSort,
    hpSupervisorPerfMonthStatus,
    hpShiftPerfMonthGradeStatus,
    hpShiftPerfMonthGradePeopleStatus,
    hpShiftPerfMonthGradePeopleSort,
    hpForemanPerfMonthGradeStatus,
    hpForemanPerfMonthGradePeopleStatus,
    hpForemanPerfMonthGradePeopleSort,
    hpStaffPerfMonthGradeSort,
    hpStaffPerfMonthGradeStatus,
    hpManpowerPerfYearScoreSort,
    hpManpowerPerfYearScoreData,
    hpManpowerPerfYearScorePeopleSort,
    hpPerfPerfYearScoreSort,
    hpPerfPerfYearScorePeopleSort,
    hpStaffPerfYearScoreStatus,
    hpStaffPerfYearScoreSort,
    hpManpowerPerfYearEvaluateSort,
    hpManpowerPerfYearEvaluatePeopleSort,
    hpManpowerPerfOutstandStaffSort,
    hpPerfPerfOutstandStaffSort,
    hpPerfPerfOutstandStaffLevels,
    hpDeptPerfOutstandStaffSort,
    hpPerfPerfYearEndRewardsType,
    hpPerfPerfYearEndRewardsSort,
    hpPerfPerfAttendanceSort,
    hpStaffPerfWorkReportStatus,
    hpShiftPerfworkReportAuditStatus,
    hpForemanPerfWorkReportAuditStatus,
    hpSupervisorPerfWorkReportAuditStatus,
    hpDeptPerfWorkReportAuditStatus,
    hpPerfTaskPerfFormStatus,
    hpStaffPerfDeptRewardTimeIndex,
    hpStaffPerfDeptRewardStatus,
    hpStaffPerfDeptRewardType,
    hpStaffPerfRewardResultType,
    hpStaffPerfRewardResultTimeIndex,
    hpStaffPerfRewardResultStatus,
    hpShiftPerfDeptRewardType,
    hpShiftPerfDeptRewardsSort,
    hpShiftPerfDeptRewardStatus,
    hpForemanPerfDeptRewardType,
    hpForemanPerfDeptRewardsSort,
    hpForemanPerfDeptRewardStatus,
    hpSupervisorPerfDeptRewardType,
    hpSupervisorPerfDeptRewardsSort,
    hpSupervisorPerfDeptRewardStatus,
    hpDeptPerfDeptRewardType,
    hpDeptPerfDeptRewardsSort,
    hpDeptPerfDeptRewardStatus,
    hpPerfPerfDeptRewardType,
    hpPerfPerfDeptRewardsSort,
    hpPerfPerfDeptRewardStatus,
    hpManPerfAttendanceSort,
    hpPerfTaskPerfFormPeopleStatus,
    hpStaffPerfPerfFormStatus,
    hpPerfPerfAttendanceType,
    hpShiftPerfPerfFormStatus,
    hpShiftPerfFormAuditStatus,
    hpForemanPerfFormAuditStatus,
    hpSupervisorPerfPerfFormStatus,
    hpForemanPerfPerfFormStatus,
    hpSupervisorPerfFormAuditStatus,
    hpStaffPerfPerfFormTarget,
    hpShiftPerfPerfFormTarget,
    hpForemanPerfPerfFormTarget,
    hpSupervisorPerfPerfFormTarget,
}