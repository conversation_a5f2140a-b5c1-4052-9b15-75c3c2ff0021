<template>
    <el-dialog
        title="部套数量"
        :visible.sync="dialogVisible"
        @close="cancel"
        width="70%"
        >
            <div class="container">
                <el-form class="form" :inline="true" ref="form" :model="searchForm" label-width="80px">
                    <el-form-item class="form-item" label="搜索">
                        <el-input v-model="searchForm.query" size="small" placeholder="部套名/部套编码/所属模块"></el-input>
                    </el-form-item>
                    <el-form-item class="form-item">
                        <el-button
                        type="primary"
                        icon="el-icon-search"
                        size="mini"
                        @click="handleQuery(false)"
                        >搜索</el-button
                        >
                        <el-button icon="el-icon-refresh" size="mini" @click="handleQuery(true)"
                        >重置</el-button
                        >
                    </el-form-item>
                </el-form>

                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button
                        type="primary"
                        icon="el-icon-upload2"
                        size="mini"
                        plain
                        @click="handleExp"
                        >导出</el-button
                        >
                    </el-col>
                    <el-col class="hint-text">
                        第一行:计划完成时间，第二行:实际完成时间;第三行:未完成差值或完成差值
                    </el-col>
                </el-row>

                <div class="table-box">
                    <el-table
                    v-loading="loading"
                    height="70vh"
                    :data="tableData"
                    >
                        <el-table-column
                        type="selection"
                        width="55">
                        </el-table-column>
    
                        <el-table-column 
                        label="序号" 
                        align="center"
                        type="index">
                        </el-table-column>
    
                        <el-table-column 
                        label="部套编码" 
                        align="center"
                        prop="partCode">
                        </el-table-column>
    
                        <el-table-column 
                        label="部套名" 
                        align="center"
                        prop="partName">
                        </el-table-column>
    
                        <el-table-column 
                        label="所属模块" 
                        align="center"
                        prop="moduleName">
                        </el-table-column>
    
                        <el-table-column 
                        label="技术准备需求时间" 
                        align="center"
                        prop="technicalTime"
                        width="180"
                        >
                            <template slot-scope="{row}">
                                <p :class="row.technicalDays | filterStatus">{{row.technicalPlanTime | dateFormat | filterEmpty}}</p>
                                <p :class="row.technicalDays | filterStatus">{{row.technicalFinishTime | dateFormat | filterEmpty}}</p>
                                <p :class="row.technicalDays | filterStatus" v-if="row.technicalDays == 0 || row.technicalDays">{{row.technicalDays}}天</p>
                                <p v-else>-</p>
                            </template>
                        </el-table-column>
    
                        <el-table-column 
                        label="合同签订时间" 
                        align="center"
                        prop="contractPlanTime">
                            <template slot-scope="{row}">
                                <p :class="row.contractDays | filterStatus">{{row.contractPlanTime | dateFormat | filterEmpty}}</p>
                                <p :class="row.contractDays | filterStatus">{{row.contractFinishTime | dateFormat | filterEmpty}}</p>
                                <p :class="row.contractDays | filterStatus" v-if="row.contractDays == 0 || row.contractDays">{{row.contractDays}}天</p>
                                <p v-else>-</p>
                            </template>
                        </el-table-column>
    
                        <el-table-column 
                        label="采购到厂时间" 
                        align="center"
                        prop="purchaseTime">
                            <template slot-scope="{row}">
                                <p :class="row.purchaseDays | filterStatus">{{row.purchasePlanTime | dateFormat | filterEmpty}}</p>
                                <p :class="row.purchaseDays | filterStatus">{{row.purchaseFinishTime | dateFormat | filterEmpty}}</p>
                                <p :class="row.purchaseDays | filterStatus" v-if="row.purchaseDays == 0 || row.purchaseDays">{{row.purchaseDays}}天</p>
                                <p v-else>-</p>
                            </template>
                        </el-table-column>
    
                        <el-table-column 
                        label="生产完成时间" 
                        align="center"
                        prop="productTime">
                            <template slot-scope="{row}">
                                <p :class="row.productDays | filterStatus">{{row.productPlanTime | dateFormat | filterEmpty}}</p>
                                <p :class="row.productDays | filterStatus">{{row.productFinishTime | dateFormat | filterEmpty}}</p>
                                <p :class="row.productDays | filterStatus" v-if="row.productDays == 0 || row.productDays">{{row.productDays}}天</p>
                                <p v-else>-</p>
                            </template>
                        </el-table-column>
    
                        <el-table-column 
                        label="关注" 
                        align="center"
                        prop="pointStatus">
                            <template slot-scope="{row}">
                                <el-switch
                                v-model="row.pointStatus"
                                :active-value="1"
                                :inactive-value="0"
                                @change="changeStatus(row)"
                                >
                                </el-switch>
                            </template>
                        </el-table-column>
    
                    </el-table>
                </div>
            
            </div>
    </el-dialog>
</template>

<script>
import { amountSwitch } from '@/api/expectationAnalysis/expectationQuery'
export default {
  components:{},

  data(){
    return{
        dialogVisible:false,
        searchForm:{
            query:''
        },
        loading:false,
        tableData:[],
        listData:[],
    }
  },

  filters:{
    filterStatus(value){
        if(value === '' || value === undefined || value === null ){return ''}
        let num = Number(value)
        switch(true){
            case (num<=0):
                return 'danger';
            case (0<num && num<=5):
                return 'warning';
            case (5<num):
                return 'default';
        }
    },
    filterEmpty(value){
        return value ? value : '-' 
    }
  },

  methods:{
    init(tableData){
        this.tableData = JSON.parse(JSON.stringify(tableData))
        this.listData = JSON.parse(JSON.stringify(tableData))
        this.loading = false
        this.dialogVisible = true
    },
    // 导出
    handleExp(){},

    handleQuery(flag){
        if(flag){
            this.searchForm.query = ''
        }
        let query = this.searchForm.query
        this.tableData = this.listData.filter((item)=>{
            return item.partCode.includes(query) || item.partName.includes(query) || item.moduleName.includes(query)
        })

    },

    changeStatus(row){
        this.loading = true
        let params  = {
            id:row.id,
            status:row.pointStatus
        }
        amountSwitch(params).then((res)=>{
            this.$message({
                type:'success',
                message:'操作成功',
                duration:1500
            })
            this.loading = false
            this.$emit('loadData')
        })
    },

    cancel(){
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped>
.default {
  color:#67C23A;
}
.warning {
  color:#E6A23C;
}
.danger {
  color:#F56C6C;
}
.mb8 {
    display:flex;
}
.hint-text {
    text-align:right;
    color:#F56C6C;
    font-size:14px;
}
/* .table-box {
    height:20%;
    overflow-y:scroll;
} */
</style>