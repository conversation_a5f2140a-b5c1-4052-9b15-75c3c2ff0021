import request from '@/utils/request'

// 分页查询表单配置列表
export function formConfigList(query) {
    return request({
        url: `/flowable/workflow/form/list`,
        method:'get',
        params:query
    });
}

// 查询全部表单配置列表
export function formConfigListAll() {
    return request({
        url: `/flowable/workflow/form/allList`,
        method:'get',
    });
}


// 新增表单配置
export function addFormConfig(query) {
    return request({
        url: `/flowable/workflow/form`,
        method:'post',
        data:query
    });
}

// 修改表单配置
export function editFormConfig(query) {
    return request({
        url: `/flowable/workflow/form`,
        method:'put',
        data:query
    });
}

// 获取流程表单信息
export function getForm(formId) {
    return request({
        url: `/flowable/workflow/form/${formId}`,
        method:'get',
    });
}

// 删除
export function delForm(formId) {
    return request({
        url: `/flowable/workflow/form/${formId}`,
        method:'delete',
    });
}
