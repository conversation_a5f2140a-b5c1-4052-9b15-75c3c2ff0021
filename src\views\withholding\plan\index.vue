<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="机组" prop="posid" label-width="40px">
        <el-select v-model="queryParams.posid" placeholder="请选择" clearable>
          <el-option
            v-for="item in engineeringList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="分类" prop="asort" label-width="40px">
        <el-select v-model="queryParams.asort" placeholder="请选择" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->

      <el-form-item label="状态" prop="alarmStatus" label-width="40px">
        <el-select
          v-model="queryParams.alarmStatus"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="keyword" label-width="40px">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" /> -->

      <el-table-column label="大模块分类" prop="preConRd" />
      <el-table-column label="令号" prop="posid" />
      <el-table-column label="项目名称" prop="post1">
        <template slot-scope="scope">
          <a
            @click="item(scope.row)"
            style="color: rgb(24, 144, 255); cursor: pointer"
            >{{ scope.row.post1 }}</a
          >
        </template>
      </el-table-column>
      <el-table-column label="序号" prop="zps0102" />
      <el-table-column label="需要预提" prop="preNeed" />
      <el-table-column label="设计评审完成" prop="reviewRd" />
      <el-table-column label="零件数量" prop="total" />
      <el-table-column label="计划交期" prop="compDate" />
      <el-table-column label="完成情况" prop="alarmStatus">
        <template slot-scope="scope">
          <span
            style=" line-height: 28px;display: inline-block;width: 44px; height: 28px;font-size: 12px; font-weight: 400;text-align: center;"
            :class="scope.row.alarmStatus == 4 ?'finish':'unfinish'"
            >{{ scope.row.alarmStatus == 4 ? "已完成" : "未完成" }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="findPlanDList(scope.row)"
            >查看预提情况</el-button
          >
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:post:edit']"
            >设为已完成</el-button
          > -->
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:post:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="岗位名称" prop="postName">
          <el-input v-model="form.postName" placeholder="请输入岗位名称" />
        </el-form-item>
        <el-form-item label="岗位编码" prop="postCode">
          <el-input v-model="form.postCode" placeholder="请输入编码名称" />
        </el-form-item>
        <el-form-item label="岗位顺序" prop="postSort">
          <el-input-number
            v-model="form.postSort"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="岗位状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog> -->


    <el-dialog title="预提计划详情" center :visible.sync="findPlanDListOpen" width="90%" append-to-body>
      <el-table :data="planDList">
        <el-table-column label="图号" prop="zmmeTh" />
        <el-table-column label="图号名称" prop="zmmeThmc" />
        <el-table-column label="所属部套编码" prop="dwgNo" />
        <el-table-column label="所属部套名称" prop="dwgName" />
        <el-table-column label="进口" prop="imports" />
        <el-table-column label="直发" prop="grPower" />
        <el-table-column label="预提单号" prop="preNo" />
        <el-table-column label="类型" prop="preType" />
        <el-table-column label="序号" prop="preItem" />
        <el-table-column label="版次" prop="version" />
        <el-table-column label="是否全部完成" prop="zall" />
        <el-table-column label="预提日期" prop="predat" />
        <el-table-column label="状态" prop="preSt" />
        <el-table-column label="工厂" prop="werks" />
        <el-table-column label="物料" prop="matnr" />
        <el-table-column label="物料描述" prop="maktx" />
        <el-table-column label="设计数量" prop="rdqty" />
        <el-table-column label="工艺消耗定额" prop="gyqty" />
        <el-table-column label="基本计量单位" prop="meins" />
        <el-table-column label="需求日期" prop="bdter" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeTable">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 项目名称详情弹框 -->
    <el-dialog
      title="项目详情"
      :visible.sync="itemOpen"
      width="600px"
      append-to-body
    >
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>机组名称：<span>{{detailsList.post1}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>令号：<span>{{detailsList.posid}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>产品类型：<span>{{detailsList.prodTypeName}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>机组容量：<span>{{detailsList.usr04}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>合同签订日期：<span>{{detailsList.compDate}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>定类：<span>{{detailsList.projDl}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>所属电厂：<span>{{detailsList.powerPl}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>板块名称：<span>{{detailsList.dwgName}}</span></span>
          </div></el-col
        >
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPost,
  getPost,
  delPost,
  addPost,
  updatePost,
} from "@/api/system/post";
import { findAllPage, findPlanDList } from "../../../api/withholding/index";
import { getEngineeringList } from "@/api/items/items";
export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      detailsList:{},
      engineeringList:[], // 机组
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      planDList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 项目名称
      itemOpen: false,
      findPlanDListOpen: false,
      statusOptions: [
        { label: "报警", value: 1 },
        { label: "预警", value: 2 },
        { label: "进行中", value: 3 },
        { label: "已完成", value: 4 },
      ],
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        keyword: undefined,
        posid: undefined,
        asort: undefined,
        alarmStatus: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" },
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      findAllPage(this.queryParams).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加岗位";
    },
    /** 修改按钮操作 */
    findPlanDList(row) {
      findPlanDList({ wpId: row.wpId}).then(res=>{
        if (res.code == 1) {
          this.planDList = res.data
          this.findPlanDListOpen = true
        }
      })
    },
    closeTable() {
      this.planDList = []
      this.findPlanDListOpen = false;
    },
    /** 提交按钮 */
    // submitForm: function () {
    //   this.$refs["form"].validate((valid) => {
    //     if (valid) {
    //       if (this.form.postId != undefined) {
    //         updatePost(this.form).then((response) => {
    //           this.$modal.msgSuccess("修改成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       } else {
    //         addPost(this.form).then((response) => {
    //           this.$modal.msgSuccess("新增成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       }
    //     }
    //   });
    // },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    // 项目名称
    item(item) {
      this.detailsList=item
      this.itemOpen = true;
    },

    //机组列表
    getEngineeringList() {
      getEngineeringList(this.dataForm).then((res) => {
        res.data.forEach((element, index) => {
          this.engineeringList.push({
            value: element.posid,
            label: element.post1 + "(" + element.posid + ")",
          });
        });
      });
    },
  },
};
</script>
<style scoped>
::v-deep .el-dialog__body {
  line-height: 30px;
}
.unfinish {
  width: 44px;
  height: 28px;
  background: #f9f9f9;
  border-radius: 3px;
  border: 1px solid rgba(148, 149, 153, 0.4);
  font-size: 12px;
  font-weight: 400;
  color: #949599;
}
.finish {

  background: #e7faf0;
  border-radius: 3px;
  border: 1px solid rgba(101, 210, 153, 0.4);
color: #65D299;
}
</style>
