import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect"),
      },
    ],
  },
  {
    path: "/query/detail",
    component: () => import("../views/casting/query/detail.vue"),
    hidden: true,
  },
  {
    path: "/test",
    component: () => import("../views/test/index.vue"),
    hidden: true,
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/appauthUser/:id",
    component: () => import("../views/systems/SystemRole/authUser.vue"),
    hidden: true,
  },
  {
    path: "/houauthUser/:id",
    component: () => import("../views/systems/SystemRole/authUser.vue"),
    hidden: true,
  },
  {
    path: "/approle",
    component: () => import("@/views/systems/AppRole"),
    hidden: true,
  },
  {
    path: "/supplier",
    component: () => import("@/views/systems/supplier"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },

  {
    path: "",
    component: Layout,
    redirect: "index",
    children: [
      {
        path: "index",
        component: () => import("@/views/index"),
        name: "Index",
        meta: { title: "首页", icon: "dashboard", affix: true },
      },
      {
        path: "/cast/cast/nodeTemplate/nodeTemplateDetail",
        hidden: true,
        component: () =>
          import("../views/casting/nodeTemplate/nodeTemplateDetail.vue"),
        name: "Cast",
        meta: { title: "铸锻件节点模板详情" },
      },
      {
        path: "/riskControl/riskControl/projectRiskList",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/riskControl/projectRiskList/index.vue"),
        name: "ProjectRiskList",
        meta: { title: "项目风险列表" },
      },
      {
        path: "/riskControl/riskControl/peopleVindicate",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/riskControl/peopleVindicate/index.vue"),
        name: "peopleVindicate",
        meta: { title: "部门人员维护" },
      },
      {
        path: "/castingForging/castingForging/contract",
        component: Layout,
        hidden: true,
        component: () => import("../views/castingForging/contract/index.vue"),
        name: "contract",
        meta: { title: "铸锻件合同" },
      },
      {
        path: "/cast/cast/query/detail",
        component: Layout,
        hidden: true,
        component: () => import("../views/casting/query/detail.vue"),
        name: "Detail",
        meta: { title: "铸锻件节点记录管理" },
      },
      {
        path: "/hourole-auth/:roleId",
        component: Layout,
        hidden: true,
        component: () => import("@/views/systems/SystemRole/authUser"),
        name: "AuthUser",
        meta: { title: "分配用户" },
      },
      {
        path: "/approle-auth/:roleId",
        component: Layout,
        hidden: true,
        component: () => import("@/views/systems/AppRole/authUser"),
        name: "AuthUser",
        meta: { title: "分配用户" },
      },
      {
        path: "/smallTestExperiment/smallTestExperiment/experimentPlan",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/smallTestExperiment/experimentPlan/index.vue"),
        name: "experimentPlan",
        meta: { title: "检验实验计划" },
      },
      {
        path: "/smallTestExperiment/smallTestExperiment/processManagement",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/smallTestExperiment/processManagement/index.vue"),
        name: "processManagement",
        meta: { title: "工序管理" },
      },
      {
        path: "/smallTestExperiment/smallTestExperiment/smallMachineStatistics",
        component: Layout,
        hidden: true,
        component: () =>
          import(
            "../views/smallTestExperiment/smallMachineStatistics/index.vue"
          ),
        name: "smallMachineStatistics",
        meta: { title: "小机统计汇总" },
      },
      {
        path: "/smallTestExperiment/smallTestExperiment/personnel",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/smallTestExperiment/personnel/index.vue"),
        name: "personnel",
        meta: { title: "小机人员维护" },
      },

      // 大机
      {
        path: "/bigTestExperiment/bigTestExperiment/remoteSupervision",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/bigTestExperiment/remoteSupervision/index.vue"),
        name: "remoteSupervision",
        meta: { title: "远程监造" },
      },
      {
        path: "/bigTestExperiment/bigTestExperiment/nodeManagement",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/bigTestExperiment/nodeManagement/index.vue"),
        name: "nodeManagement",
        meta: { title: "节点管理" },
      },
      {
        path: "/bigTestExperiment/bigTestExperiment/bigMachineStatistics",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/bigTestExperiment/bigMachineStatistics/index.vue"),
        name: "bigMachineStatistics",
        meta: { title: "大机统计汇总" },
      },
      {
        path: "/bigTestExperiment/bigTestExperiment/personnel",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/bigTestExperiment/personnel/index.vue"),
        name: "personnel",
        meta: { title: "大机人员维护" },
      },

      {
        path: "/expectationAnalysis/expectationAnalysis/assemblyUnit",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/expectationAnalysis/assemblyUnit/index.vue"),
        name: "assemblyUnit",
        meta: { title: "部套管理" },
      },
      {
        path: "/expectationAnalysis/expectationAnalysis/modularity",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/expectationAnalysis/modularity/index.vue"),
        name: "modularity",
        meta: { title: "模块管理" },
      },
      {
        path: "/expectationAnalysis/expectationAnalysis/modelManager",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/expectationAnalysis/modelManager/index.vue"),
        name: "modelManager",
        meta: { title: "模型管理" },
      },
      {
        path: "/expectationAnalysis/expectationAnalysis/expectationQuery",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/expectationAnalysis/expectationQuery/index.vue"),
        name: "modelManager",
        meta: { title: "期量查询" },
      },

      // 模板管理
      // {
      //   path: '/productionPlan/productionPlan/productionModules',
      //   component: Layout,
      //   hidden: true,
      //   component: () => import('../views/productionPlan/productionModules/index.vue'),
      //   name: 'productionModules',
      //   meta: { title: '模板管理' }
      // },

      // 生产调度计划详情
      {
        path: "/productionPlan/productionPlan/productionPlanInfo",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/productionPlan/productionPlanInfo/index.vue"),
        name: "productionPlanInfo",
        meta: { title: "生产调度计划" },
      },

      // 厂商端生产调度计划
      {
        path: "/supplierProductionPlan/supplierProductionPlan/productionPlan",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/supplierProductionPlan/productionPlan/index.vue"),
        name: "productionPlan",
        meta: { title: "生产调度计划" },
      },
      // 供应商统计
      {
        path: "/statistics/projectStatistics/detail",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/statistics/projectStatistics/detail.vue"),
        name: "projectStatisticsDetail",
        meta: { title: "项目统计详情" },
      },
      // 供应商统计
      {
        path: "/productionScheduling/projectStatistics/detail",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/productionScheduling/projectStatistics/detail.vue"),
        name: "productionSchedulingDetail",
        meta: { title: "供应商统计详情" },
      },
      {
        path: "/statistics/statisticalOfThePersonInCharge/detail",
        component: Layout,
        hidden: true,
        component: () =>
          import(
            "../views/statistics/statisticalOfThePersonInCharge/detail.vue"
          ),
        name: "statisticalOfThePersonInChargeDetail",
        meta: { title: "负责人统计详情" },
      },
      {
        path: "/statistics/purchasingGroupStatistics/detail",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/statistics/purchasingGroupStatistics/detail.vue"),
        name: "purchasingGroupStatisticsDetail",
        meta: { title: "采购组统计详情" },
      },
      {
        path: "/statistics/contractStatistics/detail",
        component: Layout,
        hidden: true,
        component: () =>
          import("../views/statistics/contractStatistics/detail.vue"),
        name: "contractStatisticsDetail",
        meta: { title: "合同统计详情" },
      },
    ],
  },

  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "Profile",
        meta: { title: "个人中心", icon: "user" },
      },
    ],
  },
  // {
  //   path: "/system",
  //   component: Layout,
  //   name: "System",
  //   meta: { title: "系统管理", icon: "system" },
  //   children: [
  //     {
  //       path: "menu",
  //       name: "Menu",
  //       component: () => import("@/views/system/menu/index"),
  //       meta: {
  //         title: "菜单管理",
  //         icon: "tree-table",
  //         // 如果需要权限控制,可以添加以下权限标识
  //         permissions: ["system:menu:list"],
  //       },
  //     },
  //   ],
  // },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: Layout,
    hidden: true,
    // permissions: ['system:user:edit'],
    children: [
      {
        path: "role/:userId(\\d+)",
        component: () => import("@/views/system/user/authRole"),
        name: "AuthRole",
        meta: { title: "分配角色", activeMenu: "/system/user" },
      },
    ],
  },
  {
    path: "/system/role-auth",
    component: Layout,
    hidden: true,
    // permissions: ['system:role:edit'],
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/system/role/authUser"),
        name: "AuthUser",
        meta: { title: "分配用户", activeMenu: "/system/role" },
      },
    ],
  },

  // 用户系统APP角色分配
  {
    path: "/systems/AppRole-auth/",
    component: Layout,
    hidden: true,
    // permissions: ['systems:AppRole:edit'],
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/systems/AppRole/authUser"),
        name: "APPAuthUsers",
        meta: { title: "分配用户", activeMenu: "/systems/AppRole" },
      },
    ],
  },

  // 用户体系系统角色分配
  {
    path: "/systems/SystemRole-auth/",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/systems/SystemRole/authUser"),
        name: "AuthUsers",
        meta: { title: "分配用户", activeMenu: "/systems/SystemRole" },
      },
    ],
  },

  {
    path: "/system/dict-data",
    component: Layout,
    hidden: true,
    // permissions: ['system:dict:list'],
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/system/dict/data"),
        name: "Data",
        meta: { title: "字典数据", activeMenu: "/system/dict" },
      },
    ],
  },
  {
    path: "/monitor/job-log",
    component: Layout,
    hidden: true,
    // permissions: ['monitor:job:list'],
    children: [
      {
        path: "index",
        component: () => import("@/views/monitor/job/log"),
        name: "JobLog",
        meta: { title: "调度日志", activeMenu: "/monitor/job" },
      },
    ],
  },
  {
    path: "/tool/gen-edit",
    component: Layout,
    hidden: true,
    // permissions: ['tool:gen:edit'],
    children: [
      {
        path: "index/:tableId(\\d+)",
        component: () => import("@/views/tool/gen/editTable"),
        name: "GenEdit",
        meta: { title: "修改生成配置", activeMenu: "/tool/gen" },
      },
    ],
  },
];

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch((err) => err);
};

export default new Router({
  mode: "hash", // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
});
