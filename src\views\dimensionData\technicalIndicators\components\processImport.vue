<template>
  <!-- 用户导入对话框 -->
  <el-dialog title="导入" :visible.sync="dialogVisible" width="400px" append-to-body>
    <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" v-if="dialogVisible"
      :action="upload.url" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress"
      :on-success="handleFileSuccess" drag>
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip text-center" slot="tip">
        <span>仅允许导入xls、xlsx格式文件。</span>
        <!-- <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
              > -->
      </div>
    </el-upload>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitFileForm" :loading="upload.isUploading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  components: {},

  data() {
    return {
      dialogVisible: false,
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { "X-Token": "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/sd-server/quality/import/032",
      },
    };
  },

  created() { },

  methods: {
    init() {
      this.dialogVisible = true;
      this.upload.isUploading = false;
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
    },
    submitFileForm() {
      this.$emit("dialog");
      this.dialogVisible = false;
    },
    /** 下载模板操作 */
    // importTemplate() {
    //   exportExcelTemplate().then((response) => {
    //     const url = window.URL.createObjectURL(new Blob([response]));
    //     const link = document.createElement("a");
    //     link.target = "_blank";
    //     link.href = url;
    //     link.setAttribute("download", "用户信息模板.xlsx");
    //     document.body.appendChild(link);
    //     link.click();
    //   });
    // },
  },
};
</script>

<style scoped lang="less"></style>
