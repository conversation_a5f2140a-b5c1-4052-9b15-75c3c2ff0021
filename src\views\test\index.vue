<template>
  <div class="app-container">
    <div class="map-container">
      <div id="mapChart" :style="{ width: '100%', height: '600px' }"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import provinceData from './provinceData'

export default {
  name: 'MapChart',
  data() {
    return {
      chart: null,
      currentMap: 'china',
      currentProvince: '',
      mapData: [
        { name: '北京', value: 100 },
        { name: '上海', value: 200 },
        { name: '广东', value: 300 },
        { name: '浙江', value: 150 },
        { name: '江苏', value: 180 }
      ]
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById('mapChart'))
      // 加载中国地图
      this.loadMapData('china')
    },

    // 加载地图数据
    async loadMapData(mapName) {
      try {
        let url
        if (mapName === 'china') {
          url = 'https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json'
        } else {
          // 获取省份的行政编码
          const provinceCode = provinceData[mapName]
          if (!provinceCode) return
          url = `https://geo.datav.aliyun.com/areas_v3/bound/${provinceCode}_full.json`
        }

        const response = await fetch(url)
        const geoJson = await response.json()

        // 注册地图数据
        echarts.registerMap(mapName, geoJson)
        this.renderMap(mapName)

        // 添加点击事件
        this.chart.on('click', params => {
          if (mapName === 'china') {
            const provinceName = params.name
            this.loadMapData(provinceName)
          }
        })
      } catch (error) {
        console.error('加载地图数据失败:', error)
      }
    },

    renderMap(mapName) {
      const option = {
        backgroundColor: '#fff',
        title: {
          text: mapName === 'china' ? '中国地图' : `${mapName}地图`,
          left: 'center',
          subtext: mapName !== 'china' ? '点击空白处返回全国地图' : ''
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}<br/>数值：{c}'
        },
        visualMap: {
          min: 0,
          max: 300,
          left: 'left',
          top: 'bottom',
          text: ['高', '低'],
          calculable: true,
          inRange: {
            color: ['#C6FFDD', '#FBD786', '#f7797d']
          }
        },
        series: [{
          name: '数据分布',
          type: 'map',
          map: mapName,
          roam: true,
          label: {
            show: true
          },
          data: this.getCityData(mapName),
          itemStyle: {
            borderColor: '#666',
            areaColor: '#eee'
          },
          emphasis: {
            itemStyle: {
              areaColor: '#66ccff'
            }
          }
        }]
      }

      this.chart.setOption(option, true)

      // 添加返回按钮点击事件
      if (mapName !== 'china') {
        this.chart.getZr().on('click', params => {
          if (!params.target) {
            this.loadMapData('china')
          }
        })
      }
    },

    getCityData(mapName) {
      if (mapName === 'china') {
        return this.mapData
      }
      // 这里返回省份的城市数据
      return [
        { name: '杭州市', value: 100 },
        { name: '宁波市', value: 80 },
        { name: '温州市', value: 70 }
      ]
    }
  }
}
</script>

<style scoped>
.map-container {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
