<template>
    <el-dialog
    title="修改节点"
    :visible.sync="dialogVisible"
    width="950px"
    @close="cancel">
        <el-button type="primary" plain @click="add" icon="el-icon-plus" style="margin-bottom:20px">新增节点</el-button>
        <el-form ref="form"  class="demo-ruleForm" :inline="true">
            <div v-for="(item,index) in partNodeList" :key="index">
                <el-form-item label="节点名称" label-width="80px">
                   <el-input :disabled="item.nodeStatus == 0?false:true" v-model="item.nodeName" placeholder="请输入节点名称" clearable style="width:160px"></el-input>
                </el-form-item>
                <el-form-item label="节点计划时间" label-width="120px">
                    <el-date-picker
                    :disabled="item.nodeStatus == 0?false:true"
                    style="width:160px"
                    v-model="item.planTime"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="请选择时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="节点顺序" label-width="90px">
                    <el-input :disabled="item.nodeStatus == 0?false:true" v-model="item.nodeType" placeholder="请输入节点顺序" clearable style="width:160px"></el-input>
                </el-form-item>
                <el-button v-if="item.nodeStatus == 0" type="danger" plain @click="del(index)">删除</el-button>
            </div>
        </el-form>

        <span slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { detailNodeInfo,editMesNode } from '@/api/suppilerProductionPlan/productionPlan'
export default {
  components:{},

  data(){
    return{
        dialogVisible:false,
        btnLoading:false,
        rowData:null,
        partNodeList:[],
    }
  },

  methods:{
    init(row){
        this.rowData = row
        this.dialogVisible = true
        this.btnLoading = false
        this.loadData()
    },
    loadData(){
        detailNodeInfo(this.rowData.partId).then((res)=>{
            this.partNodeList = res.data.partNodeList || []
        }).catch(()=>{
            
        })
    },
    add(){
        this.partNodeList.push({nodeName:'',planTime:'',nodeType:'',nodeStatus:0})
    },
    del(index){
        this.partNodeList.splice(index, 1)
    },
    confirm(){
        this.partNodeList.forEach((item,index)=>{
            if(item.planTime){
                this.partNodeList[index]['planTimeStr'] = item.planTime.split('T')[0]
            }
            delete this.partNodeList[index].planTime
        })
        let params = {
            nodeList:this.partNodeList,
            partId:this.rowData.partId,
            type:2,
        }
        this.btnLoading = true
        editMesNode(params).then((res)=>{
            this.$message({
                type:'success',
                message:'操作成功',
                duration:1500
            })
            this.$emit('loadData')
            this.btnLoading = false
            this.dialogVisible = false
        }).catch(()=>{
            this.btnLoading = false
        })
    },
    cancel(){
        this.partNodeList = []
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped lang='scss'>
</style>