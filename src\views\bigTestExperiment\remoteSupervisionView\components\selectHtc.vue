<template>
  <div>
    <el-dialog
      title="指定HTC"
      :visible.sync="dialogVisible"
      append-to-body
      @close="cancel"
      width="600px"
    >
      <el-form ref="form" :model="form">
        <el-form-item
          prop="htcId"
          :rules="[{ required: true, message: '请选择htc', trigger: 'blur' }]"
        >
          <el-select v-model="form.htcId" placeholder="请选择" clearable filterable @change="changeRadio">
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.name"
              :value="item.userId">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { getHtcUserList } from "@/api/bigTestExperiment/personnel.js";
export default {
  data() {
    return {
      personShow: false,
      title: "",
      formLabelWidth: "120px",
      btnLoading: false,
      dialogVisible: false,
      userList: [],
      planId: "",
      form: {
        htcId: "",
        htcName: "",
      },
    };
  },
  methods: {
    //重置
    rest() {},
    //初始化
    init(form) {
      this.dialogVisible = true;
      this.form.htcId = form.htcId;
      this.form.htcName = form.htcUserName;
      this.getHtcUserList();
    },

    getHtcUserList() {
      getHtcUserList().then((res) => {
        this.userList = res.data;
      }).catch(() => {});
    },

    changeRadio(val) {
      let options = this.userList.find((item)=>{
        return item.userId === val
      })
      this.form.htcName = options ? options.name : '';
    },

    // 确定
    confirm() {
      this.$refs['form'].validate((valid) => {
          if(valid){
            this.$emit('setHtc',this.form)
            this.dialogVisible = false
          }
      })
    },

    cancel(){
      this.$refs['form'].resetFields()
      this.dialogVisible = false
    }
  },
};
</script>
<style scoped lang="scss">
.btn-box {
  margin-bottom: 8px;
  padding-left: 36px;
  display: flex;
  align-items: center;
  .btn-label {
    width: 85px;
  }
}

.dialog-content {
  height: 60vh;
  display: flex;
  justify-content: space-between;
  .content {
    width: 30%;
  }
  .block {
    flex: 1;
    padding: 8px;
    overflow-y: scroll;
    .timeline-item-top {
      display: flex;
      justify-content: space-between;
      background-color: #e3f0ff;
      padding: 4px 12px;
      border-radius: 4px;
      .top-right {
        font-size: 12px;
        color: #666;
        text-align: right;
      }
      .top-title {
        font-size: 16px;
        color: #1890ff;
        font-weight: 700;
      }
      .top-content {
        font-size: 12px;
        color: #666;
        :nth-child(2) {
          margin-left: 24px;
        }
      }
    }
    .timeline-item-card {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 8px;
      background-color: #f6f7f9;
      padding: 4px 12px;
      border-radius: 4px;
      .card-left {
        display: flex;
        flex-direction: column;
        .card-address {
          font-size: 12px;
          color: #85929b;
        }
        .img-box {
          width: 100%;
          display: flex;
          img {
            width: 100px;
            height: 100px;
            margin-right: 12px;
          }
        }
      }
    }
  }
}
</style>
