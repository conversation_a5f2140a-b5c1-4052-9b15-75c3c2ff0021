import request from "@/utils/request";
// 分页查询
export function apiGetList(data) {
  return request({
    url: "/sd-server/bid",
    method: "get",
    params: data,
  });
}
// 模板下载
export function apiGetTemplateList(data) {
  return request({
    url: "/sd-server/bid/template",
    method: "get",
    params: data,
    responseType: 'blob'
  });
}
// 新增
export function apiAdd(data) {
  return request({
    url: "/sd-server/bid",
    method: "post",
    data: data,
  });
}
// 修改
export function apiUpdate(data) {
  return request({
    url: "/sd-server/bid",
    method: "put",
    data: data,
  });
}
// 删除
export function apiDelete(data) {
  return request({
    url: "/sd-server/bid",
    method: "delete",
    data: data,
  });
}
