<template>
  <el-dialog title="修改查看" :visible.sync="dialogVisible" width="700px" top="30px" :before-close="handleClose">
    <!-- 基本信息部分 -->
    <div class="info-section">
      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">提交时间：</span>
            <span class="info-content">{{ detail.crtTime || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">招标编号：</span>
            <span class="info-content">{{ detail.bidNo || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">招标项目名称：</span>
            <span class="info-content">{{ detail.bidName || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">分类编号：</span>
            <span class="info-content">{{ detail.typeNo || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">供方编码：</span>
            <span class="info-content">{{ detail.supplierCode || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">供方编号：</span>
            <span class="info-content">{{ detail.supplierNo || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">供方名称：</span>
            <span class="info-content">{{ detail.supplierName || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">投标报价：</span>
            <span class="info-content">{{ detail.bidPrice || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">价格排名：</span>
            <span class="info-content">{{ detail.ranking || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">是否中标：</span>
            <span class="info-content">{{ detail.bidWin ? '是' : '否' || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">不参加/相应：</span>
            <span class="info-content">{{ detail.bidScore || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">是否邀标：</span>
            <span class="info-content">{{ detail.bidInvite ? '是' : '否' || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">参与招标得分：</span>
            <span class="info-content">{{ detail.participateScore || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">检查员：</span>
            <span class="info-content">{{ detail.inspector || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">标书响应：</span>
            <span class="info-content">{{ detail.bidScore || '-' }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 修改记录部分 -->
    <div class="history-section">
      <div class="history-title">修改记录</div>
      <div class="history-list">
        <div v-for="(item, index) in historyList" :key="index" class="history-item">
          <div class="history-time">{{ item.crtTime }}</div>
          <div class="history-content">
            <div class="history-row">
              <div class="history-info-group">
                <span class="info-item">
                  <label>操作人：</label>
                  <span>{{ item.crtUserName }}</span>
                </span>
              </div>
              <div class="history-info-group">
                <span class="info-item">
                  <label>状态：</label>
                  <span>{{ { 1: '修改', 2: '删除' }[item.type] }}</span>
                </span>
              </div>
              <div class="history-info-group">
                <span class="info-item">
                  <label>{{ { 2: '删除', 1: '改分' }[item.type] }}理由：</label>
                  <span>{{ item.reason }}</span>
                </span>
                <span class="info-item">
                  <label>罚分：</label>
                  <span>{{ item.scoreChange }} </span>
                </span>
              </div>
            </div>
            <div class="history-file">
              <label>证明文件：</label>
              <el-link type="primary" @click="downloadFile(it.newName)" v-for="(it, index) in item.fileList"
                :key="index" style="margin-right: 60px;">{{ it.name
                }}</el-link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { apiGetHistory } from '@/api/dimensionData/qualityIndicator'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: [Object],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      detail: {},
      historyList: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getDetail()
      }
    }
  },
  methods: {
    async getDetail() {
      try {
        this.loading = true
        const { data } = await apiGetHistory(this.row)
        this.detail = this.row
        this.historyList = data
      } catch (error) {
        this.$message.error('获取历史记录失败')
      } finally {
        this.loading = false
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    downloadFile(file) {
      // 实现文件下载逻辑
      window.open(process.env.VUE_APP_FILE_API + file, '_blank')
    }
  }
}
</script>

<style scoped>
.history-section {
  margin-top: 20px;
  border-top: 1px solid #EBEEF5;
  padding-top: 20px;
}

.history-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #409EFF;
}

.history-list {
  max-height: 250px;
  overflow-y: auto;
  padding-right: 10px;
}

.history-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px dashed #EBEEF5;
}

.history-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.history-time {
  font-weight: bold;
  margin-bottom: 8px;
}

.history-content {
  padding-left: 20px;
}

.history-row {
  display: flex;
  align-items: flex-start;
  gap: 48px;
}

.history-info-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-info-group:nth-child(1) {
  min-width: 150px;
}

.history-info-group:nth-child(2) {
  min-width: 120px;
}

.history-info-group:nth-child(3) {
  flex: 1;
}

.info-item {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.info-item label {
  color: #909399;
  margin-right: 4px;
}

.history-file {
  margin-top: 8px;
  display: flex;
  align-items: center;
}

.history-file label {
  color: #909399;
  margin-right: 4px;
}

.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-thumb {
  background-color: #E4E7ED;
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-track {
  background-color: #F5F7FA;
}
</style>
