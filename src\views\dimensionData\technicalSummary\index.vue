<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年份" prop="year">
        <el-date-picker v-model="queryParams.year" format="yyyy年" value-format="yyyy" type="year" placeholder="选择年">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="待审核" value="0"></el-option>
          <el-option label="已完成" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="query">
        <el-input v-model="queryParams.query" placeholder="请输入" clearable @keyup.enter.native="handleQuery"
          style="width: 200px" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery"
          style="margin-left: 8px">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery" style="margin-left: 8px">重置</el-button>
      </el-form-item>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>

    </el-form>

    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange" max-height="600">
      <el-table-column label="序号" type="index" width="120">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="公司名称" prop="supplierName" width="300" align="center" />
      <el-table-column label="公司编号" prop="supplierNo" width="150" />
      <el-table-column label="分类编号" prop="typeNo" width="150" />
      <el-table-column label="研发投入分数" prop="researchScore" width="150" />
      <el-table-column label="技术水平分数" prop="effectScore" width="150" />
      <el-table-column label="审批进度" prop="auditProgress" width="150" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleOperation(scope.row, 'view')">{{ scope.row.auditProgress
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="submitStatus" width="100" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.submitStatus === 0" type="warning" effect="plain">待审核</el-tag>
          <el-tag v-if="scope.row.submitStatus === 1" type="success" effect="plain">已完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-view" @click="handleOperation(scope.row, 'view')">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <add ref="addDialog" @refresh="resetQuery"></add>
    <edit ref="editDialog" @refresh="resetQuery"></edit>
  </div>
</template>

<script>
import add from "./components/add.vue";
import edit from "./components/edit.vue";
import {
  getTechDataPageSupplier,
  getTechDataOpen,
} from "@/api/dimensionData/technicalSummary.js";

export default {
  components: {
    add,
    edit,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 查询参数
      dateRange: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: undefined,
        status: undefined, //状态
        year: undefined, // 年份
      },
      //部门树
      deptTree: [],
      //机组详情
      posidList: {},
      year: '',//开启研发上传年份
      openUploadPopover: false,
      techDataButtonShow: '',//开启研发上传按钮显示权限
      userInfo: {},
    };
  },
  created() {
    this.queryParams.year = new Date(Date.now()).getFullYear().toString()
    // getDeptTree().then(res => {
    //   this.deptTree = res.data
    // })
    this.userInfo = JSON.parse(localStorage.getItem('userInfo'))
    this.queryParams.userType = this.userInfo.userType
    // getTechDataButton({ userType: this.userInfo.userType }).then(res => {
    //   this.techDataButtonShow = res.data
    // })
    this.getList();
  },
  methods: {
    viewFiles(files) {
      console.log(files)
    },
    cascaderChange(event) {
      if (event && event.length >= 1) {
        this.queryParams.deptNo = event[event.length - 1]
      } else {
        this.queryParams.deptNo = undefined
      }
    },
    //开启研发上传
    openUpload() {
      if (!this.year) {
        this.$message.error('请选择年份');
        return
      }
      getTechDataOpen({ year: Number(this.year) }).then(res => {
        this.openUploadPopover = false
      })
    },
    /** 分页查询 */
    getList() {
      this.loading = true;
      getTechDataPageSupplier(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    // 处理新增/修改/查看操作
    handleOperation(row) {
      this.$router.push({ path: '/dimensionData/technicalIndicators/dimensionData/technologyIndicator', query: { row } });
    },
    /** 新增按钮操作 */
    handleAdd() {

      this.$refs.addDialog.show(null, 'add');
    },
    /** 查看草稿操作 */
    handleViewDraft() {
      this.$refs.addDialog.loadDraft();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  line-height: 36px;
}

.view-icon {
  color: #1890FF; // 更改为浅蓝色
  font-size: 18px; // 稍微调大一点
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

.item-center {
  display: flex;
  align-items: center;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.font-size-14 {
  font-size: 14px;
}

.grey {
  color: #999;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}

.openUploadPopoverBottomBox {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}
</style>
