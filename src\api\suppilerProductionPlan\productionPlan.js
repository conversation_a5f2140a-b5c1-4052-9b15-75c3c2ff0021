import request from "@/utils/request";
// 列表查询
export function supplierList(param) {
  return request({
    url: "/mes-server/part/detail/supplier/list",
    method: "get",
    params: param,
  });
}
export function generate(id) {
  return request({
    url: "/mes-server/part/detail/supplier/generate/" + id,
    method: "get",
  });
}
// 导入
export function detailUploadFile(param) {
  return request({
    url: "/mes-server/part/detail/upload/supplier/file",
    method: "post",
    data: param,
  });
}

// 详情
export function detailNodeInfo(detailId) {
  return request({
    url: `/mes-server/part/detail/node/${detailId}`,
    method: "get",
  });
}

// 详情-新增
export function detailNodeRecord(query) {
  return request({
    url: `/mes-server/part/detail/node/record`,
    method: "post",
    data: query,
  });
}

// 详情-点击完成
export function detailNodeNodeId(nodeId) {
  return request({
    url: `/mes-server/part/detail/node/${nodeId}`,
    method: "put",
  });
}
// 生产调度修改节点
export function editMesNode(query) {
  return request({
    url: `/mes-server/mes/node`,
    method: "put",
    data: query,
  });
}
// 详情-修改报警状态
export function modifyStatus(query) {
  return request({
    url: `/mes-server/mes/node/${query.id}/${query.status}`,
    method: "put",
  });
}
