<template>
     <el-dialog
    :title="rowData ? '修改':'新增'"
    :visible.sync="dialogVisible"
    width="50%"
    @close="cancel"
    >
        <div>
            <el-form :model="form" :rules="rules" ref="form" label-width="80px" class="demo-ruleForm">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="名称" prop="modelName">
                            <el-input v-model="form.modelName" placeholder="请输入名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="备注" prop="remarks">
                            <el-input v-model="form.remarks" type="textarea" :rows="3" placeholder="请输入内容"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>

        <span slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { addPartAmount,modifyPartAmount } from "@/api/productionPlan/productionModules.js";
export default {

  data(){
    return{
        dialogVisible:false,
        btnLoading:false,
        rowData:null,
        form:{
            modelName:'',
            remarks:'',
        },
        rules:{
            modelName:[
                { required: true, message: '请输入名称', trigger: 'blur' },   
            ],
        } 
    }
  },

  methods:{
    init(row){
        this.dialogVisible = true
        this.rowData = row
        this.btnLoading = false
        if(row){
            this.form = JSON.parse(JSON.stringify(row))
        }else{
            this.form = {
                modelName:'',
                remarks:'',
            }
        }
    },

    confirm(){
        this.$refs['form'].validate((valid) => {
            if(valid){
                this.btnLoading = true
                if(this.rowData){
                    let params = {
                        ...this.form
                    }
                    modifyPartAmount(params).then((res)=>{
                        this.$message({
                            type:'success',
                            message:'操作成功',
                            duration:1500
                        })
                        this.btnLoading = false
                        this.dialogVisible = false
                        this.$emit('loadData')
                    })
                }else{
                    let params = {
                        ...this.form
                    }
                    addPartAmount(params).then((res)=>{
                        this.$message({
                            type:'success',
                            message:'操作成功',
                            duration:1500
                        })
                        this.btnLoading = false
                        this.dialogVisible = false
                        this.$emit('loadData')
                    })     
                }
            }
        })
    },

    cancel(){
        this.$refs['form'].resetFields()
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped lang='less'>
</style>