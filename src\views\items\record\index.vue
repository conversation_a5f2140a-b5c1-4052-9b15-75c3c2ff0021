<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="机组" label-width="40px">
        <el-select v-model="queryParams.posid" placeholder="请选择" clearable>
          <el-option
            v-for="(dict,index) in engineeringList"
            :key="index"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="模块" label-width="40px">
        <el-select v-model="queryParams.moduleCode" placeholder="请选择" clearable>
          <el-option
            v-for="(dict,index) in moduleList"
            :key="index"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="状态" label-width="40px">
        <el-select v-model="queryParams.nodeStatus" placeholder="请选择" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="搜索">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>

    </el-form>

    <el-row :gutter="10" class="mb8">

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" width="55" type="index" />
      <el-table-column label="部套名称" prop="dwgName">
        <template slot-scope="scope">
          <a @click="items(scope.row)" style="color: #1890FF; cursor: pointer">{{
            scope.row.dwgName
          }}</a>
        </template>
        </el-table-column>
      <el-table-column label="部套编码" prop="dwgNo" />
      <el-table-column label="令号" prop="posid" />
       <el-table-column label="项目名称" prop="post1">
        <template slot-scope="scope">
          <a @click="item(scope.row)" style="color: #1890FF; cursor: pointer">{{
            scope.row.post1
          }}</a>
        </template>
      </el-table-column>
      <el-table-column label="节点名称" prop="nodeName" />
      <el-table-column label="记录内容" prop="record" />
      <el-table-column label="记录时间" prop="createTime" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleLook(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>当前节点：<span>{{nodeInfo.nodeName}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>规定完成时间：<span>{{nodeInfo.planTime}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>地址：<span>{{nodeInfo.address}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>备注：<span>{{nodeInfo.record}}</span></span>
          </div></el-col
        >
      </el-row>
       <el-row type="flex" class="row-bg" style="margin-top:10px">
        <div class="img_box">
          <img v-for="(item,index) in nodeInfo.imageList" :key="index" :src="item.newName" alt="" style="margin-right:10px">
        </div>
      </el-row>
    </el-dialog>

      <!-- 项目名称详情弹框 -->
    <el-dialog
      title="项目详情"
      :visible.sync="opens"
      width="600px"
      append-to-body
    >
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>机组名称：<span>榆能杨伙盘1#机组项目</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>令号：<span>118076</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>产品类型：<span>汽轮机</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>机组容量：<span>66</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>合同签订日期：<span>2022-05-30</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>定类：<span>A</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>所属电厂：<span>汕头华电发电有限公司</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>板块名称：<span>直发现场部套</span></span>
          </div></el-col
        >
      </el-row>
    </el-dialog>


     <!-- 部套详情 -->
    <el-dialog title="部套详情" :visible.sync="itemOpen" width="600px" append-to-body>
 <el-row type="flex" class="row-bg">
  <el-col :span="12"><div class="grid-content bg-purple"><span>部套名称：<span>高压转子备件</span></span></div></el-col>
  <el-col :span="12"><div class="grid-content bg-purple-light"><span>部套编码：<span>CCH01E.000.COA.HPM-6</span></span></div></el-col>
</el-row>

 <el-row type="flex" class="row-bg">
  <el-col :span="12"><div class="grid-content bg-purple"><span>分工：<span>生产部</span></span></div></el-col>
  <el-col :span="12"><div class="grid-content bg-purple-light"><span>项目经理：<span>汪洋</span></span></div></el-col>
</el-row>

 <el-row type="flex" class="row-bg">
  <el-col :span="12"><div class="grid-content bg-purple"><span>合同交期：<span>2022-05-30</span></span></div></el-col>
  <el-col :span="12"><div class="grid-content bg-purple-light"><span>MM：<span>A</span></span></div></el-col>
</el-row>

 <el-row type="flex" class="row-bg">
  <el-col :span="12"><div class="grid-content bg-purple"><span>上次排期：<span>2022-05-30</span></span></div></el-col>
  <el-col :span="12"><div class="grid-content bg-purple-light"><span>项目交期：<span>2022-05-30</span></span></div></el-col>
</el-row>
 <el-row type="flex" class="row-bg">
  <el-col :span="12"><div class="grid-content bg-purple"><span>进展情况：<span>生产组织</span></span></div></el-col>
  <el-col :span="12"><div class="grid-content bg-purple-light"><span>预计完成时间：<span>2022-05-30</span></span></div></el-col>
</el-row>
 <el-row type="flex" class="row-bg">
  <el-col :span="12"><div class="grid-content bg-purple"><span>未完原因：<span>有部分零部件未到货</span></span></div></el-col>
  <el-col :span="12"><div class="grid-content bg-purple-light"><span>状态：<span>未完成</span></span></div></el-col>
</el-row>
 <el-row type="flex" class="row-bg">
  <el-col :span="24"><div class="grid-content bg-purple"><span>状态说明：<span>生产部负责弯头预计5.30</span></span></div></el-col>
  <!-- <el-col :span="12"><div class="grid-content bg-purple-light"><span>状态：<span>未完成</span></span></div></el-col> -->
</el-row>
 <el-divider  >  </el-divider>


 <el-row type="flex" class="row-bg">
  <el-col :span="12"><div class="grid-content bg-purple"><span>机组名称：<span>榆能杨伙盘1#机组项目</span></span></div></el-col>
  <el-col :span="12"><div class="grid-content bg-purple-light"><span>令号：<span>118076</span></span></div></el-col>
</el-row>

<el-row type="flex" class="row-bg">
  <el-col :span="12"><div class="grid-content bg-purple"><span>容量：<span>88MW</span></span></div></el-col>
  <el-col :span="12"><div class="grid-content bg-purple-light"><span>产品号：<span>CCHK01</span></span></div></el-col>
</el-row>
<el-row type="flex" class="row-bg">
  <el-col :span="12"><div class="grid-content bg-purple"><span>定类：<span>A</span></span></div></el-col>
  <el-col :span="12"><div class="grid-content bg-purple-light"><span>模块名称：<span>生产直发-其他</span></span></div></el-col>
</el-row>

<el-row type="flex" class="row-bg">
  <el-col :span="12"><div class="grid-content bg-purple"><span>板块：<span>PBAM</span></span></div></el-col>
  <el-col :span="12"><div class="grid-content bg-purple-light"><span>模块编码：<span>HPM</span></span></div></el-col>
</el-row>
    </el-dialog>
  </div>
</template>

<script>
// import {
//   listPost,
//   getPost,
//   delPost,
//   addPost,
//   updatePost,
// } from "@/api/system/post";
import {getNodeList,nodeInfo} from '@/api/items/record'
import {
  getEngineeringList,
  getModuleList,
} from "@/api/items/items";
export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      nodeId:'',
      engineeringList:[],
      moduleList:[],
      nodeInfo:{},
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
        opens: false,
      itemOpen:false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        moduleCode: '',
        posid:'',
        nodeStatus:'',
        keyword:'',
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" },
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.nodeId = this.$route.query.nodeId || ''
    this.getModuleList(),
    this.getEngineeringList();
    this.getList();
  },
  methods: {
    //模块列表
    getModuleList() {
      getModuleList(this.dataList).then((res) => {
        res.data.forEach((element, index) => {
          this.moduleList.push({
            value: element.moduleCode,
            label: element.moduleName+"("+element.moduleCode+")",
          });
        });
      });
    },

    //机组列表
    getEngineeringList() {
      getEngineeringList(this.dataForm).then((res) => {
        res.data.forEach((element, index) => {
          this.engineeringList.push({
            value: element.posid,
            label: element.post1+"("+element.posid+")",
          });
        });
      });
    },

    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      let params = {
        ...this.queryParams,
        nodeId:this.nodeId
      }
      getNodeList(params).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加岗位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const postId = row.postId || this.ids;
      getPost(postId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改岗位";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
      // 项目名称弹出框
    item(){
  this.opens=true
},
// 部套名称
items(){
  this.itemOpen=true
},
// 查看
handleLook(row){
  this.title="记录详情"
  this.open=true
  this.ndoeInfo = {}
  nodeInfo(row.onrId).then((res)=>{
    this.nodeInfo = res.data
    this.nodeInfo.imageList.forEach((item)=>{
      item.newName = process.env.VUE_APP_IMAGE_API +item.newName
    })
    this.nodeInfo.planTime = row.planTime
    this.nodeInfo.nodeName = row.nodeName
    // process.env.VUE_APP_BASE_API
  })
},
  },
};
</script>
<style scoped>
::v-deep .el-dialog__body{
  line-height: 30px
}
.img_box >img {
  width:45px;
  height:45px;
}
</style>
