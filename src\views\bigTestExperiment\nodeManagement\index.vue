<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="搜索" prop="query">
        <el-input
          v-model="queryParams.query"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd()"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleImp()"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          :disabled="ids.length == 0"
          type="primary"
          plain
          icon="el-icon-check"
          size="mini"
          @click="openBatch()"
          >批量启用</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="序号" type="index" />
      <el-table-column label="产品名称" prop="productName" />
      <el-table-column label="产品编号" prop="productCode" />
      <el-table-column label="状态" prop="status" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            @change="switchChange(scope.row)"
            :active-value="1"
            :inactive-value="0"
          >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
        width="120"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleAdd(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template></el-table-column
      >
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <add ref="addDialog"></add>
    <process-import ref="processImport"></process-import>
  </div>
</template>

<script>
import add from "./components/add.vue";
import {
  getProductPage,
  delProductById,
  putProductStatus,
  putProductEnable,
} from "@/api/bigTestExperiment/nodeManagement.js";
import ProcessImport from "./components/processImport.vue";

export default {
  components: {
    add,
    ProcessImport,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: undefined, //模块名称
        moduleCode: undefined, //模块编码
        posid: undefined, //令号
        post1: undefined, //机组名称
        powerPl: undefined, //业主名称（电厂）
        startTime: undefined, //开始时间
        endTime: undefined, //结束时间
        status: undefined, //状态
      },
      //机组详情
      posidList: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 状态切换
    switchChange(row) {
      this.loading = true;
      putProductStatus({
        id: row.id,
        status: row.status,
      })
        .then((res) => {
          this.$modal.msgSuccess("操作成功");
          this.loading = false;
          this.getList();
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 批量启用
    openBatch() {
      this.loading = true;
      putProductEnable(this.ids)
        .then((res) => {
          this.$modal.msgSuccess("操作成功");
          this.loading = false;
          this.getList();
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 导入
    handleImp() {
      this.$refs.processImport.init();
    },

    /** 分页查询 */
    getList() {
      this.loading = true;
      getProductPage(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.$refs.addDialog.init(row);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delProductById(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
::v-deep .el-dialog__body {
  line-height: 36px;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}
.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
::v-deep .el-row--flex {
  margin-left: 22px;
}
::v-deep .el-dialog {
  margin-top: 30vh !important;
}
</style>
