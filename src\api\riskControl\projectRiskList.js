import request from '@/utils/request'
// 管理员分页列表
export function getRiskManagePage(param) {
  return request({
    url: '/mes-server/project/risk/manage/page',
    method: 'get',
    params: param,
  })
}

// 新增
export function addRisk(param) {
  return request({
    url: '/mes-server/project/risk/',
    method: 'post',
    data: param,
  })
}

// 删除
export function delRisk(id) {
  return request({
    url: `/mes-server/project/risk/manage/${id}`,
    method: 'delete',
  })
}

// 查询部门人员
export function getDutyUserList(param) {
  return request({
    url: '/mes-server/duty/user/list',
    method: 'get',
    params: param,
  })
}

// 下载模板
export function downloadRisk(param) {
  return request({
    url: '/mes-server/project/risk/import/download',
    method: 'get',
    params: param,
    responseType: 'blob',
  })
}

// 置顶
export function setSort(param) {
  return request({
    url: `/mes-server/project/risk/manage/sort/${param.id}/${param.val}`,
    method: 'put',
  })
}

// 下载模板
export function getProjectRisk(id) {
  return request({
    url: `/mes-server/project/risk/${id}`,
    method: 'get',
  })
}

// 部门审核
export function setDeptStatus(param) {
  return request({
    url: `/mes-server/project/risk/manage/dept/status`,
    method: 'put',
    data: param
  })
}

// 厂商审核
export function setSupplierStatus(param) {
  return request({
    url: `/mes-server/project/risk/manage/supplier/status`,
    method: 'put',
    data: param
  })
}

// 管理员审核
export function riskManageStatus(param) {
  return request({
    url: `/mes-server/project/risk/manage/status`,
    method: 'put',
    data: param
  })
}
