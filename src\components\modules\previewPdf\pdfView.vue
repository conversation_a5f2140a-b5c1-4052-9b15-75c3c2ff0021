<template>
    <div v-loading="loading" class="pdf-content">
        <div class="draw_code_content">
            <span class="draw_code">{{drawCode}}</span>
        </div>
        <pdf class="base_pdf" :src="pdfSrc" ref="pdf"></pdf>
    </div>
</template>

<script>
import pdf from "vue-pdf";
import {getFileUrl} from '@/api/publicReq.js'
export default {
    components:{pdf},
    data(){
        return {
            error:false,
            loading:false,
            fileId:null,
            drawCode:'',
            pdfSrc: null,
        }
    },
    methods:{
        // 校验文件
        checkError(pdfSrc) {
            let loadingTask = pdf.createLoadingTask(pdfSrc);
            try{
                loadingTask.promise.catch(()=>{
                this.$message({
                    type:'error',
                    message:'文件预览错误！',
                    duration:2000
                })
                this.error = true
                })
            }catch(e){

            }
        
        },
        getFileUrl(){
            this.loading = true
            getFileUrl(this.fileId).then((res)=>{
                this.pdfSrc = res.data
                this.checkError(this.pdfSrc)
                this.loading = false
            }).catch(()=>{
                this.loading = false
            })
        },
    },
    created(){
        this.fileId = this.$route.query.fileId
        this.drawCode = this.$route.query.drawCode
        this.getFileUrl()
    }
}
</script>
<style lang="less" scoped>
.pdf-content {
    height:100vh;
    width:100vw;
    overflow-y:scroll;
    .draw_code_content {
        padding:25px;
        box-sizing: border-box;
        .draw_code {
            font-size:25px;
            font-weight:500;
        }
    }
}
</style>
