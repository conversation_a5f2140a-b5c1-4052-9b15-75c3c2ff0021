import request from '@/utils/request'
// 铸锻件列表
export function getCastingPurchasePage(param) {
    return request({
        url: '/back-server/castingPurchase/web/casting/list',
        method: 'get',
        params: param,
    })
}

// 铸锻件详情
export function getCastingDetails(param) {
    return request({
        url: '/back-server/castingPurchase/web/castingDetails',
        method: 'get',
        params: param,
    })
}

// 铸锻件新增节点记录
export function addNodeRecord(param) {
    return request({
        url: '/back-server/castingNodeRecord/add',
        method: 'post',
        data: param,
    })
}

// 铸锻件修改节点完成状态
export function updateNodeStatus(param) {
    return request({
        url: '/back-server/castingNode/updateNodeStatus',
        method: 'get',
        params: param,
    })
}

//导出
export function exportData(query) {
    return request({
        url: '/back-server/castingPurchase/web/export/casting',
        method: 'get',
        params: query,
        responseType: 'blob', // important
    })
}

