@import "./flow-element-variables.less";
@import "~bpmn-js-token-simulation/assets/css/bpmn-js-token-simulation.css";
@import "~bpmn-js-token-simulation/assets/css/font-awesome.min.css";
@import "~bpmn-js-token-simulation/assets/css/normalize.css";
@import "~bpmn-js/dist/assets/diagram-js.css";
@import "~bpmn-js/dist/assets/bpmn-font/css/bpmn.css";
@import "~bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css";
@import "./process-designer.less";
@import "./process-panel.less";

@success-color: #4eb819;
@primary-color: #409EFF;
@warning-color: #E6A23C;
@danger-color: #F56C6C;
@cancel-color: #909399;

.process-viewer {
  position: relative;
  border: 1px solid #EFEFEF;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+') repeat!important;

  .success-arrow {
    fill: @success-color;
    stroke: @success-color;
  }

  .success-conditional {
    fill: white;
    stroke: @success-color;
  }

  .fail-arrow {
    fill: @warning-color;
    stroke: @warning-color;
  }

  .fail-conditional {
    fill: white;
    stroke: @warning-color;
  }

  .success.djs-connection {
    .djs-visual path {
      stroke: @success-color!important;
      marker-end: url(#sequenceflow-end-white-success)!important;
    }
  }

  .success.djs-connection.condition-expression {
    .djs-visual path {
      marker-start: url(#conditional-flow-marker-white-success)!important;
    }
  }

  .success.djs-shape {
    .djs-visual rect {
      stroke: @success-color!important;
      fill: @success-color!important;
      fill-opacity: 0.15!important;
    }

    .djs-visual polygon {
      stroke: @success-color!important;
    }

    .djs-visual path:nth-child(2) {
      stroke: @success-color!important;
      fill: @success-color!important;
    }

    .djs-visual circle {
      stroke: @success-color!important;
      fill: @success-color!important;
      fill-opacity: 0.15!important;
    }
  }

  .primary.djs-shape {
    .djs-visual rect {
      stroke: @primary-color!important;
      fill: @primary-color!important;
      fill-opacity: 0.15!important;
    }

    .djs-visual polygon {
      stroke: @primary-color!important;
    }

    .djs-visual circle {
      stroke: @primary-color!important;
      fill: @primary-color!important;
      fill-opacity: 0.15!important;
    }
  }

  .warning.djs-connection {
    .djs-visual path {
      stroke: @warning-color!important;
      marker-end: url(#sequenceflow-end-white-fail)!important;
    }
  }

  .warning.djs-connection.condition-expression {
    .djs-visual path {
      marker-start: url(#conditional-flow-marker-white-fail)!important;
    }
  }

  .warning.djs-shape {
    .djs-visual rect {
      stroke: @warning-color!important;
      fill: @warning-color!important;
      fill-opacity: 0.15!important;
    }

    .djs-visual polygon {
      stroke: @warning-color!important;
    }

    .djs-visual path:nth-child(2) {
      stroke: @warning-color!important;
      fill: @warning-color!important;
    }

    .djs-visual circle {
      stroke: @warning-color!important;
      fill: @warning-color!important;
      fill-opacity: 0.15!important;
    }
  }

  .danger.djs-shape {
    .djs-visual rect {
      stroke: @danger-color!important;
      fill: @danger-color!important;
      fill-opacity: 0.15!important;
    }

    .djs-visual polygon {
      stroke: @danger-color!important;
    }

    .djs-visual circle {
      stroke: @danger-color!important;
      fill: @danger-color!important;
      fill-opacity: 0.15!important;
    }
  }

  .cancel.djs-shape {
    .djs-visual rect {
      stroke: @cancel-color!important;
      fill: @cancel-color!important;
      fill-opacity: 0.15!important;
    }

    .djs-visual polygon {
      stroke: @cancel-color!important;
    }

    .djs-visual circle {
      stroke: @cancel-color!important;
      fill: @cancel-color!important;
      fill-opacity: 0.15!important;
    }
  }
}

.process-viewer .djs-tooltip-container, .process-viewer .djs-overlay-container, .process-viewer .djs-palette {
  display: none;
}
