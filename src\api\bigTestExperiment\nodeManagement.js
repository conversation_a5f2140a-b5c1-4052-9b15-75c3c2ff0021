import request from '@/utils/request'
// 工序管理分页
export function getProductPage(param) {
    return request({
        url: '/mes-server/bm/product',
        method: 'get',
        params: param,
    })
}

// 工序管理新增
export function addProduct(param) {
    return request({
        url: '/mes-server/bm/product',
        method: 'post',
        data: param,
    })
}

// 工序管理修改
export function putProduct(param) {
    return request({
        url: '/mes-server/bm/product',
        method: 'put',
        data: param,
    })
}

// 工序管理批量删除
export function delProductList(param) {
    return request({
        url: '/mes-server/bm/product',
        method: 'delete',
        data: param,
    })
}

// 工序管理删除
export function delProductById(id) {
    return request({
        url: `/mes-server/bm/product/${id}`,
        method: 'delete',
    })
}

// 工序管理开关修改
export function putProductStatus(param) {
    return request({
        url: `/mes-server/bm/product/${param.id}/switch/${param.status}`,
        method: 'put',
        data: param,
    })
}

// 批量启用
export function putProductEnable(param) {
    return request({
        url: `/mes-server/bm/product/enable`,
        method: 'put',
        data: param,
    })
}