<template>
  <div class="date-picker-container">
    <el-select v-model="dateType" placeholder="请选择" size="small" style="width: 110px; margin-right: 8px"
      @change="handleTypeChange" popper-class="date-select-dropdown">
      <el-option label="按年份" value="year"></el-option>
      <el-option label="按季度" value="quarter"></el-option>
      <el-option label="按月份" value="month"></el-option>
      <el-option label="按日期" value="custom"></el-option>
    </el-select>

    <div class="picker-content">
      <!-- 自定义日期范围 -->
      <div v-if="dateType === 'custom'" class="custom-date-wrapper">
        <el-date-picker v-model="dateRange" type="daterange" size="small" align="right" unlink-panels
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
          value-format="yyyy-MM-dd" @change="handleCustomDateChange" popper-class="date-picker-popper">
        </el-date-picker>
      </div>

      <!-- 年份范围选择 -->
      <template v-if="dateType === 'year'">
        <el-date-picker v-model="yearRange.start" type="year" size="small" placeholder="开始年份" value-format="yyyy"
          :picker-options="yearStartOptions" @change="handleYearStartChange" popper-class="date-picker-popper"
          class="date-picker-input">
        </el-date-picker>
        <span class="range-separator">至</span>
        <el-date-picker v-model="yearRange.end" type="year" size="small" placeholder="结束年份" value-format="yyyy"
          :picker-options="yearEndOptions" @change="handleYearChange" popper-class="date-picker-popper"
          class="date-picker-input">
        </el-date-picker>
      </template>

      <!-- 季度范围选择 -->
      <template v-if="dateType === 'quarter'">
        <div class="quarter-range">
          <div class="quarter-item">
            <el-date-picker v-model="quarterRange.startYear" type="year" size="small" placeholder="开始年份"
              value-format="yyyy" @change="handleQuarterStartYearChange" popper-class="date-picker-popper"
              class="year-picker">
            </el-date-picker>
            <el-select v-model="quarterRange.startQuarter" placeholder="季度" size="small" class="quarter-picker"
              @change="handleQuarterStartChange">
              <el-option v-for="quarter in quarterOptions" :key="quarter.value" :label="quarter.label"
                :value="quarter.value"></el-option>
            </el-select>
          </div>
          <span class="range-separator">至</span>
          <div class="quarter-item">
            <el-date-picker v-model="quarterRange.endYear" type="year" size="small" placeholder="结束年份"
              value-format="yyyy" :picker-options="quarterEndOptions" @change="handleQuarterChange"
              popper-class="date-picker-popper" class="year-picker">
            </el-date-picker>
            <el-select v-model="quarterRange.endQuarter" placeholder="季度" size="small" class="quarter-picker"
              @change="handleQuarterChange" :disabled="!quarterRange.endYear">
              <el-option v-for="quarter in getAvailableQuarters" :key="quarter.value" :label="quarter.label"
                :value="quarter.value"></el-option>
            </el-select>
          </div>
        </div>
      </template>

      <!-- 月份范围选择 -->
      <template v-if="dateType === 'month'">
        <el-date-picker v-model="monthRange.start" type="month" size="small" placeholder="开始月份" value-format="yyyy-MM"
          :picker-options="monthStartOptions" @change="handleMonthStartChange" popper-class="date-picker-popper"
          class="month-picker">
        </el-date-picker>
        <span class="range-separator">至</span>
        <el-date-picker v-model="monthRange.end" type="month" size="small" placeholder="结束月份" value-format="yyyy-MM"
          :picker-options="monthEndOptions" @change="handleMonthChange" popper-class="date-picker-popper"
          class="month-picker">
        </el-date-picker>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DateRangePicker',
  props: {
    value: {
      type: Array,
      default: null
    },
    useDefaultDate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    const now = new Date()
    const currentYear = now.getFullYear()
    const today = now.toISOString().split('T')[0]
    const startDate = `${currentYear}-01-01`
    console.log("this.value", this.value);

    const initialDateRange = this.value || (this.useDefaultDate ? [startDate, today] : null)

    return {
      dateType: 'custom',
      dateRange: initialDateRange,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      yearRange: {
        start: null,
        end: null
      },
      monthRange: {
        start: null,
        end: null
      },
      quarterRange: {
        startYear: null,
        startQuarter: null,
        endYear: null,
        endQuarter: null
      },
      quarterOptions: [
        { label: '一季度', value: 1 },
        { label: '二季度', value: 2 },
        { label: '三季度', value: 3 },
        { label: '四季度', value: 4 }
      ],
      yearStartOptions: {
        disabledDate: () => false
      },
      yearEndOptions: {
        disabledDate: time => {
          if (!this.yearRange.start) return false
          return time.getFullYear() < Number(this.yearRange.start)
        }
      },
      monthStartOptions: {
        disabledDate: () => false
      },
      monthEndOptions: {
        disabledDate: time => {
          if (!this.monthRange.start) return false
          const [startYear, startMonth] = this.monthRange.start.split('-')
          const currentDate = new Date(time)
          const currentYear = currentDate.getFullYear()
          const currentMonth = currentDate.getMonth() + 1
          return new Date(currentYear, currentMonth) < new Date(startYear, startMonth)
        }
      },
      quarterEndOptions: {
        disabledDate: time => {
          if (!this.quarterRange.startYear || !this.quarterRange.startQuarter) return false
          return time.getFullYear() < Number(this.quarterRange.startYear)
        }
      }
    }
  },
  computed: {
    getAvailableQuarters() {
      if (!this.quarterRange.startYear || !this.quarterRange.startQuarter || !this.quarterRange.endYear) {
        return this.quarterOptions
      }

      const startYear = Number(this.quarterRange.startYear)
      const endYear = Number(this.quarterRange.endYear)
      const startQuarter = this.quarterRange.startQuarter

      if (startYear === endYear) {
        // 如果是同一年，只返回大于等于开始季度的选项
        return this.quarterOptions.filter(q => q.value >= startQuarter)
      }

      if (startYear < endYear) {
        // 如果结束年份大于开始年份，返回所有季度选项
        return this.quarterOptions
      }

      return [] // 其他情况返回空数组
    }
  },
  created() {
    if (this.dateRange) {
      this.$nextTick(() => {
        this.handleCustomDateChange(this.dateRange)
      })
    }
  },
  watch: {
    value: {
      handler(newVal) {
        console.log("newVal", newVal);
        if (newVal === null && this.useDefaultDate) {
          this.dateRange = null
          return
        }
        this.dateRange = newVal
      },
      immediate: true
    },
    dateRange: {
      handler(newVal) {
        console.log("dateRange", newVal);
        if (newVal === null && this.useDefaultDate) {
          this.dateType = 'custom'
          const now = new Date()
          const currentYear = now.getFullYear()
          const today = now.toISOString().split('T')[0]
          const startDate = `${currentYear}-01-01`
          this.dateRange = [startDate, today]
          return
        }

        this.$emit('input', newVal)
        this.handleCustomDateChange(newVal)
      },
      immediate: true
    }
  },
  methods: {
    handleTypeChange(type) {
      if (this.useDefaultDate) {
        const now = new Date()
        const currentYear = now.getFullYear()
        const today = now.toISOString().split('T')[0]
        const startDate = `${currentYear}-01-01`
        this.dateRange = [startDate, today]
      } else {
        this.dateRange = null
      }

      this.yearRange = { start: null, end: null }
      this.monthRange = { start: null, end: null }
      this.quarterRange = {
        startYear: null,
        startQuarter: null,
        endYear: null,
        endQuarter: null
      }
    },
    handleYearStartChange(val) {
      this.yearRange.end = null // 清空结束年份，避免范围错误
      if (val) {
        this.yearEndOptions.disabledDate = time => time.getFullYear() < Number(val)
      }
    },
    handleMonthStartChange(val) {
      this.monthRange.end = null
      if (val) {
        const [startYear, startMonth] = val.split('-')
        this.monthEndOptions.disabledDate = time => {
          const currentDate = new Date(time)
          return new Date(currentDate.getFullYear(), currentDate.getMonth()) <
            new Date(Number(startYear), Number(startMonth) - 1)
        }
      }
    },
    handleQuarterStartYearChange(val) {
      this.quarterRange.endYear = null
      this.quarterRange.endQuarter = null
      if (val) {
        this.quarterEndOptions.disabledDate = time => time.getFullYear() < Number(val)
      }
    },
    handleQuarterStartChange() {
      if (this.quarterRange.endYear === this.quarterRange.startYear &&
        this.quarterRange.endQuarter < this.quarterRange.startQuarter) {
        this.quarterRange.endQuarter = null
      }
      this.handleQuarterChange()
    },
    handleCustomDateChange(val) {
      if (!val && this.useDefaultDate) {
        const now = new Date()
        const currentYear = now.getFullYear()
        const today = now.toISOString().split('T')[0]
        const startDate = `${currentYear}-01-01`
        val = [startDate, today]
      }

      this.$emit('date-change', {
        type: 'custom',
        dateRange: val,
        display: val ? `${val[0]} 至 ${val[1]}` : ''
      })
    },
    handleYearChange() {
      const { start, end } = this.yearRange
      if (!start || !end) return

      const dateRange = [
        `${start}-01-01`,
        `${end}-12-31`
      ]
      this.$emit('date-change', {
        type: 'year',
        dateRange,
        display: `${start}年 至 ${end}年`
      })
    },
    handleMonthChange() {
      const { start, end } = this.monthRange
      if (!start || !end) return

      const [startYear, startMonth] = start.split('-')
      const [endYear, endMonth] = end.split('-')
      const endDate = new Date(endYear, endMonth, 0).getDate()
      const dateRange = [
        `${start}-01`,
        `${end}-${endDate}`
      ]

      this.$emit('date-change', {
        type: 'month',
        dateRange,
        display: `${startYear}年${startMonth}月 至 ${endYear}年${endMonth}月`
      })
    },
    handleQuarterChange() {
      const { startYear, startQuarter, endYear, endQuarter } = this.quarterRange
      if (!startYear || !startQuarter || !endYear || !endQuarter) return

      const startMonth = (startQuarter - 1) * 3 + 1
      const endMonth = endQuarter * 3
      const endDate = new Date(endYear, endMonth, 0).getDate()

      const dateRange = [
        `${startYear}-${String(startMonth).padStart(2, '0')}-01`,
        `${endYear}-${String(endMonth).padStart(2, '0')}-${endDate}`
      ]

      this.$emit('date-change', {
        type: 'quarter',
        dateRange,
        display: `${startYear}年第${startQuarter}季度 至 ${endYear}年第${endQuarter}季度`
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.date-picker-container {
  display: inline-flex;
  align-items: center;
  position: relative;
}

.custom-date-wrapper {
  display: inline-block;
  position: relative;
}

.picker-content {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.quarter-range {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.quarter-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.range-separator {
  color: #606266;
  padding: 0 4px;
  font-size: 12px;
}

.year-picker {
  width: 100px !important;
}

.quarter-picker {
  width: 90px !important;
}

.month-picker {
  width: 120px !important;
}

.date-picker-input {
  width: 120px !important;

  :deep(.el-input__inner) {
    height: 28px;
    line-height: 28px;
    padding: 0 8px;
    font-size: 12px;
  }

  :deep(.el-input__icon) {
    line-height: 28px;
    height: 28px;
  }
}

:deep(.el-input__inner) {
  height: 28px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.el-range-editor.el-input__inner) {
  height: 28px;
  line-height: 28px;
}

:deep(.el-input__icon) {
  line-height: 28px;
}

/* 调整下拉框的样式 */
:deep(.el-select-dropdown__item) {
  padding: 0 8px;
  height: 28px;
  line-height: 28px;
  font-size: 12px;
}
</style>

<style>
.date-picker-popper {
  position: absolute !important;
  z-index: 2000 !important;
}

.el-picker-panel {
  position: absolute !important;
}

.el-picker-panel[x-placement^="bottom"] {
  margin-top: 5px !important;
}

.el-picker-panel[x-placement^="top"] {
  margin-bottom: 5px !important;
}

.date-select-dropdown {
  z-index: 2001 !important;
}
</style>
