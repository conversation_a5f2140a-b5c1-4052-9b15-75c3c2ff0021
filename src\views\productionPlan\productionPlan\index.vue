<template>
  <div class="app-container">
    <el-form :model="searchForm" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="130px">
      <el-form-item label="状态">
        <el-select v-model="searchForm.alarmStatus" clearable placeholder="请选择">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <!--
        <el-form-item label="类别">
          <el-select
            v-model="searchForm.type"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item> -->

      <el-form-item label="机组名称/令号">
        <el-input v-model="searchForm.projectName" placeholder="请输入机组名称/令号" clearable
          @keyup.enter.native="search(false)" />
      </el-form-item>
      <el-form-item label="供应商名称/编码">
        <el-input v-model="searchForm.supplier" placeholder="请输入供应商名称/编码" clearable
          @keyup.enter.native="search(false)" />
      </el-form-item>
      <el-form-item label="计划时间段">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>


      <el-form-item label="部套名称/部套编码">
        <el-input v-model="searchForm.dwgName" placeholder="请输入部套名称/部套编码" clearable
          @keyup.enter.native="search(false)" />
      </el-form-item>

      <el-form-item label="零件名称/图号">
        <el-input v-model="searchForm.bismtName" placeholder="请输入零件名称/图号" clearable
          @keyup.enter.native="search(false)" />
      </el-form-item>

      <el-form-item label="工艺路线">
        <el-input v-model="searchForm.query" placeholder="请输入工艺路线" clearable @keyup.enter.native="search(false)" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="search(false)">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="search(true)">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-upload2" size="mini" plain @click="handleDowload">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-upload action="#" class="margin-left" accept=".xlsx,.xls" :http-request="fileSuccess"
          :show-file-list="false" :before-upload="beforeUpload" :file-error="fileError">
          <el-button type="warning" icon="el-icon-download" size="mini" plain>导入</el-button>
        </el-upload>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="search"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column label="序号" type="index" />
      <el-table-column label="图号" prop="bismt" />
      <el-table-column label="零件名称" prop="bismtName" />
      <el-table-column label="部套名称" prop="dwgName" />
      <el-table-column label="部套编码" prop="dwgNo" />
      <el-table-column label="机组名称" prop="post1" width="" />
      <el-table-column label="令号" prop="posid" width="" />
      <el-table-column label="工艺路线" prop="processRoute" width="" />
      <el-table-column label="供应商" prop="currentNode" width="120" align="center">
        <template slot-scope="{row}">
          <div>
            {{ row.supplierName }}
            <p v-if="row.supplierNo">({{ row.supplierNo }})</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="零件数量" prop="dwgNum" width="" />
      <el-table-column label="计划时间" prop="planDate" width="">
        <template slot-scope="{row}">
          <div v-if="row.planDate">{{ row.planDate | dateFormat }}</div>
        </template>
      </el-table-column>
      <el-table-column label="完成时间" prop="reinDate" width="">
        <template slot-scope="{row}">
          <div v-if="row.reinDate">{{ row.reinDate | dateFormat }}</div>
        </template>
      </el-table-column>

      <el-table-column label="状态" prop="updateStatus">
        <template slot-scope="{row}">
          <el-tag v-if="String(row.alarmStatus)" :type="row.alarmStatus | filterStatus(statusOptions, 'type')">
            {{ row.alarmStatus | filterStatus(statusOptions, 'label') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="厂商最后一次操作记录" prop="nodeRecord" width="200" show-overflow-tooltip></el-table-column>
      <el-table-column label="完成的时间" prop="nodeFinishTime" width="200" show-overflow-tooltip />
      <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="270px">
        <template slot-scope="{row}">
          <!-- <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="oldHanldeInfo(row)"
              >工序</el-button
            > -->
          <el-button size="mini" type="text" icon="el-icon-view" @click="hanldeInfo(row)">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="hanldeSuppiler(row)">关联厂商</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleDate(row)">修改节点</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="searchForm.pageNum" :limit.sync="searchForm.pageSize"
      @pagination="search(false)" />
    <!-- 关联厂商 -->
    <relation-suppiler ref="relationSuppiler" @loadData="loadData"></relation-suppiler>

    <!-- 修改时间 -->
    <modify-date ref="modifyDate" @loadData="loadData"></modify-date>

    <plan-info ref="planInfo"></plan-info>
  </div>
</template>

<script>
import relationSuppiler from './components/relationSuppiler'
import modifyDate from './components/modifyDate'
import planInfo from './components/planInfo'
import uploadFile from '@/components/uploadFile/main'
import { partWebPage, detailUploadFile } from '@/api/productionPlan/productionPlan'
import { exportFile } from '@/utils/gloabUtile'
export default {
  components: { uploadFile, relationSuppiler, modifyDate, planInfo },

  data() {
    return {
      dateRange: null,

      showSearch: true,
      loading: false,
      searchForm: {
        query: '',
        projectName: '',
        dwgName: '',
        bismtName: '',
        // type:'',
        alarmStatus: '',
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      statusOptions: [
        {
          label: '报警',
          value: 1,
          type: 'danger'
        },
        {
          label: '预警',
          value: 2,
          type: 'warning'
        },
        {
          label: '正常',
          value: 3,
          type: 'default'
        },
        {
          label: '完成',
          value: 4,
          type: 'success'
        }
      ],
      typeOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '阀体',
          value: 1
        }
      ],
      tableData: []
    }
  },

  created() {
    if (this.$route.query.searchForm) {
      this.searchForm = this.$route.query.searchForm
    }
    this.loadData()
  },

  filters: {
    filterStatus(value, statusOptions, params) {
      let option = statusOptions.find((item) => {
        return item.value === value
      })
      return option[params]
    }
  },

  methods: {
    onDateChange(val) {
      console.log('日期变更事件:', val)
      if (val && val.dateRange) {
        this.searchForm.startTime = val.dateRange[0]
        this.searchForm.endTime = val.dateRange[1]
      } else {
        this.searchForm.startTime = undefined
        this.searchForm.endTime = undefined
      }
    },
    search(flag) {
      if (flag) {
        this.dateRange = false
        this.searchForm.pageNum = 1
        this.searchForm.query = ''
        this.searchForm.alarmStatus = ''
        this.searchForm.supplier = ''
        // this.searchForm.type = ''
        this.searchForm.projectName = ''
        this.searchForm.dwgName = ''
        this.searchForm.bismtName = ''
      }
      if (this.dateRange) {
        this.searchForm.startTime = this.dateRange[0]
        this.searchForm.endTime = this.dateRange[1]
      } else {
        this.searchForm.startTime = undefined
        this.searchForm.endTime = undefined
      }
      this.loadData()
    },

    oldHanldeInfo(row) {
      let searchForm = encodeURIComponent(JSON.stringify(this.searchForm))
      this.$router.push({ path: '/productionPlan/productionPlan/productionPlanInfo', query: { rowId: row.id, searchForm: searchForm } })
    },
    hanldeInfo(row) {
      this.$refs.planInfo.init(row)
    },

    hanldeSuppiler(row) {
      this.$refs.relationSuppiler.init(row)
    },

    handleDate(row) {
      this.$refs.modifyDate.init(row)
    },

    // 导出
    handleDowload() {
      exportFile('/mes-server/part/detail/export', this.searchForm)
    },

    // 上传文件前
    beforeUpload(file, callBack) {
      this.loading = true
    },

    // 上传成功
    fileSuccess(file) {
      let formData = new FormData()
      formData.append('file', file.file)
      detailUploadFile(formData).then((res) => {
        this.$message({
          type: 'success',
          message: '操作成功',
          duration: 1500
        })
        this.loadData()
      }).catch(() => {
        this.loading = false
      })

    },

    // 文件上传失败
    fileError(error) {
      this.loading = false
    },

    loadData() {
      this.loading = true
      let params = {
        ...this.searchForm
      }
      partWebPage(params).then((res) => {
        let resData = res.data || {}
        this.tableData = resData.records || []
        this.total = resData.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

  },

}

</script>

<style scoped lang='scss'></style>
