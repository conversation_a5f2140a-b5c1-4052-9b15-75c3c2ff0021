import Mock from 'mockjs'

// 获取详情
Mock.mock(/\/api\/delivery\/deduct\/detail.*/, 'get', () => {
  return {
    code: 200,
    data: {
      // 返回的模拟数据
    },
    message: 'success'
  }
})

// 修改罚分
Mock.mock(/\/api\/delivery\/deduct\/update/, 'post', {
  code: 200,
  message: 'success'
})

// 删除罚分
Mock.mock(/\/api\/delivery\/deduct\/delete/, 'post', {
  code: 200,
  message: 'success'
})

// 确认罚分
Mock.mock(/\/api\/delivery\/deduct\/confirm/, 'post', {
  code: 200,
  message: 'success'
})

// 上传文件
Mock.mock(/\/api\/delivery\/deduct\/upload/, 'post', {
  code: 200,
  data: {
    fileUrl: 'http://example.com/file.pdf'
  },
  message: 'success'
})
