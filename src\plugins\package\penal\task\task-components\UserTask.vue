<template>
  <div>
    <el-row>
      <h4><b>审批人设置</b></h4>
      <el-radio-group v-model="dataType" @change="changeDataType">
        <el-radio label="USERS">指定用户</el-radio>
        <el-radio label="ROLES">角色</el-radio>
        <el-radio label="DEPTS">部门</el-radio>
        <el-radio label="INITIATOR">发起人</el-radio>
<!--        <el-radio label="NODE">指点节点</el-radio>-->
      </el-radio-group>
    </el-row>
    <el-row>
      <div v-if="dataType === 'USERS'">
        <el-tag v-for="userText in selectedUser.text" :key="userText" effect="plain">
          {{ userText }}
        </el-tag>
        <div class="element-drawer__button">
          <el-button size="mini" type="primary" icon="el-icon-plus" @click="onSelectUsers()">添加用户</el-button>
        </div>
      </div>
      <div v-if="dataType === 'ROLES'">
        <el-select v-model="roleIds" multiple size="mini" placeholder="请选择 角色" @change="changeSelectRoles">
          <el-option v-for="item in roleOptions" :key="item.id" :label="item.roleName" :value="`ROLE${item.id}`">
          </el-option>
        </el-select>
      </div>
      <div v-if="dataType === 'DEPTS'">
        <el-cascader class="select_cascader" ref="elCascader" v-model="deptIds" :options="deptOptions"
          :props="cascaderProps" collapse-tags @change="checkedDeptChange" clearable>
        </el-cascader>
      </div>
      <div v-if="dataType === 'NODE'">
        <el-input v-model="customizeNodeId" placeholder="请输入节点ID" clearable style="width: 240px" />
      </div>
    </el-row>
    <el-row>
      <div v-show="showMultiFlog">
        <el-divider />
        <h4><b>多实例审批方式</b></h4>
        <el-row>
          <el-radio-group v-model="multiLoopType" @change="changeMultiLoopType">
            <el-row><el-radio label="Null">无</el-radio></el-row>
            <el-row><el-radio label="SequentialMultiInstance">会签（需所有审批人同意）</el-radio></el-row>
            <el-row><el-radio label="ParallelMultiInstance">或签（一名审批人同意即可）</el-radio></el-row>
          </el-radio-group>
        </el-row>
        <el-row v-if="multiLoopType !== 'Null'">
          <el-tooltip content="开启后，实例需按顺序轮流审批" placement="top-start" @click.stop.prevent>
            <i class="header-icon el-icon-info"></i>
          </el-tooltip>
          <span class="custom-label">顺序审批：</span>
          <el-switch v-model="isSequential" @change="changeMultiLoopType" />
        </el-row>
      </div>
    </el-row>

    <!-- 候选用户弹窗 -->
    <el-dialog title="候选用户" :visible.sync="userOpen" width="60%" append-to-body top="25px">
      <el-row type="flex" :gutter="20">
        <!--部门数据-->
        <el-col :span="7">
          <el-card shadow="never" style="height: 100%">
            <div slot="header">
              <span>部门列表</span>
            </div>
            <div class="head-container">
              <el-input v-model="deptName" placeholder="请输入部门名称" clearable size="small" prefix-icon="el-icon-search"
                style="margin-bottom: 20px" />
              <div class="filter-tree scroll_bar">
                <el-tree ref="nodeTree" :props="defaultProps" :data="deptOptions" node-key="id" current-node-key="05"
                  :filter-node-method="filterNode" @node-click="handleNodeClick" :expand-on-click-node="false"
                  accordion />
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="17" v-loading="tableLoading">
          <el-row>
            <el-col :span="24">
              <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
                <el-form-item label="姓名" prop="keyword">
                  <el-input v-model="queryParams.keyword" placeholder="请输入" clearable style="width: 240px"
                    @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <el-table ref="multipleTable" height="500" :data="multipleTableList" border
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="用户名" align="center" prop="username" />
            <el-table-column label="姓名" align="center" prop="name" />
            <el-table-column label="部门" align="center" prop="deptName" />
          </el-table>
          <div class="page_box">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentPage"
              @prev-click="handlePage" @next-click="handlePage" :current-page="page.pageNum" :pager-count="5"
              :page-sizes="[10, 20, 30, 40]" :page-size="10" layout="total, sizes, prev, pager, next, jumper"
              :total="page.total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="userOpen = false">取 消</el-button>
        <el-button type="primary" @click="handleTaskUserComplete">确 定</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import { listUser } from "@/api/authorityMan/userMan";
import { normalRoleList } from "@/api/authorityMan/roleMan";
import { deptTree } from '@/api/organizationChart/organizationalMan'
import TreeSelect from "@/components/modules/TreeSelect";
import { thisExpression } from "@babel/types";

const userTaskForm = {
  dataType: '',
  assignee: '',
  candidateUsers: '',
  candidateGroups: '',
  text: '',
  // dueDate: '',
  // followUpDate: '',
  // priority: ''
}

export default {
  name: "UserTask",
  props: {
    id: String,
    type: String
  },
  components: { TreeSelect },
  data() {
    return {
      customizeNodeId:null,
      tableLoading: false,
      loading: false,
      dataType: 'USERS',
      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0
      },
      cascaderProps: {
        children: 'children',
        label: 'name',
        value: 'id',
        multiple: true,
        emitPath: false,
      },
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'id'
      },
      selectedUser: {
        ids: [],
        text: []
      },
      userOpen: false,
      deptName: undefined,
      deptOptions: [],
      deptTempOptions: [],
      multipleTableList: [],
      userTotal: 0,
      selectedUserDate: [],
      selectedUsers: [],
      roleOptions: [],
      roleIds: [],
      deptTreeData: [],
      deptIds: [],
      // 查询参数
      queryParams: {
        deptId: undefined
      },
      showMultiFlog: false,
      isSequential: false,
      multiLoopType: 'Null',
    };
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement;
        this.$nextTick(() => this.resetTaskForm());
      }
    },
    deptName(newVal) {
      this.$refs.nodeTree.filter(newVal)
    }
  },
  beforeDestroy() {
    this.bpmnElement = null;
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1
    },
    async resetTaskForm() {
      const bpmnElementObj = this.bpmnElement?.businessObject;
      if (!bpmnElementObj) {
        return;
      }
      this.clearOptionsData()
      await this.getRoleOptions();
      await this.getDeptOptions()
      this.dataType = bpmnElementObj['dataType'];
      if (this.dataType === 'USERS') {
        let userIdData = bpmnElementObj['candidateUsers'] || bpmnElementObj['assignee'];
        let userText = bpmnElementObj['text'] || [];
        if (userIdData && userIdData.toString().length > 0 && userText && userText.length > 0) {
          this.selectedUser.ids = userIdData?.toString().split(',');
          this.selectedUser.text = userText?.split(',');
        }
        if (this.selectedUser.ids.length > 1) {
          this.showMultiFlog = true;
        }
      } else if (this.dataType === 'ROLES') {
        let roleIdData = bpmnElementObj['candidateGroups'] || [];
        if (roleIdData && roleIdData.length > 0) {
          this.roleIds = roleIdData.split(',')
        }
        this.showMultiFlog = true;
      } else if (this.dataType === 'DEPTS') {
        let deptIdData = bpmnElementObj['candidateGroups'] || [];
        if (deptIdData && deptIdData.length > 0) {
          this.deptIds = deptIdData.split(',');
        }
        this.showMultiFlog = true;
      }
      this.getElementLoop(bpmnElementObj);
    },
    /**
     * 清空选项数据
     */
    clearOptionsData() {
      this.selectedUser.ids = [];
      this.selectedUser.text = [];
      this.roleIds = [];
      this.deptIds = [];
    },
    /**
     * 跟新节点数据
     */
    updateElementTask() {
      const taskAttr = Object.create(null);
      for (let key in userTaskForm) {
        taskAttr[key] = userTaskForm[key];
      }
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, taskAttr);
    },
    /**
     * 查询部门下拉树结构
     */
    getDeptOptions() {
      return new Promise((resolve, reject) => {
        if (!this.deptOptions || this.deptOptions.length <= 0) {
          deptTree().then((res) => {
            let resData = res.data
            this.deptOptions = resData
            resolve(true)
          }).catch(() => {
            resolve(true)
          })
        } else {
          resolve(true)
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {

      this.page.pageNum = 1;
      this.getUserList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.page.pageNum = 1;
      this.queryParams.keyword = null;
      this.queryParams.roleId = null;
      this.selectedUsers = []; // 清空所有选择
      this.selectedUserDate = [];
      this.getUserList();
    },
    /**
     * 查询部门下拉树结构
     */
    getRoleOptions() {
      if (!this.roleOptions || this.roleOptions.length <= 0) {
        normalRoleList().then(response => this.roleOptions = response.data);
      }
    },

    // 获取表格数据
    getUserList(flag) {
      this.tableLoading = true
      if (flag) {
        this.queryParams.deptId = this.deptOptions[0].id
      }
      let params = {
        ...this.page,
        ...this.queryParams
      }
      listUser(params).then(res => {
        let resData = res.data
        this.multipleTableList = resData.records;
        this.page.total = resData.total;
        this.tableLoading = false
        // 恢复选中状态
        this.$nextTick(() => {
          // 恢复当前页面中应该被选中的行
          this.multipleTableList.forEach(row => {
            if (this.selectedUsers.some(selected => selected.id === row.id)) {
              this.$refs.multipleTable.toggleRowSelection(row, true);
            }
          });
        });
      }).catch(() => {
        this.tableLoading = false
      });
    },

    // // 筛选节点
    // filterNode(value, data) {
    //   if (!value) return true;
    //   return data.label.indexOf(value) !== -1;
    // },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.page.pageNum = 1;
      this.getUserList();
    },
    // 关闭标签
    handleClose(tag) {
      this.selectedUserDate.splice(this.selectedUserDate.indexOf(tag), 1);
      this.$refs.multipleTable.toggleRowSelection(tag);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      // 合并其他页面的选中状态和当前页面的选中状态
      const mergedSelection = [...this.selectedUsers, ...selection];
      this.selectedUsers = mergedSelection.filter((user, index) =>
        mergedSelection.findIndex(item => item.id === user.id) === index
      );
      this.selectedUserDate = this.selectedUsers;
    },

    // 点击添加用户
    async onSelectUsers() {
      this.selectedUserDate = []
      this.selectedUsers = []
      this.$refs.multipleTable?.clearSelection();
      await this.getDeptOptions();
      await this.getUserList(true)

      // this.queryParams.deptId = data.id;
      this.userOpen = true;
    },

    // 确定选择用户
    handleTaskUserComplete() {
      if (!this.selectedUserDate || this.selectedUserDate.length <= 0) {
        this.$modal.msgError('请选择用户');
        return;
      }
      userTaskForm.dataType = 'USERS';
      this.selectedUser.text = []

      this.selectedUser.text = this.selectedUserDate.map(k => k.name) || [];
      if (this.selectedUserDate.length === 1) {
        let data = this.selectedUserDate[0];
        userTaskForm.assignee = data.id;
        userTaskForm.text = data.name;
        userTaskForm.candidateUsers = null;
        this.showMultiFlog = false;
        this.multiLoopType = 'Null';
        this.changeMultiLoopType(this.multiLoopType);
      } else {
        userTaskForm.candidateUsers = this.selectedUserDate.map(k => k.id).join() || null;
        userTaskForm.text = this.selectedUserDate.map(k => k.name).join() || null;
        userTaskForm.assignee = null;
        this.showMultiFlog = true;
      }
      this.updateElementTask()
      this.userOpen = false;
    },

    // 选择角色
    changeSelectRoles(val) {
      let groups = null;
      let text = null;
      if (val && val.length > 0) {
        userTaskForm.dataType = 'ROLES';
        groups = val.join() || null;
        let textArr = this.roleOptions.filter(k => val.indexOf(`ROLE${k.id}`) >= 0);
        text = textArr?.map(k => k.roleName).join() || null;
      } else {
        userTaskForm.dataType = null;
        this.multiLoopType = 'Null';
      }
      userTaskForm.candidateGroups = groups;
      userTaskForm.text = text;
      this.updateElementTask();
      this.changeMultiLoopType(this.multiLoopType);
    },

    // 选择部门
    checkedDeptChange(checkedIds) {
      let groups = null;
      if (checkedIds && checkedIds.length > 0) {
        userTaskForm.dataType = 'DEPTS';
        groups = checkedIds.join(',') || null;

      } else {
        userTaskForm.dataType = null;
        this.multiLoopType = 'Null';
      }
      userTaskForm.candidateGroups = groups;
      this.updateElementTask();
      this.changeMultiLoopType(this.multiLoopType);
    },

    // 切换单选
    changeDataType(val) {
      if (val === 'ROLES' || val === 'DEPTS' || (val === 'USERS' && this.selectedUser.ids.length > 1)) {
        this.showMultiFlog = true;
      } else {
        this.showMultiFlog = false;
      }
      this.changeMultiLoopType('Null');
      // 清空 userTaskForm 所有属性值
      Object.keys(userTaskForm).forEach(key => userTaskForm[key] = null);
      userTaskForm.dataType = val;
      if (val === 'USERS') {
        if (this.selectedUser && this.selectedUser.ids && this.selectedUser.ids.length > 0) {
          if (this.selectedUser.ids.length === 1) {
            userTaskForm.assignee = this.selectedUser.ids[0];
          } else {
            userTaskForm.candidateUsers = this.selectedUser.ids.join()
          }
          userTaskForm.text = this.selectedUser.text?.join() || null
        }
      } else if (val === 'ROLES') {
        this.getRoleOptions();
        if (this.roleIds && this.roleIds.length > 0) {
          userTaskForm.candidateGroups = this.roleIds.join() || null;
          let textArr = this.roleOptions.filter(k => this.roleIds.indexOf(`ROLE${k.roleId}`) >= 0);
          userTaskForm.text = textArr?.map(k => k.roleName).join() || null;
        }
      } else if (val === 'DEPTS') {
        this.getDeptOptions();
        if (this.deptIds && this.deptIds.length > 0) {
          userTaskForm.candidateGroups = this.deptIds.join(',') || null;
        }
      } else if (val === 'INITIATOR') {
        userTaskForm.assignee = "${initiator}";
        userTaskForm.text = "流程发起人";
      }
      // else if (val === 'NODE') {
      //   userTaskForm.field = this.customizeNodeId
      //   // userTaskForm.text = "流程发起人";
      // }
      this.updateElementTask();
    },

    getElementLoop(businessObject) {
      if (!businessObject.loopCharacteristics) {
        this.multiLoopType = "Null";
        return;
      }
      this.isSequential = businessObject.loopCharacteristics.isSequential;
      if (businessObject.loopCharacteristics.completionCondition) {
        if (businessObject.loopCharacteristics.completionCondition.body === "${nrOfCompletedInstances >= nrOfInstances}") {
          this.multiLoopType = "SequentialMultiInstance";
        } else {
          this.multiLoopType = "ParallelMultiInstance";

        }
      }
    },

    changeMultiLoopType(type) {
      this.multiLoopType = type;
      // 取消多实例配置
      if (type === "Null") {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, { loopCharacteristics: null, assignee: null });
        return;
      }
      this.multiLoopInstance = window.bpmnInstances.moddle.create("bpmn:MultiInstanceLoopCharacteristics", { isSequential: this.isSequential });
      // 更新多实例配置
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        loopCharacteristics: this.multiLoopInstance,
        assignee: '${assignee}'
      });
      // 完成条件
      let completionCondition = null;
      // 会签
      if (type === "SequentialMultiInstance") {
        completionCondition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { body: "${nrOfCompletedInstances >= nrOfInstances}" });
      }
      // 或签
      if (type === "ParallelMultiInstance") {
        completionCondition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { body: "${nrOfCompletedInstances > 0}" });
      }
      // 更新模块属性信息
      window.bpmnInstances.modeling.updateModdleProperties(this.bpmnElement, this.multiLoopInstance, {
        collection: '${multiInstanceHandler.getUserIds(execution)}',
        elementVariable: 'assignee',
        completionCondition
      });
    },

    // 更改每页显示条数
    handleSizeChange(pageSize) {
      this.page.pageSize = pageSize
      this.getUserList()
    },

    // 选择页数
    handleCurrentPage(currentPage) {
      this.page.pageNum = currentPage
      this.getUserList()
    },

    // 点击上一页/下一页
    handlePage(currentPage) {
      this.page.pageNum = currentPage
      this.getUserList()
    }
  }
};
</script>

<style scoped lang="less">
.filter-tree {
  height: 50vh;
  overflow-y: auto;
}

.el-row .el-radio-group {
  margin-bottom: 15px;

  .el-radio {
    line-height: 28px;
  }
}

.el-tag {
  margin-bottom: 10px;

  +.el-tag {
    margin-left: 10px;
  }
}

.custom-label {
  padding-left: 5px;
  font-weight: 500;
  font-size: 14px;
  color: #606266;
}

.select_cascader {
  width: 100%;
}

/deep/.el-tree {
  background: none;
  height: 100%;

  .el-tree-node__content {
    height: 32px;
  }

  .is-current {
    >.el-tree-node__content {
      transition: all .15s ease;
      background: #bcd4ed;
      border-left: 2px solid #409eff;
      color: #2664d3;
    }
  }
}
</style>
