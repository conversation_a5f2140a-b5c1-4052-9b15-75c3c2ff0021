<template>
  <el-dialog
    title="查看"
    :visible.sync="dialogVisible"
    @close="cancel"
    width="70%"
    >
        <div class="container">
            <el-form class="search"  :inline="true">
                <el-form-item label="搜索" label-width="40px">
                    <el-input
                    v-model="searchForm.query"
                    size="mini"
                    placeholder="部套名/部套编码"
                    clearable
                    />
                </el-form-item>
                <el-form-item>
                    <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery(false)"
                    >搜索</el-button
                    >
                    <el-button icon="el-icon-refresh" size="mini" @click="handleQuery(true)"
                    >重置</el-button
                    >
                </el-form-item>
            </el-form>

            <div class="def-row">
                <div class="def-col">模块名称：{{orderInfo.name}}</div>
                <div class="def-col">模块编码：{{orderInfo.moduleCode}}</div>
            </div>

            <div class="table-box">
                <el-table
                :data="tableData"
                style="width: 100%">
                    <el-table-column 
                    label="序号" 
                    align="center"
                    type="index">
                    </el-table-column>

                    <el-table-column
                    prop="name"
                    label="部套名称"
                    align="center"
                    width="150">
                    </el-table-column>

                    <el-table-column
                    prop="partCode"
                    label="编码规则"
                    align="center"
                    width="150">
                    </el-table-column>

                    <el-table-column
                        prop="ytMonth"
                        label="技术准备日期"
                        align="center"
                        width=""
                    >
                        <template slot-scope="{row}">
                        <div v-if="String(row.ytMonth)">{{row.ytMonth}}天</div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="bidMonth"
                        label="定标周期"
                        align="center"
                        width=""
                    >
                        <template slot-scope="{row}">
                        <div v-if="String(row.bidMonth)">{{row.bidMonth}}天</div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="produceCycleMonth"
                        label="采购周期"
                        align="center"
                        width=""
                    >
                        <template slot-scope="{row}">
                        <div v-if="String(row.produceCycleMonth)">{{row.produceCycleMonth}}天</div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="produceMonth"
                        label="生产周期"
                        align="center"
                        width=""
                    >
                        <template slot-scope="{row}">
                        <div v-if="String(row.produceMonth)">{{row.produceMonth}}天</div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </el-dialog>
</template>

<script>
export default {
    data(){
        return {
            dialogVisible:false,
            tableData:[],
            partList:[],
            orderInfo:{},
            searchForm:{
                query:'',
                pageSize:10,
                pageNum:1
            }
        }
    },
    methods:{
        init(row){
            this.dialogVisible = true
            this.orderInfo = row
            this.partList = row.partList || []
            this.tableData = row.partList || []
        },
        handleQuery(flag){
            if(flag){
                this.searchForm.query = ''
                this.searchForm.pageNum = 1
            }
            this.loadData()
        },

        loadData(){
            let query = this.searchForm.query
            this.tableData = this.partList.filter((item)=>{
                return item.name.includes(query) || item.partCode.includes(query)
            })
        },

        cancel(){
            this.dialogVisible = false
        }
    }
}
</script>

<style scoped>
.search {
    display:flex;
    align-items:center;
}
.btn-container {
    margin-left:10px;
}
.def-row {
    width:50%;
    display:flex;
    margin-bottom:10px;

}
.def-row > .def-col {
    width:50%;
}

</style>