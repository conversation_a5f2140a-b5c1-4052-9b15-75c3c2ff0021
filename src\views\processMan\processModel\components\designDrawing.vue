<template>
    <el-dialog :title="designerData.title" :visible.sync="dialogVisible" append-to-body fullscreen>
        <process-designer
          :key="dialogVisible"
          style="border:1px solid rgba(0, 0, 0, 0.1);"
          ref="modelDesigner"
          v-loading="designerData.loading"
          :bpmnXml="designerData.bpmnXml"
          :designerForm="designerData.form"
          @save="onSaveDesigner"
        />
      </el-dialog>
</template>
<script>
import ProcessDesigner from '@/components/modules/ProcessDesigner';
import {getBpmnXml,saveModel} from '@/api/processMan/processModel'
export default {
    components:{ProcessDesigner},
    data(){
        return {
            dialogVisible:false,
            rowData:null,
            designerData: {
                loading: false,
                bpmnXml: '',
                modelId: null,
                form: {
                    processName: null,
                    processKey: null,
                    namespace: null
                }
            }
        }
    },
    methods:{
        async init(row){
            this.rowData = row
            this.designerData.modelId = row.modelId
            this.designerData.form = {
                processName: row.modelName,
                processKey: row.modelKey,
                category: row.category
            }
            this.designerData.title = "流程设计 - " + row.modelName;
            if(row.modelId){
                await this.getBpmnXml()
            }
        },

        async getBpmnXml(){
            this.designerData.loading = true
            await getBpmnXml(this.rowData.modelId).then((res)=>{
                this.designerData.bpmnXml = res.data || '';
                this.designerData.loading = false
                this.dialogVisible = true
            }).catch(()=>{
                this.designerData.loading = true
            })
        },

        // 保存流程
        onSaveDesigner(bpmnXml){
            this.bpmnXml = bpmnXml;
            this.$confirm("是否将此模型保存为新版本？", "提示", {
                distinguishCancelAndClose: true,
                confirmButtonText: '是',
                cancelButtonText: '否'
            }).then(() => {
                    this.confirmSave(true)
            }).catch(action => {
                if (action === 'cancel') {
                    this.confirmSave(false)
                }
            })
        },

        confirmSave(newVersion){
            let params = {
                modelId: this.designerData.modelId,
                bpmnXml: this.bpmnXml,
                newVersion
            }
            saveModel(params).then((res)=>{
                this.$message({
                    type:'success',
                    message:'操作成功',
                    duration:1500
                })
                this.$emit('hideDialog')
                this.dialogVisible = false
            })
        }
    }

}
</script>
