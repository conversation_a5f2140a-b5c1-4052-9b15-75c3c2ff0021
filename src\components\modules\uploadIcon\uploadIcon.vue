<template>
    <div class="icon_box" @mouseenter="isClear = value ? true :false" @mouseleave="isClear = false">
        <icon-picker v-model="iconVal"></icon-picker>
        <span class="el-input_suffix">
            <i v-if="isClear" class="icon el-icon-circle-close" @click.stop="clearVal"></i>
            <slot v-else></slot>
        </span>
    </div>

</template>
<script>
export default {
  props:{
    value:[String,Number,Array],
  },
  data(){
    return {
      isClear:false,
      iconVal:''
    }
  },
  watch:{
    value:{
      handler(newVal){
        this.iconVal = newVal
      },
      deep:true,
      immediate:true
    },
    iconVal(newVal){
      this.$emit('input',newVal)
    }
  },
  methods:{
    clearVal(){
      this.iconVal = ''
      this.$emit('input','')
    },
  }
}
</script>
<style lang="less" scoped>
.icon_box {
    position:relative;
    .el-input_suffix {
        position:absolute;
        top:1px;
        right:11px;
        color:#DCDFE6;
        font-size:14px;
        cursor:pointer;
    }
}
</style>
