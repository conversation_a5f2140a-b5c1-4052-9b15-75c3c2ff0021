import request from "@/utils/request";

// 获取详情
export function getDeductDetail(id) {
  return request({
    url: "/api/delivery/deduct/detail",
    method: "get",
    params: { id },
  });
}

// 修改罚分
export function updateDeduct(data) {
  return request({
    url: "/api/delivery/deduct/update",
    method: "post",
    data,
  });
}

// 删除罚分
export function deleteDeduct(data) {
  return request({
    url: "/api/delivery/deduct/delete",
    method: "post",
    data,
  });
}

// 确认罚分
export function confirmDeduct(data) {
  return request({
    url: "/api/delivery/deduct/confirm",
    method: "post",
    data,
  });
}
