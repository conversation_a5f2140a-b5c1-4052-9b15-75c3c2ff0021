import request from '@/utils/request'


// 获取路由
export function getTreeList(id) {
  if (id) {
    return request({
      url: `/user-server/backMenu/getTreeList?id=${id}`,
      method: 'get',

    })
  } else {
    return request({
      url: '/user-server/backMenu/getTreeList',
      method: 'get',
    })
  }

}
// 后台角色列表
export function getHouListPage(data) {
  return request({
    url: '/user-server/backRole/getListPage',
    method: 'post',
    params: data,
  })
}
// 后台角色修改用户状态
export function updateStatus(param) {
  return request({
    url: '/user-server/backRole/updateStatus',
    method: 'get',
    params: param,
  })
}
// 新增后台角色
export function addBackRole(data) {
  return request({
    url: '/user-server/backRole/add',
    method: 'post',
    data,

  })
}
// 修改后台角色
export function updateBackRole(data) {
  return request({
    url: '/user-server/backRole/update',
    method: 'post',
    data,

  })
}
// 删除后台角色
export function deleteBackRole(ids) {
  return request({
    url: `/user-server/backRole/deletes?ids=${ids}`,
    method: 'get',

  })
}