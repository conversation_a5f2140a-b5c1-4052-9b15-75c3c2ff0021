import request from "@/utils/request";
//分页查询
export function apiGetList(params) {
  return request({
    url: "/sd-server/supplier",
    method: "get",
    params: params,
  });
}
//战略供应商维护>分页查询
export function apiGetStrategyList(params) {
  return request({
    url: "/sd-server/supplier/strategy",
    method: "get",
    params: params,
  });
}
// 获取评级范围
export function apiGetRatingRanges(params) {
  return request({
    url: "/sd-server/supplier/score",
    method: "get",
    params: params,
  });
}
// 更新评级范围
export function apiUpdateRatingRanges(data) {
  return request({
    url: "/sd-server/supplier/score",
    method: "post",
    data: data,
  });
}
// 同步数据
export function apiSyncData(params) {
  return request({
    url: "/sd-server/supplier/sync",
    method: "get",
    params: params,
  });
}
