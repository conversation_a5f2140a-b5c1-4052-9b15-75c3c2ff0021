<template>
    <el-dialog
      title="新增"
      :visible.sync="dialogVisible"
      @close="cancel"
      width="70%"
      >
        <div class="container">
            <el-form class="form" :inline="true" ref="form" :model="form" label-width="80px" :rules="rules">
                <el-form-item class="form-item" label="模型名称" prop="name">
                    <el-input v-model="form.name" size="small" placeholder="请输入"></el-input>
                </el-form-item>
            </el-form>

            <p class="title">从已有部套中选择</p>

            <el-form ref="searchform" :model="searchForm" :inline="true" label-width="50px">

                <el-form-item label="搜索">
                    <el-input v-model="searchForm.query" size="small" placeholder="部套名/部套编码"></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="small"
                    @click="handleQuery(false)"
                    >搜索</el-button
                    >
                    <el-button icon="el-icon-refresh" 
                    size="small" @click="handleQuery(true)"
                    >重置</el-button
                    >
                    <span class="select-txt">已选 <span class="bold">2</span> 条</span>
                </el-form-item>

            </el-form>

            <div class="table-box">
                <el-table
                v-loading="loading"
                :data="tableData"
                style="width: 100%"
                row-key="id"
                @selection-change="handleSelectionChange"
                >
                    <el-table-column
                    type="selection"
                    reserve-selection
                    width="55">
                    </el-table-column>

                    <el-table-column 
                    label="序号" 
                    align="center"
                    type="index">
                    </el-table-column>

                    <el-table-column
                    prop="name"
                    label="模块名称"
                    align="center"
                    width="">
                    </el-table-column>

                    <el-table-column
                    prop="moduleCode"
                    label="模块编码"
                    align="center"
                    width="">
                    </el-table-column>

                    <el-table-column
                    prop="partNum"
                    align="center"
                    label="部套数"
                    width="">
                        <template slot-scope="{row}">
                            <div>{{row.partList && row.partList.length}}</div>
                        </template>
                    </el-table-column>

                    <el-table-column
                    prop=""
                    align="center"
                    label="查看部套"
                    width="100">
                        <template slot-scope="{row}">
                            <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-view"
                            @click="handleInfo(row)"
                            >查看</el-button>
                        </template>
                    </el-table-column>
                    
                </el-table>

                <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="searchForm.pageNum"
                :limit.sync="searchForm.pageSize"
                @pagination="loadData"
                />
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        </span>
        

        <module-info ref="moduleInfo"></module-info>
        <code-list ref="codeList"></code-list>
    </el-dialog>
  </template>
  
<script>
import moduleInfo from './moduleInfo'
import codeList from './codeList.vue'
import { modulePage } from '@/api/expectationAnalysis/modularity'
import { qtModel } from '@/api/expectationAnalysis/modelManager'
export default {
    components:{moduleInfo,codeList},
    data(){
        return {
            total: 0,
            dialogVisible:false,
            btnLoading:false,
            loading:false,
            tableData:[],
            selectData:[],
            form:{
                name:''
            },
            searchForm:{
                query:'',
                pageNum:1,
                pageSize:10
            },
            rules:{
                name:[
                    { required: true, message: '请输入模型名称', trigger: 'blur' },   
                ]
            }
        }
    },
    methods:{
        init(){
            this.dialogVisible = true
            this.btnLoading = false
            this.loadData()
        },

        // 查看
        handleInfo(row){
            this.$refs.moduleInfo.init(row)
        },

        // 点击令号
        handleCode(row){
            this.$refs.codeList.init(row)
        },

        handleSelectionChange(selectData){
            this.selectData = selectData.map((item)=>{
                return item.id
            })
        },

        handleQuery(flag){
            if(flag){
                this.searchForm.query = ''
                this.searchForm.pageNum = 1
            }
            this.loadData()
        },

        loadData(){
            this.loading = true
            let params = {
                ...this.searchForm,
            }
            modulePage(params).then((res)=>{
                let {records,total}  = res.data || {}
                this.tableData = records

                this.total = total
                this.loading = false
            }).catch(()=>{
                this.loading = false
            })
        },

        
        confirm(){
            this.$refs['form'].validate((valid) => {
                if(valid){
                    if(this.selectData.length === 0){
                        this.$message({
                            type:'warning',
                            message:'请选择操作的数据',
                            duration:1500
                        })
                        return
                    }
                    this.btnLoading = true
                    let params = {
                        ...this.form,
                        moduleIdList:this.selectData
                    }
                    qtModel(params).then((res)=>{
                        this.$message({
                            type:'success',
                            message:'操作成功',
                            duration:1500   
                        })
                        this.btnLoading = false
                        this.dialogVisible = false
                        this.$emit('hideDialog')
                    }).catch(()=>{
                        this.btnLoading = false
                    })
                }
            })

            
        },

        cancel(){
            this.$refs['form'].resetFields()
            this.dialogVisible = false
        }
    }
}
</script>
  
<style scoped>
    .check-icon {
        font-size:14px;
        color:#409EFF;
        cursor:pointer;
    }
    .bold {
        font-weight:bold;
    }
    .form {
        display:flex;
    }
    .select-txt {
        margin-left:10px;
    }
    .title {
        color:#409EFF;
        font-size:15px;
        margin-bottom:10px;
    }
    .form-item {
        width:70%;
    }
    .form-item ::v-deep .el-form-item__content{
        width:60%;
    }

    .def-row {
        display:flex;
        margin:10px 0;
    }
    .def-row > .def-col{
        width:50%;
        font-size:14px;
        text-align:left;
    }
    .def-col.text {
        padding-left:12px;
        box-sizing:border-box;
    }
    .big-size {
        font-size:18px;
        font-weight:500;
    }
</style>