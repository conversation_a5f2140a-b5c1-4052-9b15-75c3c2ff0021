<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="状态" prop="status" label-width="40px">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable>
          <el-option
            v-for="dict in statusList"
            :key="dict.value"
            :label="dict.text"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="风险等级" prop="riskLevel" label-width="80px">
        <el-select
          v-model="queryParams.riskLevel"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in riskLevelList"
            :key="dict.value"
            :label="dict.text"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="query">
        <el-input
          v-model="queryParams.query"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postList">
      <el-table-column label="序号" type="index" />
      <el-table-column label="责任方" prop="dutyName" />
      <el-table-column label="责任部门" prop="dutyUserName" />
      <el-table-column label="责任供应商">
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            title=""
            width="250"
            trigger="hover"
            :content="dataJoin(scope.row.mesProjectRiskSupplierList,'supplierName')">
            <el-button slot="reference" type="text" v-if="scope.row.mesProjectRiskSupplierList && scope.row.mesProjectRiskSupplierList.length>0">
              <div class="over-text" style="width:150px">{{ dataJoin(scope.row.mesProjectRiskSupplierList,'supplierName') }}</div>
            </el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="涉及产品" prop="productName" />
      <el-table-column label="风险等级" prop="riskLevel">
        <template slot-scope="scope">
          {{ listFind(riskLevelList, scope.row.riskLevel).text }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="300"
        label="风险点描述"
        prop="riskDesc"
      >
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            width="200"
            trigger="click"
            :content="scope.row.riskDesc"
          >
            <el-button slot="reference" type="text">
              <div class="over-text">{{ scope.row.riskDesc }}</div>
            </el-button>
          </el-popover>
          <!-- {{ scope.row.riskDesc }} -->
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createUserName" />
      <el-table-column label="创建时间" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" prop="reviewTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.reviewTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status">
        <template slot-scope="scope">
          {{ listFind(statusList, scope.row.status).text }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="140"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="showItem(scope.row)"
            >反馈</el-button
          >
        </template></el-table-column
      >
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <feedback ref="feedback"></feedback>

  </div>
</template>

<script>
import {
  getRiskDeptPage,
  getDutyUserPage,
  addDeptInfo,
} from "@/api/riskControl/projectRiskListByDept";

import feedback from './components/feedback'

export default {
  name: "Post",
  components:{feedback},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      openDept: false,
      openSupplier: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        query: "",
        pageNum: 1,
        pageSize: 10,
        status: undefined, //状态
        riskLevel: undefined, // 风险等级
      },
      riskLevelList: [
        {
          text: "严重",
          value: 0,
        },
        {
          text: "一般",
          value: 1,
        },
        {
          text: "轻微",
          value: 2,
        },
      ],
      statusList: [
        {
          text: "待部门反馈",
          value: 1,
        },
        {
          text: "待供应商反馈",
          value: 2,
        },
        {
          text: "待审核",
          value: 3,
        },
        {
          text: "已完成",
          value: 4,
        },
      ],
      //机组详情
      riskData: {},
      detailLoading: false,
      // 表单参数
      // deptForm: {
      //   pageNum: 1,
      //   pageSize: 10,
      //   total: 0,
      // },
      // deptCheckList: [],
      
      supplierCheck: {},
      // 部门人员列表
      dutyUserPage: [],
      // 厂商列表
      supplierPage: [],
      supLoading: false,

      // 审核弹框
      auditOpen: false,
      auditForm: {
        rejectDesc: "",
        status: null,
      },
      auditData: {},
      auditRole: null,
      auditLoading: false,

      // 反馈描述
      feedbackDesc: "",

      dialogImageUrl: "",
      dialogVisible: false,
      imageList: [],
      fileList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    dataJoin(array,text){
      let newArray = []
      if(array&& array.length >0){
        array.forEach(item => {
          newArray.push(item[text])
        });
        return newArray.join(',')
      }else{
        return ''
      }
    },
    listFind(list = [], value) {
      let index = list.findIndex((item) => {
        return item.value == value;
      });
      if (index > -1) {
        return list[index];
      }
    },

    /** 下载模板操作 */
    importTemplate() {
      this.download(
        "mes-server/project/risk/import/download",
        {
          ...this.queryParams,
        },
        "项目风险控制导入模板.xlsx"
      );
    },

    //创建时间处理
    timeChange() {
      this.queryParam.startTime = this.dateRange[0];
      this.queryParam.endTime = this.dateRange[1];
    },
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getRiskDeptPage(this.queryParams)
        .then((response) => {
          this.postList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.openDept = false;
    },
    cancelSupplier() {
      this.openSupplier = false;
    },
    // 重置
    reset() {
      this.deptForm.pageNum = 1;
      this.deptForm.pageSize = 10;
      this.supplierForm.page = 1;
      this.supplierForm.size = 10;
      this.supplierForm.keyword = "";
      this.deptCheckList = [];
      this.supplierCheckList = [];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    

    getDutyUserPage() {
      getDutyUserPage(this.deptForm).then((res) => {
        this.dutyUserPage = res.data.records;
        this.deptForm.total = res.data.total;
      });
    },

    
    /** 提交按钮 */
    submitForm: function () {
      let deptList = this.deptCheckList.map((item) => {
        return {
          dutyDeptId: item.deptId,
          dutyUserId: item.userId,
        };
      });
      addDeptInfo({
        deptList,
        id: this.riskData.id,
      }).then((response) => {
        this.$modal.msgSuccess("新增成功");
        this.getProjectRiskDept(this.riskData.id);
        this.openDept = false;
      });
    },

    // 审核
    showItem(row) {
      this.$refs.feedback.init(row)
    },
  
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-radio__label {
  display: none;
}
.img-box {
  display: flex;
  img {
    cursor: pointer;
    width: 146px;
    height: 146px;
    margin-right: 12px;
    border-radius: 8px;
  }
}
.over-text {
  font-size: 13px;
  width: 290px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.content-label {
  font-weight: 700;
}
::v-deep .el-dialog__body {
  line-height: 36px;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}
.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
::v-deep .el-row--flex {
  margin-left: 22px;
}
// ::v-deep .el-dialog {
//   margin-top: 30vh !important;
// }
.timeline-item-top {
  display: flex;
  justify-content: space-between;
  background-color: #f3f6fd;
  padding: 4px 12px;
  border-radius: 4px;
}
.input-box {
  margin-top: 8px;
}
.timeline-item-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  background-color: #f6f7f9;
  padding: 4px 12px;
  border-radius: 4px;
  .card-left {
    display: flex;
    flex-direction: column;
  }
}
.add-btn {
  margin-top: 8px;
  text-align: right;
}

.timeline-item-dept {
  display: flex;
  margin-top: 8px;
  height:auto;  
  
  .card-left {
    width: calc((100% - 10px)/2);
    background-color: #f6f7f9;
    padding: 4px 12px;
    border-radius: 4px;
    margin-right:10px;
    min-height:80px;
    box-sizing: border-box;
    height:inherit; 
    align-items:stretch
  }
  .card-left:last-of-type {
    margin-right:0;
  }
}
</style>
