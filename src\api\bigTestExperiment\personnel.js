import request from '@/utils/request'
// 分页
export function getBmUserPage(param) {
    return request({
        url: '/mes-server/bm/user/page',
        method: 'get',
        params: param,
    })
}

// 新增
export function addBmUser(param) {
    return request({
        url: '/mes-server/bm/user',
        method: 'post',
        data: param,
    })
}

// 修改
export function modifySmUser(param) {
    return request({
        url: '/mes-server/bm/user',
        method: 'put',
        data: param,
    })
}

// 删除
export function delBmUser(id) {
    return request({
        url: `/mes-server/bm/user/${id}`,
        method: 'delete',
    })
}

// 查询全部htc-qs人员
export function getHtcUserList(param) {
    return request({
        url: '/mes-server/bm/user/list',
        method: 'get',
        params: param,
    })
}