<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期">
        <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" />
      </el-form-item>
      <el-form-item label="搜索">
        <el-input v-model="queryParams.query" placeholder="请输入关键字" clearable style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="tableData" style="width: 100%; margin-top: 20px"
      @sort-change="handleSortChange">
      <el-table-column label="序号" type="index" width="60" align="center">
        <template slot-scope="scope">
          {{ queryParams.pageNum * 10 - queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="厂商名称" prop="supplierName" align="center" />
      <el-table-column label="厂商编号" prop="supplierCode" align="center" />
      <el-table-column label="铸锻件数量" prop="castingCount" align="center" sortable="custom" />
      <el-table-column label="应维护频率（每周维护一次）" prop="purchaseCount" width="220" align="center" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.purchaseCount }}次
        </template>
      </el-table-column>
      <el-table-column label="实际维护次数" prop="recordCount" align="center" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.recordCount }}次
        </template>
      </el-table-column>
      <el-table-column label="最后维护记录时间" prop="recordTime" align="center" sortable="custom" />

    </el-table>

    <!-- 分页组件 -->
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
      :current-page="queryParams.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="total" style="margin-top: 20px; text-align: right" />


  </div>
</template>

<script>
import { getMaintenanceScheduleList } from '@/api/supplier/schedule'
export default {
  name: 'MaintenanceSchedule',
  data() {
    return {
      loading: false,
      queryParams: {
        dateRange: [],
        query: '',
        pageNum: 1,
        pageSize: 10,
        sort: '',
        sortType: ''
      },
      total: 0,
      tableData: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表数据
    getList() {
      this.loading = true
      getMaintenanceScheduleList({
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        query: this.queryParams.query,
        startTime: this.queryParams.dateRange && this.queryParams.dateRange[0],
        endTime: this.queryParams.dateRange && this.queryParams.dateRange[1],
        sort: this.queryParams.sort,
        sortType: this.queryParams.sortType
      }).then(res => {
        this.tableData = res.data.records
        this.total = res.data.total
      }).finally(() => {
        this.loading = false
      })
    },

    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.queryParams = {
        dateRange: [],
        query: '',
        pageNum: 1,
        pageSize: 10,
        sort: '',
        sortType: ''
      }
      this.getList()
    },

    handleRefresh() {
      this.getList()
    },

    handleSetting() {
      // 实现列设置功能
      this.$message.info('列设置功能开发中')
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },

    // 页码改变
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },

    handleSortChange(column) {
      if (column.prop) {
        this.queryParams.sortType = column.prop
        this.queryParams.sort = column.order === 'ascending' ? '1' : '2'
        this.handleQuery()
      }
    }
  }
}
</script>

<style scoped>
.top-right-buttons {
  position: absolute;
  top: 10px;
  right: 20px;
}

.top-right-buttons .el-button {
  margin-left: 10px;
}
</style>
