<template>
   <el-dialog
      title="导入"
      :visible.sync="dialogVisible"
      width="400px"
      append-to-body
      @close="cancel"
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="headers"
        :action="url"
        :disabled="btnLoading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :file-list="fileList"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
</template>

<script>
import { downloadRisk } from "@/api/riskControl/projectRiskList";
import { getToken } from "@/utils/auth";
export default {
  components:{},

  data(){
    return{
        dialogVisible:false,
        btnLoading:false,
        fileList:[],
        headers: { "X-Token": "Bearer " + getToken() },
        url:process.env.VUE_APP_BASE_API + "/mes-server/project/risk/import"
        
    }
  },
  

  created(){},

  methods:{
    init(){
        this.dialogVisible = true
        this.btnLoading = false
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.btnLoading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      if(response.code === 0){      
        let msg = response.data.join(',')
        this.$message({
          type:'error',
          message:msg,
          duration:2000
        })
        this.btnLoading = false
      }else{
        this.dialogVisible = false
        this.$emit('getList')
      }
    },

    importTemplate() {
        downloadRisk().then((response) => {
            const url = window.URL.createObjectURL(new Blob([response]));
            const link = document.createElement("a");
            link.target = "_blank";
            link.href = url;
            link.setAttribute("download", "项目风险控制导入模板.xlsx");
            document.body.appendChild(link);
            link.click();
        });
    },

    confirm(){
        this.$refs.upload.submit();
    },

    cancel(){
      this.fileList = []
      this.dialogVisible = false
    }
  },

}

</script>

<style scoped lang='less'>
</style>