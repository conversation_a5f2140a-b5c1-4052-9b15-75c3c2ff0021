<template>
    <el-dialog
      title="查看"
      :visible.sync="dialogVisible"
      @close="cancel"
      width="70%"
      >
        <div class="container">

            <el-form ref="searchform" :model="searchForm" :inline="true" label-width="50px">
                <el-form-item label="搜索">
                    <el-input v-model="searchForm.query" size="small" placeholder="模块名/模块编码"></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="small"
                    @click="handleQuery(false)"
                    >搜索</el-button
                    >
                    <el-button icon="el-icon-refresh" 
                    size="small" @click="handleQuery(true)"
                    >重置</el-button
                    >
                </el-form-item>
            </el-form>

            <div class="table-box">
                <el-table
                :data="tableData"
                style="width: 100%"
                >

                    <el-table-column 
                    label="序号" 
                    align="center"
                    type="index">
                    </el-table-column>

                    <el-table-column 
                    label="机组类别" 
                    prop="modelNameList" 
                    align="center">
                    <template>
                        <div>{{moduleNameList}}</div>
                    </template>
                    </el-table-column>

                    <el-table-column
                    prop="name"
                    label="模块名称"
                    align="center"
                    width="">
                    </el-table-column>

                    <el-table-column
                    prop="partList"
                    align="center"
                    label="部套数"
                    width="">
                        <template slot-scope="{row}">
                            <div v-if="row.partList && row.partList.length>0">
                                {{row.partList.length}}
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column
                    prop=""
                    align="center"
                    label="查看部套"
                    width="100">
                        <template slot-scope="{row}">
                            <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-view"
                            @click="handleInfo(row)"
                            >查看</el-button>
                        </template>
                    </el-table-column>
                    
                </el-table>
            </div>
        </div>
        <module-info ref="moduleInfo"></module-info>
        <code-list ref="codeList"></code-list>
    </el-dialog>
    
</template>

<script>
import codeList from './codeList'
import moduleInfo from './moduleInfo'
export default {
  components:{codeList,moduleInfo},

  data(){
    return{
        dialogVisible:false,
        moduleNameList:'',
        tableData:[],
        moduleList:[],
        searchForm:{
            query:''
        }
    }
  },

  created(){},

  methods:{
    init(row){
        this.moduleNameList = row.name
        
        this.moduleList = row.moduleList || []
        this.tableData = row.moduleList || []
        this.dialogVisible = true
    },
    handleSelectionChange(){

    },

    handleQuery(flag){
        if(flag){
            this.searchForm.query =''
        }
        let query = this.searchForm.query
        this.tableData = this.moduleList.filter((item)=>{
            return item.name.includes(query) || item.moduleCode.includes(query)
        })
    },

    // 查看
    handleInfo(row){
        this.$refs.moduleInfo.init(row,this.moduleNameList)
    },

    cancel(){
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped>
.check-icon {
    font-size:14px;
    color:#409EFF;
    cursor:pointer;
}
.select-txt {
    margin-left:10px;
}
</style>