<template>
  <div class="process_class">
    <div class="search_form">
      <el-row type="flex" :gutter="6">
<!--        <el-col :span="4">-->
<!--          <el-select-->
<!--            v-model="searchForm.moduleId"-->
<!--            placeholder="请选择板块"-->
<!--            class="format_option"-->
<!--            clearable-->
<!--          >-->
<!--            <el-option-->
<!--              v-for="item in moduleOptions"-->
<!--              :key="item.id"-->
<!--              :label="item.name"-->
<!--              :value="item.id"-->
<!--            >-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-col>-->

        <el-col :span="4">
          <el-input
            v-model="searchForm.categoryName"
            class="format_option"
            placeholder="请输入分类名称"
            clearable
          ></el-input>
        </el-col>

        <el-col :span="6">
          <div class="btn_box">
            <el-button
              @click="search()"
              class="btn search_btn"
              icon="el-icon-search"
              type="primary"
              >搜索</el-button
            >
            <el-button
              @click="search(true)"
              class="btn reset_btn"
              icon="el-icon-refresh"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="operation_btn">
      <el-button
        class="btn add_btn"
        icon="el-icon-plus"
        type="primary"
        @click="handleAdd()"
        >新增</el-button
      >
    </div>

    <div class="table_box" v-loading="loading">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column type="index" label="序号" width="80" align="center">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableColumn"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :width="item.width"
        >
          <template slot-scope="{ row }">
            <div v-if="item.imgStatus" class="avatar_box">
              <img v-if="row[item.prop]" class="img" :src="row[item.prop]" />
            </div>
            <div v-else-if="item.checkTime">
              <i class="el-icon-time"></i>
              {{ row[item.prop] | dateTimeFormat }}
            </div>
            <el-tag
              v-else-if="item.tabStatus"
              :type="row.status === 0 ? 'danger' : 'default'"
              >{{ row[item.prop] }}</el-tag
            >
            <div v-else>{{ row[item.prop] }}</div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="200">
          <template slot-scope="{ row }">
            <div class="handle_btn">
              <el-button
                type="text"
                size="small"
                icon="el-icon-edit"
                @click="handleAdd(row)"
                >修改</el-button
              >
              <el-popconfirm title="是否确定删除？" @confirm="handleDel(row)">
                <el-button
                  slot="reference"
                  class="del"
                  type="text"
                  size="small"
                  icon="el-icon-delete"
                  >删除</el-button
                >
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="page_box">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentPage"
          :pager-count="5"
          :current-page="page.pageNum"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
        >
        </el-pagination>
      </div>
    </div>

    <add-process-class
      ref="addProcessClass"
      @hideDialog="hideDialog"
    ></add-process-class>
  </div>
</template>
<script>
import addProcessClass from "./components/addProcessClass.vue";
import {
  processClassList,
  delProcessClass,
} from "@/api/processMan/processClass";
// import { getPlateList } from "@/api/platformMan/plateMan";
export default {
  components: {
    addProcessClass,
  },
  data() {
    return {
      loading: false,
      addPlateVisible: false,
      searchForm: {
        moduleId: "",
        categoryName: "",
      },
      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      moduleOptions: [],
      tableData: [],
      tableColumn: [
        // {
        //   prop: "moduleName",
        //   label: "板块名称",
        //   width: "100",
        // },
        {
          prop: "code",
          label: "分类编码",
          width: "100",
        },
        {
          prop: "categoryName",
          label: "分类名称",
          width: "100",
        },
        {
          prop: "remark",
          label: "备注",
          width: "",
        },
        {
          prop: "createTime",
          label: "创建时间",
          width: "200",
        },
      ],
    };
  },
  beforeCreate() {
    // getPlateList().then((res) => {
    //   this.moduleOptions = res.data;
    // });
  },
  methods: {
    hideDialog() {
      this.loadData();
    },

    // 新增/修改
    handleAdd(row) {
      this.$nextTick(() => {
        this.$refs.addProcessClass.init(row);
      });
    },

    // 删除
    handleDel(row) {
      delProcessClass(row.id).then((res) => {
        this.$message({
          type: "success",
          message: "操作成功",
          duration: 1500,
        });
        this.loadData();
      });
    },

    // 查询、重置
    search(reset) {
      if (reset) {
        this.searchForm = {
          name: "",
        };
      }
      this.page.pageNum = 1;
      this.loadData();
    },

    // 获取数据
    loadData() {
      this.loading = true;
      let params = {
        ...this.page,
        ...this.searchForm,
      };
      processClassList(params)
        .then((res) => {
          let resData = res.data;
          this.tableData = resData.records;
          this.page.total = resData.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 更改每页显示条数
    handleSizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.loadData();
    },

    // 选择页数/点击上一页/下一页
    handleCurrentPage(currentPage) {
      this.page.pageNum = currentPage;
      this.loadData();
    }
  },
  created() {
    this.loadData();
  },
};
</script>
<style lang="less" scoped>
.process_class {
  padding: 16px 12px 0;
}
</style>
