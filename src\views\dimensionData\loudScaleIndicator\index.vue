<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="状态" prop="secondType">
        <el-select style="width: 100%" v-model="queryParams.secondType" placeholder="请选择" clearable>
          <el-option v-for="item in secondCategoryList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="分类编号" prop="typeNo">
        <el-select v-model="queryParams.typeNo" placeholder="请选择" style="width: 100%">
          <el-option v-for="(item, index) in typeNoList" :key="index" :label="item.label" :value="item.typeNo">
            <template #default>
              <el-tooltip :content="item.typeName" placement="top">
                <span>{{ item.typeNo }}</span>
              </el-tooltip>
            </template>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="query">
        <el-input v-model="queryParams.query" style="width: 300px" placeholder="供方编码/供方编号/供方名称/检查员姓名" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="mb8 justify-between">
      <el-row :gutter="10" class="item-center">
        <!-- <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
        </el-col> -->
        <!-- <el-col :span="1.5">
          <el-button type="primary" size="mini" @click="openBatch()">导出（已选0条）</el-button>
        </el-col> -->
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-upload2" size="mini" @click="handleImport()">导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-download" size="mini" @click="exportData()">模板下载</el-button>
        </el-col>
      </el-row>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </div>
    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange" max-height="550">
      <el-table-column type="selection" width="55" />
      <el-table-column label="序号" type="index" width="120">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="开标日期" prop="openTime" width="150" />
      <el-table-column label="供方分类" prop="typeNo" width="150" />
      <el-table-column label="招标编号" prop="bidNo" width="200" show-overflow-tooltip />
      <el-table-column label="招标项目名称" prop="bidName" width="300" show-overflow-tooltip align="center" />
      <el-table-column label="参标供方" prop="supplierName" width="200" />
      <el-table-column label="单标投标报价" prop="bidPrice" />
      <el-table-column label="不参标/不响应" prop="bidScore1" />
      <el-table-column label="参与招标" prop="bidScore2" />
      <el-table-column label="标书响应" prop="bidScore3" />
      <el-table-column label="中标情况" prop="bidWin">
        <template slot-scope="scope">
          {{ scope.row.bidWin === 1 ? '是' : '否' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="中标金额" prop="priceWin" />
      <el-table-column label="参考价" prop="referencePrice" />
      <el-table-column label="节约" prop="conserve" />
      <el-table-column label="降幅" prop="amplitude" /> -->
      <el-table-column label="扣分原因(参与招标)" prop="reason1" width="200" />
      <el-table-column label="扣分原因(标书响应)" prop="reason2" width="200" />
      <el-table-column label="修改历史" prop="" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" v-if="scope.row.updateUserId"
            @click="historyHandle(scope.row)">有修改</el-button>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column width="180" label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleEdit(scope.row, 'view')">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row, 'edit')">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete"
            @click="handleEdit(scope.row, 'delete')">删除</el-button>
        </template></el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <add ref="addDialog" :visible.sync="addDialogVisible" @refresh="resetQuery"></add>
    <process-import ref="processImport" @refresh="getList"></process-import>
    <edit-score-dialog :visible.sync="editDialogVisible" @refresh="resetQuery" :row="currentRow" :type="currentType" />
    <history-dialog :visible.sync="historyDialogVisible" :row="currentRow" />
  </div>
</template>

<script>
import add from "./components/add.vue";
import { download } from "@/utils/request";
import {
  apiGetList,
  apiGetTemplateList
} from "@/api/dimensionData/loudScaleIndicator.js";
import { apiGetSecondCategory } from "@/api/dimensionData/qualityIndicator.js";
import { apiGetTypeNoList } from '@/api/appraiseManager/appraiseAll'

import ProcessImport from "./components/processImport.vue";
import EditScoreDialog from "./components/edit.vue";
import HistoryDialog from "./components/historyDialog.vue";
export default {
  components: {
    add,
    ProcessImport,
    EditScoreDialog,
    HistoryDialog
  },
  data() {
    return {
      typeNoList: [],
      // 当前选中行
      currentRow: {},
      // 二级分类
      secondCategoryList: [],
      // 修改历史弹出层
      historyDialogVisible: false,
      // 当前选中行id
      currentId: '',
      // 当前选中行类型
      currentType: 'view',
      editDialogVisible: false,
      // 添加弹出层
      addDialogVisible: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: undefined, //模块名称
        moduleCode: undefined, //模块编码
        posid: undefined, //令号
        post1: undefined, //机组名称
        powerPl: undefined, //业主名称（电厂）
        startTime: undefined, //开始时间
        endTime: undefined, //结束时间
        status: undefined, //状态
        typeNo: undefined, //分类编号
      },
      //机组详情
      posidList: {},
    };
  },
  created() {
    this.getList();
    this.getSecondCategory();
    this.getAllTypeNo()
  },
  methods: {
    // 获取全部的分类编号
    getAllTypeNo() {
      apiGetTypeNoList().then((response) => {
        this.typeNoList = response.data
      })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.$refs.processImport.init();
    },
    exportData() {
      // 使用fetch API进行文件下载，正确添加token
      const baseURL = process.env.VUE_APP_BASE_API || '';
      const token = this.$store.getters.token || '';

      // 显示加载中提示
      this.$loading({
        lock: true,
        text: '正在下载模板，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 使用fetch API进行请求，正确设置请求头
      fetch(`${baseURL}/sd-server/bid/template`, {
        method: 'GET',
        headers: {
          'X-Token': `Bearer ${token}`
        }
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('下载失败');
          }
          return response.blob();
        })
        .then(blob => {
          // 创建Blob URL并触发下载
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', '响标度导入模板.xlsx');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          // 关闭加载提示
          this.$loading().close();
        })
        .catch(error => {
          // 关闭加载提示
          this.$loading().close();
          this.$message.error('下载模板失败：' + error.message);
          console.error('下载错误:', error);
        });
    },
    // 获取二级分类
    getSecondCategory() {
      apiGetSecondCategory({ type: 3 }).then((res) => {
        this.secondCategoryList = res.data.map(item => {
          return {
            label: item,
            value: item
          }
        })
        this.secondCategoryList = [{ label: '全部', value: '' }, ...this.secondCategoryList]
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.addDialogVisible = true
    },
    handleEdit(row, type) {
      this.currentRow = row
      this.currentType = type
      this.editDialogVisible = true
    },
    // 获取修改历史
    historyHandle(row) {
      this.historyDialogVisible = true
      this.currentRow = row
    },
    // 导入
    handleImp() {
      this.$refs.processImport.init();
    },

    /** 分页查询 */
    getList() {
      this.loading = true;
      apiGetList(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },

  },
};
</script>

<style scoped>
::v-deep .el-dialog__body {
  line-height: 36px;
}

.item-center {
  display: flex;
  align-items: center;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.font-size-14 {
  font-size: 14px;
}

.grey {
  color: #999;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
</style>
