<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期">
        <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" />
      </el-form-item>
      <el-form-item>
        <el-input v-model="queryParams.query" placeholder="请输入关键字" clearable style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%; margin-top: 20px">
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="厂商名称" prop="name" align="center" />
      <el-table-column label="厂商编号" prop="code" align="center" />
      <el-table-column label="铸钢件数量" prop="castingCount" align="center" />
      <el-table-column label="应维护节点记录数" prop="requiredMaintenanceCount" align="center">
        <template slot-scope="scope">
          {{ scope.row.requiredMaintenanceCount }}次
        </template>
      </el-table-column>
      <el-table-column label="实际维护节点记录数" prop="actualMaintenanceCount" align="center">
        <template slot-scope="scope">
          {{ scope.row.actualMaintenanceCount }}次
        </template>
      </el-table-column>
    </el-table>

    <!-- 右上角功能按钮 -->
    <div class="top-right-buttons">
      <el-button size="mini" circle icon="el-icon-refresh" @click="handleRefresh" title="刷新" />
      <el-button size="mini" circle icon="el-icon-setting" @click="handleSetting" title="列设置" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'MaintenanceRecord',
  data() {
    return {
      loading: false,
      queryParams: {
        dateRange: [],
        query: ''
      },
      tableData: []
    }
  },
  methods: {
    handleQuery() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
      }, 500)
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    handleRefresh() {
      this.handleQuery()
    },
    handleSetting() {
      // 实现列设置功能
      this.$message.info('列设置功能开发中')
    }
  }
}
</script>

<style scoped>
.top-right-buttons {
  position: absolute;
  top: 10px;
  right: 20px;
}

.top-right-buttons .el-button {
  margin-left: 10px;
}
</style>
