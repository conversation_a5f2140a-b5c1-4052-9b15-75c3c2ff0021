<template>
  <div>
    <el-dialog title="铸锻件合同详情" :visible.sync="visible" width="980px" append-to-body :top="'5vh'" @close="handleClose"
      destroy-on-close>
      <div class="dialog-content" v-loading="itemLoading">
        <div class="content">
          <el-row>
            <el-col :span="24">
              <div class="grid-content bg-purple">
                <span>公司名称：<span>{{ posidList.supplierName ? posidList.supplierName : '-' }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>联系人：<span>{{ posidList.projectManager ? posidList.projectManager : '-' }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>电话：<span>{{ posidList.phone ? posidList.phone : '-' }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>当前节点：<span>{{ posidList.currentNodeName ? posidList.currentNodeName : '-' }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>步骤节点：<span>{{ posidList.currentNode ? posidList.currentNode : '-' }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>节点记录数：<span>{{
                  posidList.nodeRecordCount
                    }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>合同开始时间：<span>{{ posidList.startTime ? posidList.startTime : '-' }}</span></span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>约定交货时间：<span>{{ posidList.plifz ? posidList.plifz : '-' }}</span></span>
              </div>
            </el-col>
            <!-- <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>计划需求时间：<span>{{ posidList.frgdt }}</span></span>
              </div>
            </el-col> -->
            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>实际完成时间：<span>{{ posidList.budatMkp ? posidList.budatMkp : '-' }}</span></span>
              </div>
            </el-col>

            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>厂家预计完成时间：<span>{{ posidList.zrsv08 ? posidList.zrsv08 : '-' }}</span></span>
              </div>
            </el-col>
          </el-row>
          <div class="change-record">变更记录</div>
          <div class="change-record-box block"
            v-if="posidList && posidList.updateRecordList && posidList.updateRecordList.length > 0">
            <el-timeline>
              <el-timeline-item color="#1890FF" v-for="(item, index) in posidList.updateRecordList" :key="index"
                placement="top">
                <el-card>
                  <div>
                    <span class="font-bold">{{ item.crtTime }}</span>
                    <span class="ml-4 color-grey">{{ item.userName }} </span>
                  </div>
                  <block v-for="(item2, index2) in item.array" :key="index2">
                    <div>{{ item2.nodeName }}</div>
                    <div style="color: #999; font-size: 13px;">
                      {{ item2.planTimeOldValue }} 修改为 {{ item2.planTimeNewValue }}
                    </div>
                  </block>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-else>
            <el-empty description="暂无变更记录" style="padding:  0;" />
          </div>
        </div>

        <div class="block">
          <el-timeline>
            <el-timeline-item color="#1890FF" v-for="(item, index) in posidList.nodeList" :key="index">
              <div class="timeline-item-top">
                <div class="top-left">
                  <div class="top-title">{{ item.nodeName }}</div>
                  <div class="top-content">
                    <p>规定完成时间：{{ item.planTime ? item.planTime : '-' }}</p>
                    <p>实际完成时间：{{ item.finishTime ? item.finishTime : '-' }}</p>
                    <p v-if="item.nodeType == 2">厂家当前节点预计完成时间：{{ item.supplierPlanTime ? item.supplierPlanTime : '-' }}
                    </p>
                  </div>
                </div>
                <div class="top-right">
                  <div v-if="item.finishStatus == 2">
                    <el-button type="text" size="small" @click="handleUpdate(item)"
                      v-if="posidList.qcFileStatus == 0 && item.nodeName == '发货' && page == 'contract'">去维护</el-button>
                    <el-popconfirm title="是否确认完成？" @confirm="updateNodeStatusMethod(item)"
                      v-else-if="((page == 'contract' && item.nodeType == 2) || page == '')">
                      <el-button slot="reference" type="text" size="small">点击完成</el-button>
                    </el-popconfirm>
                  </div>

                  <div v-else>{{ { 0: "未完成", 1: "已完成", 2: "进行中" }[item.finishStatus] }}</div>
                  <el-button v-if="item.finishStatus == 2 && item.nodeType == 2" type="primary" size="small"
                    icon="el-icon-plus" plain @click="handleAdd(item)">新增</el-button>
                </div>
              </div>
              <div class="">
                <div class="timeline-item-card" v-for="(item2, index2) in item.recordList" :key="'a' + index2">
                  <div class="card-left">
                    <span>{{ item2.record }}</span>
                    <span class="card-address">{{ item2.createTime }}</span>
                    <div class="img-box">
                      <el-image v-for="(img, imgIndex) in item2.imageList" :key="imgIndex" :src="imageApi + img.newName"
                        :preview-src-list="[imageApi + img.newName]" style="width: 100px; height: 100px;" />
                    </div>

                    <span v-if="item2.address" class="card-address">
                      <i class="el-icon-location-outline"></i>
                      {{ item2.address }}</span>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
    <Add :addVisible.sync="addVisible" :row="addForm" @refresh="getDetail(castingId)" />
  </div>
</template>
<script>
import {
  getCastingDetails,
  updateNodeStatus,
} from "@/api/castingForging/contract";
import Add from "./add.vue";
export default {
  components: {
    Add,
  },
  props: {
    page: {
      type: String,
      default: "",
    },
    detailVisible: {
      type: Boolean,
      default: false,
    },
    castingId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      addVisible: false,
      itemLoading: false,
      posidList: {

      },
      imageApi: process.env.VUE_APP_IMAGE_API,
      addForm: {},
      visible: false,
    };
  },
  watch: {
    detailVisible: {
      handler(val) {
        this.visible = val;
        if (val) {
          this.getDetail(this.castingId);
        }
      },
      immediate: true
    },
  },
  methods: {
    handleUpdate(row) {
      // this.$emit('maintenance', row)
      this.$router.push({
        path: '/bigTestExperiment/bigTestExperiment/remoteSupervision/index',
        query: {
          row
        }
      })
    },
    // 修改状态
    updateNodeStatusMethod(item) {
      this.itemLoading = true;
      updateNodeStatus({ nodeId: item.id })
        .then((res) => {
          this.itemLoading = false;
          this.$modal.msgSuccess("修改成功");
          this.getDetail(this.castingId);
        })
        .catch(() => {
          this.itemLoading = false;
        });
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.addVisible = true;
      this.addForm = {
        imageList: [],
        record: "",
        castingId: row.castingId,
        planTime: row.planTime,
        nodeId: row.id,
      };
    },
    getDetail(castingId) {
      this.itemLoading = true;
      getCastingDetails({ castingId, type: this.page == 'contract' ? 2 : 1 }).then((res) => {
        this.posidList = res.data;

        this.itemLoading = false;
      });
      this.addVisible = false;
    },
    handleClose() {
      this.$emit('update:detailVisible', false);
      this.addVisible = false;
    }
  },
};
</script>
<style scoped lang="scss">
.ml-4 {
  margin-left: 16px;
}

// 如果想确保竖线显示，可以添加以下样式
:deep(.el-timeline) {
  .el-timeline-item:last-child {
    .el-timeline-item__tail {
      display: block; // 确保显示
    }
  }
}

.el-timeline-item {
  padding-bottom: 0 !important;
}

.color-grey {
  color: #999;
}

.change-record {
  font-size: 16px;
  color: #1890ff;
  font-weight: 700;
  margin: 16px 0;
}

.change-record-box {
  height: 30vh;
  width: 100%;
  overflow-y: auto;

  ::v-deep .el-timeline {
    .el-timeline-item:last-child {
      .el-timeline-item__tail {
        display: none;
      }
    }
  }
}

.time-box {
  margin-left: 20px;
  margin-bottom: 16px;
}

::v-deep .el-dialog__body {
  line-height: 36px;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}

::v-deep .el-row--flex {
  margin-left: 22px;
}

.dialog-content {
  height: 80vh;
  display: flex;
  justify-content: space-between;

  .content {
    width: 30%;
  }

  .block {
    flex: 1;
    padding: 8px;
    overflow-y: scroll;
  }
}

.timeline-item-top {
  display: flex;
  justify-content: space-between;
  background-color: #e3f0ff;
  padding: 4px 12px;
  border-radius: 4px;

  .top-right {
    font-size: 12px;
    color: #666;
    text-align: right;
  }

  .top-title {
    font-size: 16px;
    color: #1890ff;
    font-weight: 700;
  }

  .top-content {
    font-size: 12px;
    color: #666;

    >p {
      padding: 0;
      margin: 0;
      line-height: 18px;
    }

    // :nth-child(2) {
    // margin-left: 24px;
    // }
  }
}

.timeline-item-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  background-color: #f6f7f9;
  padding: 4px 12px;
  border-radius: 4px;

  .card-left {
    display: flex;
    flex-direction: column;

    .card-address {
      font-size: 12px;
      color: #85929b;
    }

    .img-box {
      width: 100%;
      display: flex;

      img {
        width: 100px;
        height: 100px;
        margin-right: 12px;
      }
    }
  }
}

.loading-box {

  ::v-deep .el-upload-list__item,
  ::v-deep .el-upload--picture-card {
    width: 80px;
    height: 80px;
    position: relative;
  }

  ::v-deep .icon_pic {
    position: absolute;
    top: 25px;
    left: 25px;
  }
}
</style>
