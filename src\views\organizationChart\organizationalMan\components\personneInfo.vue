<template>
    <el-dialog
    title="详情"
    :visible.sync="dialogVisible"
    width="40%"
    :append-to-body="true"
    >
    <div class="dialog_con_info">
        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">姓名：</span> {{form.name}}
            </el-col>
            <el-col :span="12">
                <span class="txt">员工编号： </span>{{form.no}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">直属领导：</span>{{form.leaderName}}
            </el-col>
            <el-col :span="12">
                <span class="txt">邮箱：</span>{{form.mail}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">所属机构：</span>{{form.deptName}}
            </el-col>
            <el-col :span="12">
                <span class="txt">手机号：</span>{{form.phone}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">性别：</span>{{(form.male === 0 ? '女' : '男')}}
            </el-col>
            <el-col :span="12">
                <span class="txt">证件号码：</span>{{form.idCard}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">出生日期：</span>{{form.birthDate | dateFormat}}
            </el-col>
            <el-col :span="12">
                <span class="txt">参加工作日期：</span>{{form.joinWorkDate | dateFormat}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">SAP编码：</span>{{form.glbdef11}}
            </el-col>
            <el-col :span="12">
                <span class="txt">学历：</span>{{form.name1}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">婚姻状况：</span>{{form.name2}}
            </el-col>
            <el-col :span="12">
                <span class="txt">民族：</span>{{form.name3}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">政治面貌：</span>{{form.name4}}
            </el-col>
            <el-col :span="12">
                <span class="txt">部门名称：</span>{{form.name5}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">岗位名称：</span>{{form.postname}}
            </el-col>
            <el-col :span="12">
                <span class="txt">人员分类名称：</span>{{form.name6}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">管理层级：</span>{{form.name7}}
            </el-col>
            <el-col :span="12">
                <span class="txt">岗位序列名称：</span>{{form.postSeriesName}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">任职开始日期：</span>{{form.beginDate | dateFormat}}
            </el-col>
            <el-col :span="12">
                <span class="txt">在岗：</span>{{form.postStat === 'Y' ? '是' : '否'}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">芯片号：</span>{{form.glbdef1}}
            </el-col>
            <el-col :span="12">
                <span class="txt">职称：</span>{{form.name9}}
            </el-col>
        </el-row>

        <el-row type="flex">
            <el-col :span="12">
                <span class="txt">状态：</span>{{(form.status === 1 ? '启用' : '禁用')}}
            </el-col>
        </el-row>

    </div>
    </el-dialog>
</template>
<script>
export default {
    data(){
        return {
            dialogVisible:false,
            form:{}
        }
    },
    methods:{
        init(row){
            this.dialogVisible = true
            this.form = JSON.parse(JSON.stringify(row))
        },
    }
}
</script>
<style lang="less" scoped>
.dialog_con {
    height:30vh;
    line-height:30px;
}
</style>
