<template>
  <div class="new_process">

    <div class="search_form">
      <el-row type="flex" :gutter="6">
        <el-col :span="4">
          <el-input v-model="searchForm.processKey" class="format_option" size="small" placeholder="请输入流程标识"
            clearable></el-input>
        </el-col>
        <el-col :span="4">
          <el-input v-model="searchForm.processName" class="format_option" size="small" placeholder="请输入流程名称"
            clearable></el-input>
        </el-col>
        <el-col :span="3">
          <el-select v-model="searchForm.category" class="format_option" size="small" placeholder="请选择流程分类" clearable>
            <el-option v-for="item in typeOptions" :key="item.id" :label="item.categoryName" :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="4">
          <div class="btn_box">
            <el-button @click="search(false)" size="small" class="btn search_btn" icon="el-icon-search"
              type="primary">搜索</el-button>
            <el-button @click="search(true)" size="small" class="btn reset_btn" icon="el-icon-refresh">重置</el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- <div class="operation_btn">
        <el-button
          class="btn add_btn"
          size="small"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd()"
          >新增</el-button
        >
      </div> -->
    <div class="table_box">
      <el-table v-loading="loading" height="600" :data="tableData" row-key="id" style="width: 100%">
        <el-table-column type="index" label="序号" width="50" align="center">
        </el-table-column>
        <el-table-column v-for="(item, index) in tableColumn" :key="index" :prop="item.prop" :label="item.label"
          align="center" :width="item.width">
          <template slot-scope="{row}">
            <el-tag v-if="item.tabStatus" :type="row.suspended ? 'danger' : 'success'">{{ row[item.prop] }}</el-tag>
            <el-tag v-else-if="item.versionStatus" type="default">v{{ row[item.prop] }}</el-tag>
            <el-link v-else-if="item.linkstatus" type="primary" @click="handleProcessView(row)">{{ row[item.prop]
              }}</el-link>
            <div v-else-if="item.checkTime">
              <i class="el-icon-time"></i>
              {{ row[item.prop] | dateTimeFormat }}
            </div>
            <div v-else>{{ row[item.prop] }}</div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="250">
          <template slot-scope="{row}">
            <div class="handle_btn">
              <el-button type="text" @click="handleLaunch(row)" size="small" icon="el-icon-video-play">发起</el-button>

            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="page_box">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentPage"
          :current-page="page.pageNum" :pager-count="5" :page-sizes="[10, 20, 30, 40]" :page-size="10"
          layout="total, sizes, prev, pager, next, jumper" :total="page.total">
        </el-pagination>
      </div>
    </div>

    <launch-process ref="launchProcess" @hidDialog="hidDialog"></launch-process>
    <flow-chart ref="flowChart"></flow-chart>
  </div>

</template>

<script>
import LaunchProcess from './components/launchProcess.vue';
import { processList } from '@/api/officeMan/newProcess'
import { processClassListAll } from '@/api/processMan/processClass.js'
import flowChart from './components/flowChart.vue';
export default {
  components: {
    LaunchProcess,
    flowChart
  },
  data() {
    return {
      loading: false,
      typeOptions: [],
      tableData: [],

      searchForm: {
        processKey: '',
        handleQuery: '',
        category: ''
      },

      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0
      },

      tableColumn: [
        {
          prop: 'processKey',
          label: '流程标识',
          width: '200'
        },
        {
          prop: 'processName',
          label: '流程名称',
          linkstatus: true,
          width: '200'
        },
        {
          prop: 'categoryName',
          label: '流程分类',
          width: '150'
        },
        {
          prop: 'version',
          label: '流程版本',
          versionStatus: true,
          width: ''
        },
        {
          prop: 'suspendedVal',
          label: '状态',
          tabStatus: true,
          width: '100'
        },
        {
          prop: 'deploymentTime',
          label: '部署时间',
          checkTime: true,
          width: '200'
        },
      ]
    };
  },

  beforeCreate() {
    processClassListAll().then((res) => {
      let resData = res.data
      this.typeOptions = resData.map((item) => {
        return { categoryName: `${item.moduleName}-${item.categoryName}`, id: item.id }
      })
    }).catch(() => {
    })
  },

  created() {
    this.loadData();
  },
  methods: {
    hidDialog() {
      this.loadData();
    },
    // 搜索/重置
    search(reset) {
      if (reset) {
        this.searchForm = {
          processKey: '',
          handleQuery: '',
          category: ''
        };
      }
      this.page.pageNum = 1;
      this.loadData();
    },

    // 流程图
    handleProcessView(row) {
      this.$refs.flowChart.init(row)
    },

    // 发起
    handleLaunch(row) {
      this.$refs.launchProcess.init(row)
    },

    loadData() {
      this.loading = true
      let params = {
        ...this.page,
        ...this.searchForm
      }
      processList(params).then((res) => {
        let resData = res.data
        resData.records.forEach((item) => {
          item.suspendedVal = item.suspended ? '挂起' : '激活'
        })
        this.tableData = resData.records
        this.page.total = resData.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 更改每页显示条数
    handleSizeChange(pageSize) {
      this.page.pageSize = pageSize
      this.loadData()
    },

    // 选择页数/点击上一页/下一页
    handleCurrentPage(currentPage) {
      this.page.pageNum = currentPage
      this.loadData()
    }

  }
};
</script>
<style lang="less">
.new_process {
  padding: 16px 12px 0;
}
</style>
