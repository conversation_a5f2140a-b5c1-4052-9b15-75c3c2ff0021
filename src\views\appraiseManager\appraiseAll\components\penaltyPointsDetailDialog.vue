<template>
  <el-dialog title="罚分明细" :visible.sync="dialogVisible" width="1000px" :before-close="handleClose"
    custom-class="supplier-portrait-dialog" :top="'10vh'" destroy-on-close>
    <div v-loading="loading" class="supplier-info">
      <template v-if="supplierData">
        <div class="basic-info">
          <h3>{{ supplierData.name }}</h3>
          <div class="info-row">
            <span>供方编号：{{ supplierData.code }}</span>
            <span>准入时间：{{ supplierData.entryDate }}</span>
          </div>
          <div class="info-row">
            <span>供方编码：{{ supplierData.supplierCode }}</span>
          </div>
        </div>
        <!-- 评分详情 -->
        <div class="score-details">
          <el-tabs v-model="activeTab" class="supplier-tabs" type="card">
            <el-tab-pane v-for="(item, index) in scoreDetails" :key="index" :label="item.typeNo" :name="item.typeNo">
              <template #label>
                <div class="tab-label" @click="handleTabClick(item)">
                  <el-tooltip :content="item.typeName" placement="top">
                    <span>{{ item.typeNo }}({{ item[currentTab] ? item[currentTab].length : 0
                      }}条)</span>
                  </el-tooltip>
                </div>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
        共计<span class="font-bold font-16">{{ tableData.length }}</span>条罚分记录
        <template>
          <el-table :data="tableData" style="width: 100%" max-height="400">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="type" label="维度" width="100" align="center" />
            <el-table-column prop="secondType" label="细分维度" width="120" align="center" />
            <el-table-column prop="score" label="罚分" width="100" align="center" />
            <el-table-column prop="reason" label="原因" align="center" show-overflow-tooltip :min-width="200"
              class-name="reason-column">
            </el-table-column>
            <el-table-column prop="deptName" label="填写部门" width="120" align="center" />
            <el-table-column prop="inspector" label="填写人" width="100" align="center" />
          </el-table>
        </template>
      </template>
    </div>
  </el-dialog>
</template>

<script>
import * as echarts from 'echarts'
import { apiGetPenaltyPointsDetail } from '@/api/appraiseManager/appraiseAll'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: {}
    }
  },

  data() {
    return {
      dialogVisible: false,
      loading: false,
      supplierData: null,
      chart: null,
      currentTabIndex: 0,
      activeTab: '', // 当前激活的标签页
      scoreDetails: [],
      tableData: []
    }
  },
  computed: {
    currentTab() {
      if (this.row) {
        return {
          1: 'qualityList', 2: 'deliveryList', 3: 'serveList', 4:
            'technologyList', 5: 'priceList', 6: 'bidList', 7: 'supplyList'
        }[this.row.type]
      }
      return 1
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.row) {
        this.fetchSupplierData()
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },

  methods: {
    // 模拟获取数据
    async fetchSupplierData() {
      this.loading = true

      const res = await apiGetPenaltyPointsDetail({ ...this.row })
      const { supplierName, supplierCode, supplierNo, joinTime } = this.row

      // 更新模拟据
      this.supplierData = {
        name: supplierName,
        code: supplierCode,
        entryDate: joinTime,
        supplierCode: supplierNo,
      }
      if (!res.data || !res.data.length) {
        this.loading = false
        return
      }
      this.scoreDetails = res.data

      // 设置默认激活的标签页
      this.activeTab = this.scoreDetails[0]?.typeNo
      this.tableData = this.scoreDetails[0][{ 1: 'qualityList', 2: 'deliveryList', 3: 'serveList', 4: 'technologyList', 5: 'priceList', 6: 'bidList', 7: 'supplyList' }[this.row.type]]

      this.loading = false
    },


    handleClose() {
      this.dialogVisible = false
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
      this.supplierData = null
    },

    handleTabClick(item) {
      this.tableData = item[{ 1: 'qualityList', 2: 'deliveryList', 3: 'serveList', 4: 'technologyList', 5: 'priceList', 6: 'bidList', 7: 'supplyList' }[this.row.type]]
    }
  },

  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  }
}
</script>

<style>
.supplier-portrait-dialog {
  min-width: 700px;
}

.supplier-info {
  height: 75vh;
  overflow-y: auto;
  padding: 20px;
}

.basic-info {
  margin-bottom: 20px;
}

h3 {
  margin: 0;
  font-size: 16px;
  font-weight: normal;
  margin-bottom: 15px;
}

.info-row {
  color: #666;
  font-size: 14px;
  line-height: 1.8;
}

.info-row span {
  margin-right: 30px;
}

.score-details {
  margin: 20px 0;
}

.score-row {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.score-item {
  flex: 1;
  text-align: center;
}

.total-score {
  color: #333;
  font-size: 14px;
}

.rank {
  margin: 5px 0;
  font-size: 14px;
}

.rank-text {
  font-size: 15px;
  color: #409EFF;
  font-weight: bold;
}

.material-info {
  margin: 20px 0;
  text-align: center;
}

.material-name {
  text-align: center;
  font-size: 18px;
  margin-bottom: 8px;
  font-weight: bold;
}

.material-desc {
  color: #666;
  font-size: 12px;
}

.metrics-table {
  margin: 20px 60px;
  background-color: #F5F7FA;
  border-radius: 4px;
}

.table-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 15px;
  text-align: center;
}

.table-cell {
  flex: 1;
  font-size: 14px;
}

.values .table-cell {
  color: #333;
}

.radar-chart {
  width: 100%;
  height: 400px;
  margin-top: 20px;
}

/* 覆盖 element-ui 的默认样式 */
:deep(.el-dialog__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #EBEEF5;
}

.supplier-tabs {
  margin-top: 10px;
}

/* 自定义 el-tabs 样式 */
:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  height: 30px;
  line-height: 30px;
  padding: 0 15px;
  font-size: 12px;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
  background-color: #ECF5FF;
  border-radius: 4px;
}

:deep(.el-tabs__nav) {
  border: none;
}

:deep(.el-tabs__active-bar) {
  display: none;
}

:deep(.el-tabs__item:hover) {
  color: #409EFF;
}

:deep(.el-tabs__content) {
  display: none;
  /* 隐藏内容区域，因为我们不需要显示标签页内容 */
}

:deep(.reason-column) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-tooltip {
  max-width: 80vw !important;
  word-break: break-all;
  white-space: pre-wrap;
}

/* 如果上面的方式不��效，可以尝试这个更强的选择器 */
.el-popper.is-light.custom-tooltip {
  max-width: 80vw !important;
  word-break: break-all;
  white-space: pre-wrap;
}

.el-tooltip__popper {
  max-width: 80vw !important;
}

.el-tooltip__popper .popper__arrow {
  border-width: 6px !important;
}

/* 如果上面的不生效，可以尝试这个更具体的选择器 */
body .el-tooltip__popper {
  max-width: 80vw !important;
  line-height: 1.5;
  word-break: break-all;
}
</style>
