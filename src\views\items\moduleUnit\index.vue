<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="机组" prop="posid" label-width="40px">
        <el-select
          v-model="queryParams.posid"
          filterable
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(dict,index) in engineeringList"
            :key="index"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="模块" prop="moduleCode" label-width="40px">
        <el-select
          v-model="queryParams.moduleCode"
          filterable
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(dict,index)  in moduleList"
            :key="index"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status" label-width="40px">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable>
          <el-option
            v-for="dict in status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" />
      <el-table-column label="部套名称" align="center" prop="dwgName" />
      <el-table-column label="部套号" align="center" prop="dwgNo" />
      <el-table-column label="令号" align="center" prop="posid" />
      <el-table-column label="项目名称" align="center" prop="post1">
        <template slot-scope="scope">
          <a @click="item(scope.row)" style="color: #1890FF; cursor: pointer">{{ scope.row.post1 }}</a>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="compFlag">
       <template slot-scope="scope">
          <span
            style="
              font-size: 12px;
              display: inline-block;
              width: 44px;
              height: 28px;
              line-height: 28px;
              border-radius: 3px;
            "
            :class="scope.row.alarmStatus == 0 ? 'bao' :'finish'"
            >{{ scope.row.alarmStatus == 0 ? '未完成':'未完成' }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 部套详情 -->
    <el-dialog
      title="部套详情"
      :visible.sync="open"
      width="600px"
      append-to-body
    >
      <el-row>
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>部套名称：<span>{{dwgList.dwgName}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>部套编码：<span>{{dwgList.dwgNo}}</span></span>
          </div></el-col
        >

        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>模块编号：<span>{{dwgList.moduleCode}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>令号：<span>{{dwgList.posid}}</span></span>
          </div></el-col
        >

        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>部套数量：<span>{{dwgList.dwgNum}}</span></span>
          </div></el-col
        >

        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>已完数量：<span>{{dwgList.compNum}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>预计完成时间：<span>{{dwgList.planDate}}</span></span>
          </div></el-col
        >

        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>终检合格证时间：<span>{{dwgList.reinDate}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>终检合格证标识：<span>{{dwgList.reinCode}}</span></span>
          </div></el-col
        >
      </el-row>
      <!-- <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>齐套：<span>{{dwgList}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>齐套时间：<span>{{dwgList}}</span></span>
          </div></el-col
        >
      </el-row>
      <el-row type="flex" class="row-bg">
        <el-col :span="24"
          ><div class="grid-content bg-purple">
            <span>人工标示：<span>{{dwgList}}</span></span>
          </div></el-col
        >
        <el-col :span="24"
          ><div class="grid-content bg-purple">
            <span>人工标示时间：<span>{{dwgList}}</span></span>
          </div></el-col
        >
      </el-row> -->
      
    </el-dialog>

    <!-- 项目名称详情弹框 -->
    <el-dialog
      title="项目详情"
      :visible.sync="itemOpen"
      width="600px"
      append-to-body
    >
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>机组名称：<span>{{posidList.post1}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>令号：<span>{{posidList.posid}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>产品类型：<span>{{posidList.prodTypeStr}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>机组容量：<span>{{posidList.usr04}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>合同签订日期：<span>{{posidList.zps0177}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>定类：<span>{{posidList.projDl}}</span></span>
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span>所属电厂：<span>{{posidList.powerPl}}</span></span>
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span>板块名称：<span>{{posidList.boardName	}}</span></span>
          </div></el-col
        >
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getEngineeringList,
  getModuleList,
  delPost,
  addPost,
  updatePost,
} from "@/api/items/items";

import { moduleUnitList } from '@/api/items/moduleUnit'

export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 项目名称是否显示弹出层
      itemOpen: false,
      status: [
        { label: "未完成", value: 0 },
        { label: "已完成", value: 1 },
      ],
      dataForm: {
        keyword:'',
      },
      dataList: {
        keyword: '',
        posid: '',
      },

      //机组详情
      posidList:{},
      //部套详情
      dwgList:{},
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        posid: '', //令号
        moduleCode: '', //模块编号
        status: '', //部套状态
        keyword: '', //关键字
      },
      //机组列表
      engineeringList: [],
      //模块列表
      moduleList: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" },
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    if(localStorage.getItem('posid')||localStorage.getItem('moduleCode')){
      this.queryParams.posid=localStorage.getItem('posid')
       this.queryParams.moduleCode=localStorage.getItem('moduleCode')
    }
    this.getEngineeringList();
    this.getModuleList(),
    this.getList();
    localStorage.removeItem('posid')
    localStorage.removeItem('moduleCode')
  },
  mounted(){
    this.dataForm.keyword = this.queryParams.posid
    this.dataList.posid = this.dataForm.keyword
  },
  methods: {
    /** 查询岗位列表 */
    getList() {
      this.loading = true
      let params = {
        ...this.queryParams
      }
      moduleUnitList(params).then((res)=>{
        this.postList = res.data
        this.total = res.total
        this.loading = false
      }).catch(()=>{
        this.loading = false
      })
    },
    //机组列表
    getEngineeringList() {
      getEngineeringList(this.dataForm).then((res) => {
        res.data.forEach((element, index) => {
          this.engineeringList.push({
            value: element.posid,
            label: element.post1+"("+element.posid+")",
          });
        });
      });
    },
    
    //模块列表
    getModuleList() {
      getModuleList(this.dataList).then((res) => {
        res.data.forEach((element, index) => {
          this.moduleList.push({
            value: element.moduleCode,
            label: element.moduleName+"("+element.moduleCode+")",
          });
        });
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.posid=undefined
      this.queryParams.moduleCode=undefined
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加岗位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.dwgList=row
        this.open = true;
        this.title = "部套详情";
     
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    // 项目名称弹出框
    item(row) {
      this.posidList=row
      this.itemOpen = true;
    },
  },
};

</script>

<style  scoped>
::v-deep .el-dialog__body {
  line-height: 30px;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #E8F4FF;
  border: 1px solid rgba(56,154,255,0.4);
  color: #389AFF;
}
.finish {
  background: #E7FAF0;
  border: 1px solid rgba(101,210,153,0);
  color: #65D299;
}
.yu1 {
  color: #fac116;
}
.bao1 {
  color: #e47985;
}
.jin1 {
  color: #389AFF;
}
.finish1 {
  color: #65D299;
}
::v-deep .el-row--flex{
  margin-left: 22px;
}
::v-deep .el-dialog{
  margin-top: 30vh !important;
}
</style>