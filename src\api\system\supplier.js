import request from "@/utils/request";
// 新增供应商
export function addSupplier(data) {
  return request({
    url: "/user-server/supplier/add",
    method: "post",
    data,
  });
}

// 修改状态
export function apiSupplierListStatusEdit(query) {
  return request({
    url: `/sd-server/supplier/no/${query.id}/switch/${query.status}`,
    method: "put",
  });
}

// 删除
export function apiSupplierListDelete(query) {
  return request({
    url: "/sd-server/supplier/no",
    method: "delete",
    params: query,
  });
}
// 生成子账号
export function generateSupplier(id) {
  return request({
    url: `/user-server/supplier/generate/${id}`,
    method: "post",
  });
}
// 修改供应商
export function updateSupplier(data) {
  return request({
    url: "/user-server/supplier/update",
    method: "post",
    data,
  });
}
// 删除供应商
export function deleteSupplier(query) {
  return request({
    url: `/user-server/supplier/delete`,
    method: "get",
    params: query,
  });
}
// 批量删除供应商
export function deletesSupplier(query) {
  return request({
    url: `/user-server/supplier/deletes?ids=${query}`,
    method: "get",
  });
}
// 查询供应商列表
export function getListPage(query) {
  return request({
    url: "/user-server/supplier/getListPage",
    method: "get",
    params: query,
  });
}
// 新增供应商联系人
export function addSupplierContact(data) {
  return request({
    url: "/user-server/supplierContact/add",
    method: "post",
    data,
  });
}
// 删除供应商联系人
export function deleteSupplierContact(query) {
  return request({
    url: `/user-server/supplierContact/delete?ids=${query}`,
    method: "get",
  });
}
// 查询供应商列表
export function getContactListPage(query) {
  return request({
    url: "/user-server/supplierContact/getListPage",
    method: "get",
    params: query,
  });
}
export function getNoPage(query) {
  return request({
    url: "/sd-server/supplier/no/page",
    method: "get",
    params: query,
  });
}
export function getNoAddPage(query) {
  return request({
    url: "/sd-server/supplier/no/add/page",
    method: "get",
    params: query,
  });
}
export function getNoAdd(query) {
  return request({
    url: "/sd-server/supplier/no",
    method: "post",
    data: query,
  });
}
