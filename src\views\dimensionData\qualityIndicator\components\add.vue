<template>
  <el-dialog title="新增" :visible.sync="dialogVisible" width="800px" :before-close="handleClose">
    <el-form :model="form" :rules="rules" ref="form" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="扣分项" prop="secondType">
            <el-select v-model="form.secondType" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in secondCategoryList" :key="item" :label="item" :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <div class="tip-text" v-if="getTipText()">
        <div class="text-nowrap">判罚标准：</div>{{ getTipText() }}
      </div>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="扣分原因" prop="reason">
            <el-input v-model="form.reason" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="扣减分数" prop="score">
            <el-input v-model="form.score" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="供方编号" prop="supplierCode">
            <el-input v-model="form.supplierCode" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供方名称" prop="supplierName">
            <el-input v-model="form.supplierName" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分类编号" prop="typeNo">
            <el-select v-model="form.typeNo" placeholder="请选择" style="width: 100%">
              <el-option v-for="(item, index) in typeNoList" :key="index" :label="item.label" :value="item.typeNo">
                <template #default>
                  <el-tooltip :content="item.typeName" placement="top">
                    <span>{{ item.typeNo }}</span>
                  </el-tooltip>
                </template>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品名称" prop="productName">
            <el-input v-model="form.productName" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="form.projectName" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作令号" prop="posid">
            <el-input v-model="form.posid" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采购合同号" prop="contractNo">
            <el-input v-model="form.contractNo" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="NCR编号" prop="ncr">
            <el-input v-model="form.ncr" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件日期" prop="crtTime">
            <el-date-picker v-model="form.crtTime" type="date" placeholder="请选择日期"  value-format="yyyy-MM-dd"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { apiAdd, apiGetSecondCategory, apiGetAllTypeNo } from '@/api/dimensionData/qualityIndicator'
import { apiGetTypeNoList } from '@/api/appraiseManager/appraiseAll'
import { QualitySecondaryCategory, getQualityTipText } from '@/enums/qualityIndicator'
export default {
  name: 'AddDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      secondCategoryList: [],
      typeNoList: [],
      dialogVisible: false,
      form: {
        secondType: '',
        reason: '',
        score: '',
        supplierCode: '',
        supplierName: '',
        typeNo: '',
        productName: '',
        projectName: '',
        posid: '',
        contractNo: '',
        ncr: ''
      },
      rules: {
        secondType: [{ required: true, message: '请选择扣分项', trigger: 'change' }],
        reason: [{ required: true, message: '请输入扣分原因', trigger: 'blur' }],
        score: [{ required: true, message: '请输入扣减分数', trigger: 'blur' }],
        supplierCode: [{ required: true, message: '请输入供方编号', trigger: 'blur' }],
        supplierName: [{ required: true, message: '请输入供方名称', trigger: 'blur' }],
        typeNo: [{ required: true, message: '请输入分类编号', trigger: 'blur' }],
        productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
        posid: [{ required: true, message: '请输入工作令号', trigger: 'blur' }],
        contractNo: [{ required: true, message: '请输入采购合同号', trigger: 'blur' }],
        ncr: [{ required: true, message: '请输入NCR编号', trigger: 'blur' }]
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.getAllTypeNo()
          this.getSecondCategory()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    getTipText() {
      return getQualityTipText(this.form.secondType)
    },
    // 获取全部的分类编号
    getAllTypeNo() {
      apiGetTypeNoList().then((response) => {
        this.typeNoList = response.data
      })
    },
    // 获取二级分类
    getSecondCategory() {
      apiGetSecondCategory({ type: 1 }).then((response) => {
        this.secondCategoryList = response.data
      })
    },
    handleClose() {
      this.$refs.form.resetFields()
      this.dialogVisible = false
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            await apiAdd(this.form)
            this.$message.success('保存成功')
            this.$emit('refresh')
            this.handleClose()
          } catch (error) {
            this.$message.error(error.message || '保存失败')
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.tip-text {
  color: #409EFF;
  font-size: 12px;
  margin-bottom: 20px;
  padding: 0 20px;
  display: flex;
}

.text-nowrap {
  white-space: nowrap;
}

.el-form {
  padding: 0 20px;
}
</style>
