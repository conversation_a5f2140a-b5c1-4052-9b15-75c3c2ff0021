// 质量指标二级分类及其对应的提示文字
export const QualitySecondaryCategory = {
  INTERNAL_QUALITY: {
    code: "internal_quality",
    name: "内部质量",
    tipText:
      "入厂检验、源地验证、监造、巡检等过程中发现的供应商责任质量问题，一般质量问题每起扣 5 分；构成股份标准的一般质量事故每起扣20 分；构成股份标准的较大及以上质量事故每起扣 50 分。",
  },
  EXTERNAL_QUALITY: {
    code: "external_quality",
    name: "外部质量",
    tipText:
      "用户现场安装、运行等过程中反馈的供应商责任质量问题，一般质量问题每起扣 10 分；构成股份标准的一般质量事故每起扣 20 分；构成股份标准的较大及以上质量事故每起扣 50 分。",
  },
  CUSTOMER_COMPLAINT: {
    code: "customer_complaint",
    name: "顾客投诉",
    tipText:
      "用户通过电话、邮件、信函、400 电话、高层走访等形式投诉的质量问题，涉及供应商责任的一般质量问题每起扣 20 分；构成股份标准的一般质量事故每起扣 30 分，构成股份标准的较大及以上质量事故每起扣 50 分。",
  },
  DELIVERY_TIME: {
    code: "delivery_time",
    name: "交货准时度",
    tipText:
      "每延迟交货 1 天扣 1 分，提前交货超过 15 天后每 1 天扣 0.5 分，最高扣至 50 分，延迟或提前交货对企业生产造成较大影响的，一次扣 50 分，重点项目加倍考核。",
  },
  DELIVERY_ACCURACY: {
    code: "delivery_accuracy",
    name: "交货准确度",
    tipText:
      "交货目的地、品种、规格、数量等不准确，每次扣 10 分；对企业生产造成较大影响或造成库存积压的，一次扣 30 分。",
  },
  AFTER_SALES_TIMELINESS: {
    code: "after_sales_timeliness",
    name: "售后服务及时性",
    tipText:
      "供应商服务及时性差，每起扣 10 分；对企业生产或用户现场造成较大影响的，一次扣 30 分。",
  },
  AFTER_SALES_SATISFACTION: {
    code: "after_sales_satisfaction",
    name: "售后服务满意度",
    tipText:
      "因供应商服务质量差造成用户不满意的，每起扣 10 分；引起用户投诉的，一次扣 30 分。",
  },
};

// 获取提示文字的工具函数
export function getQualityTipText(categoryCode) {
  const category = Object.values(QualitySecondaryCategory).find(
    (item) => item.name === categoryCode
  );
  return category?.tipText || "";
}
