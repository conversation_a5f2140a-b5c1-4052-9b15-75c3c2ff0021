<template>
  <el-dialog title="新增" :visible.sync="dialogVisible" width="840px" append-to-body>
      <el-form
        :model="dialogQueryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="搜索" prop="keyword">
          <el-input
            v-model="dialogQueryParams.keyword"
            placeholder="请输入姓名"
            clearable
            @keyup.enter.native="dialoghHandleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="dialoghHandleQuery"
            >搜索</el-button
          >
        </el-form-item>

        <el-form-item class="lead">
            <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleLead()"
            >关联领导</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        v-loading="dialogLoading"
        :data="dialogTableList"
        @row-click="singleElection"
      >
      <el-table-column type="index" width="30" align="center">
        <template slot-scope="{ row }">
          <el-radio
            class="radio"
            v-model="people"
            :label="row.id"
          ></el-radio>
        </template>
      </el-table-column>

        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="人员姓名" prop="name" align="center" />
        <el-table-column label="所属部门" prop="alarmStatus" align="center" />
        <el-table-column label="手机号" prop="phone" align="center" />
        <el-table-column label="邮箱" prop="email" align="center" />
      </el-table>

      <pagination
        v-show="dialogTotal > 0"
        :total="dialogTotal"
        :page.sync="dialogQueryParams.page"
        :limit.sync="dialogQueryParams.size"
        @pagination="getListUser"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>

        <lead-people ref="leadPeople" @selectData="selectData"></lead-people>
    </el-dialog>
</template>

<script>
import { listUser } from "@/api/system/user.js";
import LeadPeople from './leadPeople.vue';
import { addDutyUser } from "@/api/riskControl/peopleVindicate";
export default {
  components:{LeadPeople},

  data(){
    return{
        dialogVisible:false,
        dialogLoading:false,
        dialogTableList:[],
        people:'',
        dialogTotal: 0,
        dialogQueryParams: {
            keyword: "",
            page: 1,
            size: 10,
            enable: 1,
        },
    }
  },

  created(){},

  methods:{
    init(){
        this.dialogVisible = true
        this.dialogQueryParams.page = 1
        this.getListUser()
    },

    handleLead(){
      if(!this.people){
        this.$message({
          type:'warning',
          message:'请选择数据',
          duration:2000
        })
        return
      }
      this.$refs.leadPeople.init()
    },

    getListUser() {
      this.dialogLoading = true;
      listUser(this.dialogQueryParams).then((res) => {
        this.dialogLoading = false;
        this.dialogTableList = res.data;
        this.dialogTotal = res.total;
      });
    },

    dialoghHandleQuery() {
      this.dialogQueryParams.page = 1;
      this.getListUser();
    },

    singleElection(row){
      this.people = row.id
    },

    selectData(leaderUserId){
      this.leaderUserId = leaderUserId
    },

    submitForm () {
      let params = [
        {
          userId:this.people,
          deptId:'',
          leaderUserId:this.leaderUserId,
        }
      ]
      addDutyUser(params)
        .then((response) => {
          this.$modal.msgSuccess("操作成功");
          this.dialogVisible = false;
          this.$emit('getList');
        })
        .catch(() => {});
    },
    cancel() {
      this.dialogVisible = false;
    },
  },

}

</script>

<style scoped>
.lead {
    float:right;
}
</style>