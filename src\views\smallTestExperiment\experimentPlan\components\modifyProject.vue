<template>
    <el-dialog title="修改检验实验项目" :visible.sync="dialogVisible" width="850px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px" v-loading="loading">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input
                v-model="form.productName"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="工作令号" prop="productCode">
              <el-input
                v-model="form.productCode"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="采购订单号" prop="ebeln">
              <el-input
                v-model="form.ebeln"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="短文本" prop="txz01">
                <el-input
                    v-model="form.txz01"
                    placeholder="请输入"
                />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="项目" prop="ebelp">
                <el-input
                    v-model="form.ebelp"
                    placeholder="请输入"
                />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="物料" prop="matnr">
                <el-input
                    v-model="form.matnr"
                    placeholder="请输入"
                />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="单位" prop="meins">
                <el-input
                    v-model="form.meins"
                    placeholder="请输入"
                />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="数量" prop="menge">
                <el-input
                    v-model="form.menge"
                    placeholder="请输入"
                />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="检验试验计划编号" prop="planCode">
              <el-input v-model="form.planCode" placeholder="请输入" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="部套名称" prop="partName">
              <el-input v-model="form.partName" placeholder="请输入" :disabled="form.roleType == 3" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="部套编码" prop="partCode">
              <el-input v-model="form.partCode" placeholder="请输入" :disabled="form.roleType == 3" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="厂商编码" prop="produceFactory">
                <el-input
                    v-model="form.produceFactory"
                    placeholder="请输入"
                />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="厂商名称" prop="produceFactoryName">
                <el-input
                    v-model="form.produceFactoryName"
                    placeholder="请输入"
                />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="合同号" prop="contractNo">
                <el-input
                    v-model="form.contractNo"
                    placeholder="请输入"
                />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="质检员" prop="finalManagerId">
              <el-select v-model="form.finalManagerId" filterable placeholder="请选择">
                <el-option
                  v-for="item in userList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
</template>

<script>
import { putSmPlan } from "@/api/smallTestExperiment/experimentPlan.js";
import { getHtcUserList } from "@/api/smallTestExperiment/personnel.js";
export default {
  components:{},

  data(){
    return{
        dialogVisible:false,
        btnLoading:false,
        loading:false,
        userList:[],
        form:{
          productName:'',
            productCode:'',
            ebeln:'',
            ebelp:'',
            menge:'',
            meins:'',
            txz01:'',
            matnr:'',
            planCode:'',
            partName:'',
            partCode:'',
            produceFactory:'',
            produceFactoryName:'',
            contractNo:'',
            finalManagerId:''
        },
        rules: {
          // productName:{ required: true, message: '请输入产品名称', trigger: 'blur' }, 
          productCode:{ required: true, message: '请输入工作令号', trigger: 'blur' },   
          ebeln:{required: true, message: '请输入采购订单号', trigger: 'blur'},
          ebelp:{required: true, message: '请输入项目', trigger: 'blur'},
          txz01:{ required: true, message: '请输入短文本', trigger: 'blur' },  
          partName:{ required: true, message: '请输入部套名称', trigger: 'blur' }, 
          partCode:{ required: true, message: '请输入部套编码', trigger: 'blur' }, 
          produceFactory:{ required: true, message: '请输入厂商编码', trigger: 'blur' },  
          produceFactoryName:{ required: true, message: '请输入厂商编码', trigger: 'blur' }, 
          contractNo:{required: true, message: '请输入合同号', trigger: 'blur'},
          finalManagerId:[
            { required: true, message: "请选择质检员", trigger: "blur" },
          ]
        },
    }
  },

  created(){},

  methods:{
    init(row){
        this.btnLoading = false
        this.dialogVisible = true
        this.form = JSON.parse(JSON.stringify(row))
        this.form.finalManagerId = this.form.htcId;
        this.getHtcUserList()
    },

    getHtcUserList() {
        this.loading = true
        getHtcUserList().then((res) => {
          let resData = res.data || []
            this.userList = resData.map((item)=>{
              return {label:item.name,value:String(item.userId)}
            });
            this.loading = false
        }).catch(() => {
            this.loading = false 
        });
    },

    changeRadio(val) {
      this.form.finalManagerDeptId = val.deptId;
      this.form.finalManagerDeptName = val.name;
    },

    confirm(){
        this.$refs["form"].validate((valid) => {
            if (valid) {
              this.btnLoading = true
              putSmPlan(this.form).then((response) => {
                  this.$modal.msgSuccess("修改成功");
                  this.btnLoading = false
                  this.dialogVisible = false;
                  this.$emit('getList')
              }).catch(()=>{
                  this.btnLoading = false
              });
            }
        });
    },
    cancel(){
        this.$refs['form'].resetFields()
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped lang='less'>
</style>