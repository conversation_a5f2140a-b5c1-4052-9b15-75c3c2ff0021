<template>
  <div class="app-container">
    <el-form
      :model="searchForm"
      ref="searchForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="搜索" prop="posid" label-width="40px">
        <el-input
          v-model="searchForm.query"
          placeholder="模型名/令号"
          clearable
          @keyup.enter.native="handleQuery(false)"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery(false)"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="handleQuery(true)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          plain
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="loadData"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
    >
      <el-table-column
      label="序号"
      align="center"
      type="index">
      </el-table-column>

      <el-table-column 
      label="机组类别" 
      align="center"
      prop="name">
      </el-table-column>

      <el-table-column 
      label="令号" 
      align="center"
      prop="codeList"
      >
        <template slot-scope="{row}">
          <div v-if="row.codeList && row.codeList.length > 0">
            已关联<i class="el-icon-search check-icon" @click="checkCode(row)"></i>
          </div>
        </template>
      </el-table-column>

      <el-table-column 
      label="创建时间" 
      align="center"
      prop="crtTime">
        <template slot-scope="{row}">
          <div>{{row.crtTime | dateFormat}}</div>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleInfo(row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-setting "
            @click="handleCode(row)"
            >关联令号</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDel(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="searchForm.pageNum"
      :limit.sync="searchForm.pageSize"
      @pagination="loadData"
    />
    
    <check-info ref="checkInfo"></check-info>
    <add-unit ref="addUnit" @hideDialog="loadData"></add-unit>
    <relevance-code ref="relevanceCode" @hideDialog="loadData"></relevance-code>
    <code-list ref="codeList"></code-list>
  </div>
</template>

<script>
import { modelPage,delModel } from '@/api/expectationAnalysis/modelManager'
import checkInfo from './components/checkInfo'
import addUnit from './components/addUnit.vue';
import codeList from './components/codeList.vue'
import relevanceCode from './components/relevanceCode'
export default {
  components:{checkInfo,addUnit,relevanceCode,codeList},
  data() {
    return {
      total: 0,
      showSearch:true,
      loading:false,
      tableData:[],
      searchForm:{
        query:'',
        pageNum:1,
        pageSize:10
      },
    };
  },
  created() {
    this.loadData()
  },

  methods: {
    handleQuery(flag){
      if(flag){
        this.searchForm.query = ''
        this.searchForm.pageNum = 1
      }
      this.loadData()
    },
    
    // 新增
    handleAdd(row){
      this.$refs.addUnit.init(row)
    },

    // 查看
    handleInfo(row){
      this.$refs.checkInfo.init(row)
    },

    // 关联令号
    handleCode(row){
      this.$refs.relevanceCode.init(row)
    },

    // 查看令号
    checkCode(row){
      this.$refs.codeList.init(row)
    },

    // 删除
    handleDel(row){
      this.$confirm('是否确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(()=>{
        this.loading = false
        delModel(row.id).then((res)=>{
          this.$message({
            type:'success',
            message:'操作成功',
            duration:1500
          })
          this.loadData()
        })
      }).catch(()=>{
        this.loading = false
      });
    },

    loadData(){
      this.loading = true
      let params = {
        ...this.searchForm,
      }
      modelPage(params).then((res)=>{
        let {records,total}  = res.data || {}
        this.tableData = records

        this.total = total
        this.loading = false
      }).catch(()=>{
        this.loading = false
      })
    },
  },
};
</script>

<style scoped>
.check-icon {
  font-size:14px;
  color:#409EFF;
  cursor:pointer;
  margin-left:10px;
}
</style>
