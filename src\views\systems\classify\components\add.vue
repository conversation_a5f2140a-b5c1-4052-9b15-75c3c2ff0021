<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" destroy-on-close append-to-body width="800px"
    :before-close="handleClose">
    <el-form :model="form" :rules="rules" ref="form" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="分类编号" prop="typeNo">
            <el-input v-model="form.typeNo" placeholder="请输入" :disabled="type == 'view'" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="产品名称" prop="typeName">
            <el-input v-model="form.typeName" placeholder="请输入" :disabled="type == 'view'" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="大类" prop="typeX">
            <el-input type="textarea" v-model="form.typeX" placeholder="请输入" :disabled="type == 'view'" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="中类" prop="typeL">
            <el-input type="textarea" v-model="form.typeL" placeholder="请输入" :disabled="type == 'view'" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="小类" prop="typeM">
            <el-input type="textarea" v-model="form.typeM" placeholder="请输入" :disabled="type == 'view'" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="采购部门" prop="purchaseDeptList">
            <el-cascader v-model="form.purchaseDeptList" :options="options" filterable v-loading="treeLoading"
              :props="deptProps" style="width: 100%" clearable :disabled="type == 'view'"></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="技术部门" prop="techDeptList">
            <el-cascader v-model="form.techDeptList" ref="techDeptListRef" filterable v-loading="treeLoading"
              :options="options" :props="deptProps" style="width: 100%" clearable
              :disabled="type == 'view'"></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { apiAdd, apiGetOne, apiUpdate } from '@/api/system/classify.js'
import { QualitySecondaryCategory, getQualityTipText } from '@/enums/qualityIndicator'
import { deptTree } from '@/api/organizationChart/organizationalMan'

export default {
  name: 'AddDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      treeLoading: false,
      deptProps: {
        // expandTrigger: 'hover',
        emitPath: true, // 设置为 true，会返回完整路径
        multiple: true,
        checkStrictly: true, // 这个属性很重要，允许选择任意一级
        children: 'children',
        label: 'name',
        value: 'id'
      },
      options: [],
      title: "新增",
      secondCategoryList: [],
      typeNoList: [],
      dialogVisible: false,
      form: {
        typeNo: "",
        typeName: "",
        typeX: "",
        typeL: "",
        typeM: "",
        purchaseDeptList: ["0201"],
        techDeptList: [],
      },
      rules: {
        typeNo: [{ required: true, message: '请输入分类编号', trigger: 'blur' }],
        typeName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        typeX: [{ required: true, message: '请输入大类', trigger: 'blur' }],
        typeL: [{ required: true, message: '请输入中类', trigger: 'blur' }],
        typeM: [{ required: true, message: '请输入小类', trigger: 'blur' }],
        purchaseDeptList: [{ required: true, message: '请输入采购部门', trigger: 'blur' }],
        techDeptList: [{ required: true, message: '请输入技术部门', trigger: 'blur' }],
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.getAllTree()
          if (this.row) {

            // this.form = this.row
            this.title = { view: '查看', edit: '修改', add: '新增' }[this.type]
            this.getAllTypeNo()

          } else {
            this.title = "新增"
          }
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    checkedDeptChange(val) {
      // 将二维数组转换为一维数组，只保留每个子数组的最后一个值
      return val.map(item => item[item.length - 1]);
    },
    getAllTree() {
      this.treeLoading = true
      deptTree().then((response) => {
        this.options = response.data
        this.treeLoading = false

        // 如果是编辑或查看模式，需要处理回显
        if ((this.type === 'edit' || this.type === 'view') && this.form.id) {
          this.handleCascaderData();
        }
      })
    },
    getTipText() {
      return getQualityTipText(this.form.secondType)
    },
    // 获取详情
    getAllTypeNo() {
      apiGetOne({ id: this.row.id }).then((response) => {
        this.form = {
          ...response.data,
          purchaseDeptList: response.data.purchaseDeptList.map((item) => {
            return item.deptNo
          }),
          techDeptList: response.data.techDeptList.map((item) => {
            return item.deptNo
          }),
        }
        console.log(' this.form', this.form);

      })
    },
    // 获取二级分类
    getSecondCategory() {
      apiGetSecondCategory({ type: 1 }).then((response) => {
        this.secondCategoryList = response.data
      })
    },
    handleClose() {
      this.form = {}
      this.dialogVisible = false
    },
    async handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            if (this.form.id) {
              await apiUpdate({
                ...this.form,
                purchaseDeptList: this.checkedDeptChange(this.form.purchaseDeptList),
                techDeptList: this.checkedDeptChange(this.form.techDeptList),
              })
            } else {
              await apiAdd({
                ...this.form,
                purchaseDeptList: this.checkedDeptChange(this.form.purchaseDeptList),
                techDeptList: this.checkedDeptChange(this.form.techDeptList),
              })
            }
            this.$message.success('保存成功')
            this.$emit('refresh')
            this.handleClose()
          } catch (error) {
            this.$message.error(error.message || '保存失败')
          }
        }
      })
    },
    // 处理级联选择器数据回显
    handleCascaderData() {
      // 处理采购部门数据回显
      if (this.form.purchaseDeptList && !Array.isArray(this.form.purchaseDeptList[0])) {
        const purchaseDeptValues = this.form.purchaseDeptList.map(value => {
          return this.findNodePath(this.options, value);
        }).filter(path => path.length > 0);

        this.form.purchaseDeptList = purchaseDeptValues;
      }

      // 处理技术部门数据回显
      if (this.form.techDeptList && !Array.isArray(this.form.techDeptList[0])) {
        const techDeptValues = this.form.techDeptList.map(value => {
          return this.findNodePath(this.options, value);
        }).filter(path => path.length > 0);

        this.form.techDeptList = techDeptValues;
      }
    },

    // 辅助方法：根据值找到完整路径
    findNodePath(nodes, targetValue, path = []) {
      for (const node of nodes) {
        // 创建当前路径
        const currentPath = [...path, node.id];

        // 如果找到目标值
        if (node.id === targetValue) {
          return currentPath;
        }

        // 如果有子节点，递归查找
        if (node.children && node.children.length) {
          const foundPath = this.findNodePath(node.children, targetValue, currentPath);
          if (foundPath && foundPath.length) {
            return foundPath;
          }
        }
      }

      return [];
    },
  }
}
</script>

<style scoped>
.tip-text {
  color: #409EFF;
  font-size: 12px;
  margin-bottom: 20px;
  padding: 0 20px;
  display: flex;
}

.text-nowrap {
  white-space: nowrap;
}

.el-form {
  padding: 0 20px;
}
</style>
