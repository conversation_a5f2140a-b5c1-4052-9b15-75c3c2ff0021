<template>
    <el-dialog :title="processView.title" :visible.sync="processView.open" width="70%" append-to-body>
        <process-viewer v-loading="loading" class="process_viewer" :key="`designer-${processView.index}`" :xml="processView.xmlData" />
    </el-dialog>
</template>
<script>
import ProcessViewer from '@/components/modules/ProcessViewer'
import {getBpmnXml} from '@/api/processMan/processModel'
export default {
    components:{ProcessViewer},
    data(){
        return {
            rowData:null,
            loading:false,
            processView: {
                title: '',
                open: false,
                index: undefined,
                xmlData:"",
            }
        }
    },
    methods:{
        init(row){
            this.processView.open = true
            this.rowData = row;
            this.processView.title = "流程图";
            this.processView.index = row.modelId;
            this.loadData()
        },
        loadData(){
            this.loading = true
            getBpmnXml(this.rowData.modelId).then(res => {
                this.processView.xmlData = res.data;
                this.loading = false
            }).catch(()=>{
                this.loading = false
            })
        }
    }
}
</script>
<style lang="less" scoped>
.process_viewer {
    height:60vh;
}
</style>
