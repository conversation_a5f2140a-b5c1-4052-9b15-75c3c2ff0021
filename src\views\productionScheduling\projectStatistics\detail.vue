<template>
  <div class="app-container">
    <!-- 添加返回按钮 -->
    <div class="back-button-container">
      <el-button type="primary" icon="el-icon-back" @click="handleBack">返回</el-button>
    </div>

    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <!-- <el-form-item label="日期">
        <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" />
      </el-form-item> -->
      <el-form-item label="搜索">
        <el-input v-model="queryParams.query" placeholder="请输入关键字" clearable style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <!-- 第一行 -->
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card pink">
          <div class="card-title">项目令号</div>
          <div class="card-number">{{ summaryData.posid || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card orange">
          <div class="card-title">供应商数量</div>
          <div class="card-number">{{ summaryData.supplierNum || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card green">
          <div class="card-title">铸锻件数量</div>
          <div class="card-number">{{ summaryData.totalNum || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card red">
          <div class="card-title">报警</div>
          <div class="card-number">{{ summaryData.alarmNum || 0 }}<span class="unit"></span></div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card yellow">
          <div class="card-title">预警</div>
          <div class="card-number">{{ summaryData.earlyAlarmNum || 0 }}<span class="unit"></span></div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card blue">
          <div class="card-title">进行中</div>
          <div class="card-number">{{ summaryData.normalNum || 0 }}<span class="unit"></span></div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card cyan">
          <div class="card-title">已完成</div>
          <div class="card-number">{{ summaryData.finishNum || 0 }}<span class="unit"></span></div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card purple">
          <div class="card-title">完成率</div>
          <div class="card-number">{{ summaryData.finishRate || 0 }}<span class="unit">%</span></div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card pink-light">
          <div class="card-title">按期完成率</div>
          <div class="card-number">{{ summaryData.finishOnTimeRate || 0 }}<span class="unit">%</span></div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card green-light">
          <div class="card-title">按期完成数量</div>
          <div class="card-number">{{ summaryData.finishOnTimeNum || 0 }}<span class="unit"></span></div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card orange-light">
          <div class="card-title">超期完成率</div>
          <div class="card-number">{{ summaryData.finishOutTimeRate || 0 }}<span class="unit">%</span></div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="statistics-card red-light">
          <div class="card-title">超期完成数量</div>
          <div class="card-number">{{ summaryData.finishOutTimeNum || 0 }}<span class="unit"></span></div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 表格 -->
    <el-table v-loading="loading" :data="tableData" @sort-change="handleSortChange"
      style="width: 100%; margin-top: 20px">
      <el-table-column label="序号" type="index" width="60" align="center">
        <template slot-scope="scope">
          {{ queryParams.pageSize * (queryParams.pageNum - 1) + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="供应商名称" prop="supplierName" align="center" width="200" show-overflow-tooltip />
      <el-table-column label="铸锻件总数量" prop="totalNum" align="center" sortable="custom" width="150" />
      <el-table-column label="报警" prop="alarmNum" align="center" sortable="custom" width="120" />
      <el-table-column label="预警" prop="earlyAlarmNum" align="center" sortable="custom" width="120" />
      <el-table-column label="进行中" prop="normalNum" align="center" sortable="custom" width="120" />
      <el-table-column label="已完成" prop="finishNum" align="center" sortable="custom" width="120" />
      <el-table-column label="完成率" prop="finishRate" align="center" sortable="custom" width="150">
        <template slot-scope="scope">
          {{ scope.row.finishRate }}%
        </template>
      </el-table-column>
      <el-table-column label="按期完成率" prop="finishOnTimeRate" align="center" sortable="custom" width="150">
        <template slot-scope="scope">
          {{ scope.row.finishOnTimeRate }}%
        </template>
      </el-table-column>
      <el-table-column label="按期完成数量" prop="finishOnTimeNum" align="center" sortable="custom" width="150" />
      <el-table-column label="超期完成率" prop="finishOutTimeRate" align="center" sortable="custom" width="150">
        <template slot-scope="scope">
          {{ scope.row.finishOutTimeRate }}%
        </template>
      </el-table-column>
      <el-table-column label="超期完成数量" prop="finishOutTimeNum" align="center" sortable="custom" width="150" />
    </el-table>

    <!-- 添加分页组件 -->
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
      :current-page="queryParams.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="total" style="margin-top: 20px; text-align: right" />
  </div>
</template>

<script>
import { getSupplierCompletionDetail, getSupplierCompletionSummary } from '@/api/productionScheduling/projectStatistics'
export default {
  name: 'SupplierCompletion',
  data() {
    return {
      loading: false,
      queryParams: {
        dateRange: [],
        query: '',
        pageNum: 1,
        pageSize: 10,
        sortType: '',
        sort: ''
      },
      tableData: [],
      summaryData: {},
      total: 0
    }
  },
  created() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      this.loading = true
      if (!this.$route.query.row) {
        this.$message.error('请先选择供应商')
        return
      }
      this.summaryData = this.$route.query.row || {},
        Promise.all([
          this.getSupplierCompletionDetail(),
          // this.getSupplierCompletionSummary()
        ]).finally(() => {
          this.loading = false
        })
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.queryParams = {
        dateRange: [],
        query: '',
        pageNum: 1,
        pageSize: 10,
        sortType: '',
        sort: ''
      }
      this.handleQuery()
    },
    getSupplierCompletionDetail() {
      const params = {
        posid: this.$route.query.row.posid,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        query: this.queryParams.query,
        startTime: this.queryParams.dateRange && this.queryParams.dateRange[0],
        endTime: this.queryParams.dateRange && this.queryParams.dateRange[1],
        sortType: this.queryParams.sortType,
        sort: this.queryParams.sort
      }
      return getSupplierCompletionDetail(params).then(res => {
        this.tableData = res.data.records
        this.total = res.data.total
      })
    },
    getSupplierCompletionSummary() {
      const params = {
        query: this.queryParams.query,
        startTime: this.queryParams.dateRange && this.queryParams.dateRange[0],
        endTime: this.queryParams.dateRange && this.queryParams.dateRange[1]
      }
      return getSupplierCompletionSummary(params).then(res => {
        this.summaryData = res.data
      })
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.handleQuery()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.handleQuery()
    },
    handleBack() {
      this.$router.go(-1)
    },
    handleSortChange(column) {
      //sortType  sort
      if (column.prop) {
        this.queryParams.sortType = column.prop
        // 正序倒序,1正序,2倒序
        this.queryParams.sort = column.order === 'ascending' ? '1' : '2'
      } else {
        this.queryParams.sortType = ''
        this.queryParams.sort = ''
      }
      this.handleQuery()
    }
  }
}
</script>

<style scoped>
.statistics-cards {
  margin: 20px auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.statistics-card {
  height: 120px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.card-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.card-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.unit {
  font-size: 14px;
  margin-left: 4px;
}

/* 卡片背景色 */
.pink {
  background-color: #fef0f0;
}

.orange {
  background-color: #fdf6ec;
}

.green {
  background-color: #f0f9eb;
}

.blue {
  background-color: #ecf5ff;
}

.cyan {
  background-color: #e1f3f1;
}

.purple {
  background-color: #f5f0fa;
}

.pink-light {
  background-color: #feeff1;
}

.red {
  background-color: #fef0f0;
}

.yellow {
  background-color: #fdf6ec;
}

.red-light {
  background-color: #fff1f0;
}

.green-light {
  background-color: #f0f9eb;
}

.orange-light {
  background-color: #fff7e6;
}

/* 添加返回按钮样式 */
.back-button-container {
  margin-bottom: 20px;
}
</style>
