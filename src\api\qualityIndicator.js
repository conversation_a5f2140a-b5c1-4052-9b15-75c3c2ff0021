import request from "@/utils/request";

// 获取扣分详情
export function getDeductDetail(id) {
  return request({
    url: "/api/quality/deduct/detail",
    method: "get",
    params: { id },
  });
}

// 修改扣分
export function updateDeduct(data) {
  return request({
    url: "/api/quality/deduct/update",
    method: "post",
    data,
  });
}

// 上传证明材料
export function uploadFile(data) {
  return request({
    url: "/api/quality/deduct/upload",
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}

// 删除扣分
export function deleteDeduct(data) {
  return request({
    url: "/api/quality/deduct/delete",
    method: "post",
    data,
  });
}

// 获取修改历史
export function getDeductHistory(id) {
  return request({
    url: "/api/quality/deduct/history",
    method: "get",
    params: { id },
  });
}
