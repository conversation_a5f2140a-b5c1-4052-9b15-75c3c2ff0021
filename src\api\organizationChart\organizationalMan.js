import request from '@/utils/request'

// 获取部门树（所有）
export function deptTree(query) {
    return request({
        url: `/user-server/dept/tree`,
        method: 'get',
        params: query
    });
}


// 新增部门
export function addDept(query) {
    return request({
        url: `/user-server/dept`,
        method: 'post',
        data: query
    });
}

// 修改部门
export function editDept(query) {
    return request({
        url: `/user-server/dept`,
        method: 'put',
        data: query
    });
}

// 获取部门列表
export function deptList(query) {
    return request({
        url: `/user-server/dept/page`,
        method: 'get',
        params: query
    });
}

// 批量删除部门
export function delDept(deptId) {
    return request({
        url: `/user-server/dept/${deptId}`,
        method: 'delete'
    });
}

// 获取部门人员
export function deptUser(query) {
    return request({
        url: `/user-server/dept/user/dept/page`,
        method: 'get',
        params: query
    });
}

// 获取部门正常部门树
export function normalDeptTree() {
    return request({
        url: `/user-server/dept/tree/valid`,
        method: 'get',
    });
}

// 同步部门信息
export function syncDeptInfo() {
    return request({
        url: `/user-server/dept/im/sync`,
        method: 'get',
    });
}



// 获取专业室信息
export function specialtyRoomList() {
    return request({
        url: `/user-server/dept/dms/sign`,
        method: 'get',
    });
}


// 获取机构信息
export function workshopList() {
    return request({
        url: `/user-server/dept/dm/workshop`,
        method: 'get',
    });
}





