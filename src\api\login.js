import request from '@/utils/request'
// 登录方法
export function login(username, password, verifyCode, securityCode) {

  const data = {
    username,
    password,
    verifyCode,
    securityCode
  }
  return request({
    url: `/user-server/user/loginByVerifyWeb`,
    method: 'post',
    params: data,
  })
}
// export function login(username, password, code, uuid) {
//   const data = {
//     username,
//     password,
//     code,
//     uuid
//   }
//   return request({
//     url: '/login',
//     headers: {
//       isToken: false
//     },
//     method: 'post',
//     data: data
//   })
// }

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data,

  })
}


//获取用户详细信息
// export function getInfo() {
//   return request({
//     url: '/getInfo',
//     method: 'get'
//   })
// }

export function getInfo(userType) {
  return request({
    url: `/user-server/user/getUserInfo/${userType}`,
    method: 'get',
  })
}
// 退出方法
export function logout() {
  return request({
    url: '/user-server/user/logout',
    method: 'get',
  })
}
// export function logout() {
//   return request({
//     url: '/logout',
//     method: 'post'
//   })
// }

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/user-server/user/getVerify',
    method: 'get',
  })
}

//首页统计
export function homePage() {
  return request({
    url: '/back-server/engineeringUnit/homePage',
    method: 'get',
  })
}

//首页统计 (厂商)
export function homePageSup() {
  return request({
    url: '/mes-server/mes/part/home?num=10',
    method: 'get',
  })
}