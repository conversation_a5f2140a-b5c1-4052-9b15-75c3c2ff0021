import request from '@/utils/request'
// 模块列表
export function getModuleListForWeb(param) {
  return request({
    url: '/back-server/engineeringUnit/getModuleListForWeb',
    method: 'get',
    params: param,
  })
}

//导出
export function exportData(query) {
  return request({
    url: '/back-server/engineeringUnit/exportExcelModuleListForWeb',
    method: 'get',
    params: query,
    responseType: 'blob', // important
  })
}
