<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="年份">
        <el-date-picker
          v-model="queryParams.year"
          style="width: 200px"
          type="year"
          value-format="yyyy"
          placeholder="选择年"
          @change="yearChange"
          :clearable="false"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="部门" prop="deptNo">
        <el-select
          style="width: 200px"
          :key="queryParams.year"
          v-model="queryParams.deptNo"
          placeholder="请选择"
          clearable
          @change="handleDeptChange"
          @clear="handleClearDept"
        >
          <el-option
            v-for="item in secondCategoryList"
            :key="item.deptNo"
            :label="item.deptName"
            :value="item.deptNo"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核员" prop="auditName">
        <el-input
          v-model="queryParams.auditName"
          style="width: 300px"
          placeholder="供方编码/供方编号/供方名称/检查员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select
          style="width: 100%"
          v-model="queryParams.auditStatus"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in [
              { value: 0, label: '待审核' },
              { value: 1, label: '审核通过' },
              { value: 2, label: '审核驳回' },
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属分类" prop="typeNo">
        <el-select
          style="width: 100%"
          v-model="queryParams.typeNo"
          placeholder="请选择"
        >
          <el-option
            v-for="item in posidList"
            :key="item"
            :label="item"
            :value="item"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="query">
        <el-input
          v-model="queryParams.query"
          style="width: 300px"
          placeholder="供方编码/供方编号/供方名称/检查员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div>
      <el-button type="primary" size="mini" @click="handleQuery"
        >导出（已选{{ ids.length }}条）</el-button
      >
      <el-button type="primary" size="mini" @click="handleTransfer(false)"
        >批量转办</el-button
      >
    </div>
    <div class="mb8 justify-between">
      <el-row :gutter="10" class="item-center">
        <!-- <el-col :span="1.5">
          <el-button type="primary" size="mini" @click="openBatch()">导出（已选0条）</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-download" size="mini" @click="openBatch()">导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-download" size="mini" @click="openBatch()">模板下载</el-button>
        </el-col> -->
      </el-row>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </div>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" fixed="left" />
      <el-table-column label="序号" type="index" fixed="left" width="120" />
      <el-table-column label="审核部门" prop="deptName" min-width="100" />
      <el-table-column label="审核员" prop="auditName" />
      <el-table-column label="审核状态" prop="auditStatus">
        <template slot-scope="scope">
          <!-- <el-tag>{{
            {
              0: "未审核",
              1: "已审核",
              2: "审核不通过",
            }[scope.row.auditStatus]
          }}</el-tag> -->
          <div
            :style="{
              color:
                scope.row.auditStatus === 0
                  ? 'orange'
                  : scope.row.auditStatus === 1
                  ? '#67c23a'
                  : '#f56c6c',
            }"
          >
            {{
              scope.row.auditStatus === 0
                ? "待审核"
                : scope.row.auditStatus === 1
                ? "审核通过"
                : "审核驳回"
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="审核备注" prop="auditRemark" />
      <el-table-column label="供方编码" prop="supplierCode" min-width="100" />
      <el-table-column label="供方编号" prop="supplierNo" />
      <el-table-column
        label="供方名称"
        prop="supplierName"
        width="200"
        show-overflow-tooltip
      />
      <el-table-column label="所属分类" prop="typeNo" width="120" />
      <el-table-column
        label="研发投入分数"
        prop="researchScore"
        min-width="100"
      />
      <el-table-column
        label="资质证明分数"
        prop="effectScore"
        min-width="100"
      />
      <!-- <el-table-column label="证明文件" prop="effectScore">
        <template slot-scope="scope">
          <i class="el-icon-success"></i>
        </template>
      </el-table-column> -->

      <el-table-column
        width="100"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.auditStatus === 1 || scope.row.auditStatus === 2"
            type="text"
            size=""
            @click="handleAdd(scope.row, 1)"
            >查看</el-button
          >
          <el-button
            v-if="scope.row.auditStatus === 0"
            type="text"
            size=""
            @click="handleAdd(scope.row, 2)"
            >审核</el-button
          >
          <el-button
            v-if="scope.row.auditStatus === 0"
            type="text"
            size=""
            @click="handleTransfer(scope.row)"
            >转办</el-button
          >
          <el-button
            v-if="scope.row.auditStatus === 1"
            type="text"
            size=""
            @click="handleAdd(scope.row, 3)"
            >修改</el-button
          >
        </template></el-table-column
      >
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <add ref="addDialog" @refresh="handleQuery"></add>
    <process-import ref="processImport"></process-import>
    <transfer ref="transfer" @update="handleQuery"></transfer>
  </div>
</template>

<script>
import add from "./components/add.vue";
import transfer from "./components/transfer.vue";
import {
  getProductPage,
  delProductById,
  putProductStatus,
  putProductEnable,
  getProductTypeAll,
} from "@/api/dimensionData/nodeManagement.js";
import ProcessImport from "./components/processImport.vue";
import {
  apiGetPage,
  apiGetSecondCategory,
  exportData,
  apiGetDeptByYear,
} from "@/api/dimensionData/techniinformation.js";
export default {
  components: {
    add,
    ProcessImport,
    transfer,
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: undefined, //模块名称
        auditName: undefined,
        year: new Date().getFullYear().toString(), // 默认当前年份
        deptNo: "",
        auditStatus: undefined,
        typeNo: undefined,
      },
      //机组详情
      posidList: {},
      // 部门列表
      secondCategoryList: [],
      // 排序选项
      optionList: [
        { value: "rdRatio", label: "研发占比" },
        { value: "rdInput", label: "研发投入" },
        { value: "rdScore", label: "研发得分" },
        { value: "industryScore", label: "行业影响力得分" },
      ],
    };
  },
  created() {
    const { row } = this.$route.query;
    if (row) {
      this.queryParams.typeNo = row.typeNo;
      this.queryParams.query = row.supplierName;
    }

    this.getDeptList();
    this.getProductTypeAlls();
    this.getList();
  },

  methods: {
    getProductTypeAlls() {
      getProductTypeAll().then((res) => {
        this.posidList = res.data || [];
      });
    },
    // 年份变更
    yearChange() {
      this.getDeptList();
      this.queryParams.deptNo = "";
      this.handleQuery();
    },

    // 获取部门列表
    getDeptList() {
      apiGetDeptByYear({ year: this.queryParams.year }).then((res) => {
        this.secondCategoryList = res.data || [];
        this.$nextTick(() => {
          this.queryParams.deptNo = "";
        });
      });
    },

    // 部门变更
    handleDeptChange() {
      this.handleQuery();
    },

    // 清除部门
    handleClearDept() {
      this.queryParams.deptNo = "";
      this.handleQuery();
    },

    // 状态切换
    switchChange(row) {
      this.loading = true;
      putProductStatus({
        id: row.id,
        status: row.status,
      })
        .then((res) => {
          this.$modal.msgSuccess("操作成功");
          this.loading = false;
          this.getList();
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 批量启用
    openBatch() {
      this.loading = true;
      putProductEnable(this.ids)
        .then((res) => {
          this.$modal.msgSuccess("操作成功");
          this.loading = false;
          this.getList();
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 导入
    handleImp() {
      this.$refs.processImport.init();
    },

    /** 分页查询 */
    getList() {
      this.loading = true;
      getProductPage(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    /** 新增按钮操作 */
    handleAdd(row, type) {
      this.$refs.addDialog.show(row);
      this.$refs.addDialog.type = type;
      this.$refs.addDialog.id = row.id;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delProductById(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 转办 */
    handleTransfer(row) {
      if (row) {
        this.$refs.transfer.query.id = row.id;
        this.$refs.transfer.query.ids = null;
        this.$refs.transfer.userData.type = "transfer";
        this.$refs.transfer.userData.title = "分配";
        this.$refs.transfer.userData.open = true;
        this.$refs.transfer.getTreeSelect();
      } else {
        if (!this.ids.length) return this.$message.warning("请选择数据");
        this.$refs.transfer.query.ids = this.ids;
        this.$refs.transfer.query.id = null;
        this.$refs.transfer.userData.type = "transfer";
        this.$refs.transfer.userData.title = "分配";
        this.$refs.transfer.userData.open = true;
        this.$refs.transfer.getTreeSelect();
      }
    },
  },
};
</script>

<style scoped>
.item-center {
  display: flex;
  align-items: center;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.font-size-14 {
  font-size: 14px;
}

.grey {
  color: #999;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
</style>
