<template>
  <el-dialog title="供方准入时间" :visible.sync="dialogVisible" width="1000px" :before-close="handleClose">
    <div class="search-box">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item>
          <el-input v-model="queryParams.keyword" placeholder="供方编码/供方编号/供方名称" style="width: 300px;"
            clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
        <!-- <el-form-item style="margin-left: 20px;">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
        </el-form-item> -->
      </el-form>
    </div>

    <el-table v-loading="loading" :data="tableData" style="width: 100%" max-height="400">
      <el-table-column type="index" label="序号" width="60" align="center">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="supplierCode" label="供应商编码" width="200"></el-table-column>
      <el-table-column prop="supplierName" label="供应商名称" width="220"></el-table-column>
      <el-table-column prop="typeNo" label="分类编号" width="150" align="center">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.typeName" placement="top">
            <div>{{ scope.row.typeNo }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="joinTime" label="准入时间" width="200"></el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加/编辑弹窗 -->
    <el-dialog :title="editTitle" :visible.sync="editDialogVisible" width="800px" append-to-body>
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <!-- <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="厂商名称" prop="supplierName">
              <el-input v-model="form.supplierName" placeholder="请输入" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="厂商编码" prop="supplierCode">
              <el-input v-model="form.supplierCode" placeholder="请输入" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类编号" prop="typeNo">
              <el-select v-model="form.typeNo" placeholder="请选择" style="width: 100%" disabled>
                <el-option v-for="item in typeNoList" :key="item.typeNo" :label="item.typeNo" :value="item.typeNo">
                  <template #default>
                    <el-tooltip :content="item.typeName" placement="top">
                      <span>{{ item.typeNo }}</span>
                    </el-tooltip>
                  </template>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="招标编号" prop="bidNo">
              <el-input v-model="form.bidNo" placeholder="请输入" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同编号" prop="contractNo">
              <el-input v-model="form.contractNo" placeholder="请输入" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同额" prop="contractAmount">
              <el-input v-model="form.contractAmount" placeholder="请输入" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="准入时间" prop="joinTime">
              <el-date-picker v-model="form.joinTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { apiGetTypeNoList } from '@/api/appraiseManager/appraiseAll'
import { apiAdd, apiUpdate, apiGetNoJoinTime, apiPutNoJoinTime } from '@/api/dimensionData/vendorIndicator'
import Pagination from '@/components/Pagination'

export default {
  name: 'joinTimeDialog',
  components: {
    Pagination
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      editDialogVisible: false,
      editTitle: '新增',
      loading: false,
      total: 0,
      typeNoList: [],
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: ''
      },
      form: {
        id: undefined,
        supplierCode: '',
        supplierName: '',
        typeNo: '',
        bidNo: '',
        contractNo: '',
        contractAmount: '',
        joinTime: ''
      },
      rules: {
        supplierCode: [
          { required: true, message: '请输入厂商编码', trigger: 'blur' }
        ],
        supplierName: [
          { required: true, message: '请输入厂商名称', trigger: 'blur' }
        ],
        typeNo: [
          { required: true, message: '请选择分类编号', trigger: 'change' }
        ],
        bidNo: [
          { required: true, message: '请输入招标编号', trigger: 'blur' }
        ],
        contractNo: [
          { required: true, message: '请输入合同编号', trigger: 'blur' }
        ],
        contractAmount: [
          { required: true, message: '请输入合同额', trigger: 'blur' }
        ],
        joinTime: [
          { required: true, message: '请选择准入时间', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.getTypeNoList()
          this.getList()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    // 获取分类编号列表
    getTypeNoList() {
      apiGetTypeNoList().then(response => {
        this.typeNoList = response.data
      })
    },
    // 获取数据列表
    getList() {
      this.loading = true
      apiGetNoJoinTime({
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        query: this.queryParams.keyword
      }).then(response => {
        this.tableData = response.data.records
        this.total = response.data.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 搜索
    handleSearch() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        keyword: ''
      }
      this.getList()
    },
    // 新增
    handleAdd() {
      this.editTitle = '新增'
      this.form = {
        id: undefined,
        supplierCode: '',
        supplierName: '',
        typeNo: '',
        bidNo: '',
        contractNo: '',
        contractAmount: '',
        joinTime: ''
      }
      this.editDialogVisible = true
    },
    // 编辑
    handleEdit(row) {
      this.editTitle = '编辑'
      this.form = JSON.parse(JSON.stringify(row))
      this.editDialogVisible = true
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.id) {
            // 更新
            apiPutNoJoinTime(this.form).then(() => {
              this.$modal.msgSuccess('修改成功')
              this.editDialogVisible = false
              this.getList()
            })
          } else {
            // 新增
            apiAdd(this.form).then(() => {
              this.$modal.msgSuccess('新增成功')
              this.editDialogVisible = false
              this.getList()
            })
          }
        }
      })
    },
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
    },
    // 确认
    handleConfirm() {
      this.dialogVisible = false
      this.$emit('refresh')
    }
  }
}
</script>

<style lang="scss" scoped>
.search-box {
  margin-bottom: 20px;
}
</style>
