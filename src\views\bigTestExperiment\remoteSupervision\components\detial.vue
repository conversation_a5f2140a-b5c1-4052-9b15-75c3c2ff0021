<template>
  <div>
    <el-dialog :title="title" :visible.sync="dialogVisible" append-to-body width="1100px" @close="closeDialog">
      <div class="dialog-content" v-loading="btnLoading">
        <div class="content">
          <el-row class="content-info">
            <el-col :span="24">
              <div class="grid-content bg-purple">
                <span>
                  部套名称：<span>{{ detailsData.partName }}</span>
                </span>
              </div>
            </el-col>

            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>
                  部套编码：<span>{{ detailsData.partCode }}</span>
                </span>
              </div>
            </el-col>

            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>
                  产品名称：<span>{{ detailsData.productName }}</span>
                </span>
              </div>
            </el-col>

            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>
                  工作令号：<span>{{ detailsData.productCode }}</span>
                </span>
              </div>
            </el-col>

            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>
                  步骤节点：<span>{{ detailsData.finishNodeNum }} / {{ detailsData.nodeNum }}</span>
                </span>
              </div>
            </el-col>

            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>生产厂商：<span>
                    {{
                      detailsData.produceFactoryName
                    }}
                  </span></span>
              </div>
            </el-col>

            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>
                  厂商联系方式：<span>{{ detailsData.factoryTel }}</span>
                </span>
              </div>
            </el-col>

            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>
                  短文本：<span>{{ detailsData.txz01 }}</span>
                </span>
              </div>
            </el-col>

            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>
                  炉号：<span>{{ detailsData.heatNo }}</span>
                </span>
              </div>
            </el-col>

            <el-col :span="24">
              <div class="grid-content bg-purple-light">
                <span>过程类型：<span>{{
                  getProceduralType(detailsData.processType)
                    }}</span>
                </span>
              </div>
            </el-col>

          </el-row>

          <div class="audit-log-title">审核日志</div>
          <div class="check-record-box">
            <el-timeline>
              <el-timeline-item color="#1890FF" v-for="(item, index) in detailsData.checkRecordList" :key="index">
                <div class="audit-log-item">
                  <div class="log-time">{{ item.checkDate }}</div>
                  <div class="log-text">{{ item.content }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>

        <div class="block">
          <div class="top-btn-box" v-if="showAddBtn(detailsData)">
            <el-button type="primary" size="small" plain @click="handleBasicInfo()">基本信息填写</el-button>

          </div>
          <div class="tip" v-if="showTip(detailsData)">
            请填写基本信息
          </div>

          <el-timeline v-else>
            <el-timeline-item color="#1890FF" v-for="(item, index) in detailsData.planNodeList" :key="index">
              <el-collapse accordion>
                <el-collapse-item>
                  <template slot="title">
                    <!-- <div class="timeline-item-top"></div> -->

                    <div class="top-left">
                      <p>
                        <span class="top-title">{{ item.nodeName }}</span>
                        <span class="code" v-if="item.standardNo">（{{ item.standardNo }}）</span>
                        <span class="file-flag" v-if="item.attachmentStatus === 1">（有附件）</span>
                      </p>

                      <div class="top-content">
                        <span>{{ item.nodeTip }}</span>
                      </div>

                    </div>
                    <div class="top-right">
                      <div :class="item.status | getStatus(statusOptions, 'className')">{{ item.status |
                        getStatus(statusOptions, 'label') }}</div>
                    </div>
                  </template>

                  <div>
                    <div color="#DCDFE6" v-for="(aItem, aIndex) in item.planNodeInfoList" :key="aIndex">
                      <div class="item-header">
                        <div class="item-header-left">
                          {{ aItem.nodeName }}
                        </div>
                      </div>
                      <div class="timeline-item-card">
                        <div class="card-left">
                          <span>{{ aItem.name }}</span>

                          <div v-if="
                            getRole(roleType) == aItem.nodeBy &&
                            item.status != 2
                          ">
                            <uploadFile v-model="aItem.fileList" :uploadUrl="uploadUrlFile" show-file-list>
                            </uploadFile>
                          </div>

                          <div class="file-box" v-else>
                            <div v-for="(fileItem, index) in aItem.fileList" :key="index">
                              <el-button type="text" @click="handlePreview(fileItem)">
                                {{ fileItem.name }}
                              </el-button>
                            </div>
                          </div>

                          <div class="card-bottom" v-if="aItem.commitUserName">
                            <span>{{ aItem.commitUserName }}</span>
                            <span>{{
                              formatDate(aItem.commitTime)
                              }}</span>
                            <i style="color: #67c23a" class="el-icon-success"></i>
                          </div>
                        </div>

                        <div class="card-right">
                          <el-button v-if="roleType == 2 && getRole(roleType) == aItem.nodeBy && item.status == 0"
                            type="primary" size="small" plain @click="qcSubmit(aItem)">提交</el-button>
                          <div v-if="roleType == 3 && getRole(roleType) == aItem.nodeBy && item.status == 1">
                            <el-button @click="setNodeAudit(aItem, 1)" type="success" size="small"
                              icon="el-icon-success" plain>通过
                            </el-button>

                            <el-button @click="setNodeAudit(aItem, 0)" type="danger" size="small" icon="el-icon-success"
                              plain>驳回
                            </el-button>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 其他节点 -->
                  <div v-if="item.finalNode == 0">
                    <el-timeline>
                      <el-timeline-item class="card-item" v-for="(bItem, bIndex) in item.planNodeList"
                        :key="'b' + bIndex">
                        <div class="item-header">
                          <div class="item-header-left">
                            {{ bItem.nodeName }}
                          </div>
                          <div class="item-header-right" :class="bItem.status | getStatus(statusOptions, 'className')">
                            {{ bItem.status | getStatus(statusOptions, 'label') }}
                          </div>
                        </div>
                        <div>

                          <div color="#DCDFE6" v-for="(cItem, cIndex) in bItem.planNodeInfoList" :key="'b' + cIndex">
                            <div class="item-header">
                              <div class="item-header-left">
                                {{ cItem.nodeName }}
                              </div>
                            </div>
                            <div class="timeline-item-card">
                              <div class="card-left">
                                <span>{{ cItem.name }}</span>

                                <div v-if="
                                  getRole(roleType) == cItem.nodeBy &&
                                  bItem.status != 2
                                ">
                                  <uploadFile v-model="cItem.fileList" :uploadUrl="uploadUrlFile" :status="bItem.status"
                                    show-file-list>
                                  </uploadFile>
                                </div>

                                <div class="file-box" v-else>
                                  <div v-for="(fileItem, index) in cItem.fileList" :key="index">
                                    <el-button type="text" @click="handlePreview(fileItem)">
                                      {{ fileItem.name }}
                                    </el-button>
                                  </div>
                                </div>

                                <div class="card-bottom" v-if="cItem.commitUserName">
                                  <span>{{ cItem.commitUserName }}</span>
                                  <span>{{
                                    formatDate(cItem.commitTime)
                                    }}</span>
                                  <i style="color: #67c23a" class="el-icon-success"></i>
                                </div>
                              </div>

                              <div class="card-right">
                                <el-button
                                  v-if="roleType == 2 && getRole(roleType) == cItem.nodeBy && bItem.status == 0"
                                  type="primary" size="small" plain @click="qcSubmit(cItem)">提交</el-button>

                                <div v-if="roleType == 3 && getRole(roleType) == cItem.nodeBy && bItem.status == 1">
                                  <el-button @click="setNodeAudit(cItem, 1)" type="success" size="small"
                                    icon="el-icon-success" plain>通过</el-button>

                                  <el-button @click="setNodeAudit(cItem, 0)" type="danger" size="small"
                                    icon="el-icon-success" plain>驳回</el-button>
                                </div>

                              </div>

                            </div>
                          </div>
                        </div>
                      </el-timeline-item>
                    </el-timeline>
                  </div>
                  <!-- 最后节点 -->
                  <div v-else>
                    <div class="timeline-item-card">
                      <div class="card-left">
                        <span v-if="detailsData.nodeStatus === 0">
                          该内容的生产进度未完成
                          <el-button type="primary" size="mini" plain
                            @click="handleMaintain(detailsData)">去维护</el-button>
                        </span>
                        <span>QC(R)</span>
                        <p v-if="item.planNodeLast.qcNcr">NCR编码：{{ item.planNodeLast.qcNcr }}</p>

                        <div v-if="item.planNodeLast.qcFileList && item.planNodeLast.qcFileList.length > 0">
                          <p>补充文件：</p>
                          <p class="file-name" v-for="(dItem, dIndex) in item.planNodeLast.qcFileList" :key="dIndex"
                            @click="handlePreview(dItem)">{{ dItem.name }}</p>
                        </div>

                        <div class="card-bottom" v-if="item.planNodeLast.qcStatus != null">
                          <span>{{ item.planNodeLast.qcUserName }}</span>
                          <span>{{
                            formatDate(item.planNodeLast.qcTime)
                            }}</span>
                          <i v-if="item.planNodeLast.qcStatus == 1" style="color: #67c23a" class="el-icon-success"></i>
                          <i v-if="item.planNodeLast.qcStatus == 0" style="color: #f56c6c" class="el-icon-error"></i>
                        </div>
                      </div>
                      <div class="card-right" v-if="roleType == 2 && item.planNodeLast.qcStatus != 1">
                        <el-button @click="submitLastQc(item.planNodeLast, 1)" type="success" size="small"
                          icon="el-icon-success" plain>自检材料完整无误，提交审核</el-button>
                      </div>
                    </div>
                    <div class="timeline-item-card">
                      <div class="card-left">
                        <span>HTC(R)</span>
                        <p v-if="item.planNodeLast.htcNcr">NCR编码：{{ item.planNodeLast.htcNcr }}</p>

                        <div v-if="item.planNodeLast.htcFileList && item.planNodeLast.htcFileList.length > 0">
                          <p>补充文件：</p>
                          <p class="file-name" v-for="(dItem, dIndex) in item.planNodeLast.htcFileList" :key="dIndex"
                            @click="handlePreview(dItem)">{{ dItem.name }}</p>
                        </div>

                        <div class="card-bottom" v-if="item.planNodeLast.htcUserName">
                          <span>{{ item.planNodeLast.htcUserName }}</span>
                          <span>{{
                            formatDate(item.planNodeLast.htcTime)
                            }}</span>
                          <i v-if="item.planNodeLast.htcStatus == 1" style="color: #67c23a" class="el-icon-success"></i>
                          <i v-if="item.planNodeLast.htcStatus == 0" style="color: #f56c6c" class="el-icon-error"></i>
                        </div>
                      </div>

                      <div class="card-right" v-if="
                        roleType == 3 &&
                        item.planNodeLast.htcStatus == 2
                      ">
                        <el-button @click="addQs(item.planNodeLast, 1)" type="success" size="small"
                          icon="el-icon-success" plain>上述R点文件已审阅，H/W点已见证</el-button>
                        <el-button @click="addQs(item.planNodeLast, 0)" type="danger" size="small"
                          icon="el-icon-success" plain>驳回</el-button>
                      </div>
                    </div>

                    <div class="timeline-item-card">
                      <div class="card-left">
                        <span>QS(R)</span>
                        <div class="card-bottom" v-if="item.planNodeLast.qsUserName">
                          <span>{{ item.planNodeLast.qsUserName }}</span>
                          <span>{{
                            formatDate(item.planNodeLast.qsTime)
                            }}</span>
                          <i v-if="item.planNodeLast.qsStatus == 1" style="color: #67c23a" class="el-icon-success"></i>
                          <i v-else style="color: #f56c6c" class="el-icon-error"></i>
                        </div>
                      </div>
                      <div class="card-right" v-if="
                        roleType == 4 &&
                        item.planNodeLast.qsStatus != 1 &&
                        item.planNodeLast.htcStatus == 1
                      ">
                        <el-button @click="submitLastQs(item.planNodeLast, 1)" type="success" size="small"
                          icon="el-icon-success" plain>通过</el-button>
                        <el-button @click="submitLastQs(item.planNodeLast, 0)" type="danger" size="small"
                          icon="el-icon-success" plain>驳回</el-button>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </el-timeline-item>
          </el-timeline>
        </div>

      </div>

      <div slot="footer" class="dialog-footer" v-if="roleType == 1">
        <el-button @click="adminReject(0)" type="danger" icon="el-icon-success" plain>驳回</el-button>
      </div>
    </el-dialog>

    <check-last-htc ref="checkLastHtc" @getDetail="getDetail"></check-last-htc>

    <node-audit ref="nodeAudit"></node-audit>

    <add-stove ref="addStove"></add-stove>

    <basic-info ref="basicInfo" @getDetail="getDetail"></basic-info>

    <check-last-qc ref="checkLastQc" @getDetail="getDetail"></check-last-qc>

    <htc-reject ref="htcReject" @getDetail="getDetail"></htc-reject>

    <admin-reject ref="adminReject" @getDetail="getDetail"></admin-reject>
  </div>
</template>
<script>
import nodeAudit from "./nodeAudit.vue";
import uploadFile from "./uploadFile.vue";
import checkLastHtc from "./checkLastHtc.vue";
import addStove from "./addStove.vue";
import basicInfo from './basicInfo'
import checkLastQc from './checkLastQc'
import adminReject from './adminReject'
import htcReject from "./htcReject.vue";
import { getToken } from "@/utils/auth";
import { formatDate } from "@/utils/index";
import {
  getBmPlanDetails,
  submitLastHtc,
  submitLastQs,
  submitNodeQc,
} from "@/api/bigTestExperiment/remoteSupervision.js";
export default {
  components: {
    uploadFile,
    nodeAudit,
    checkLastHtc,
    addStove,
    basicInfo,
    checkLastQc,
    htcReject,
    adminReject
  },
  data() {
    return {
      formatDate,
      uploadUrl: process.env.VUE_APP_BASE_API + "/user-server/file/upload",
      uploadUrlFile: process.env.VUE_APP_BASE_API + "/user-server/file/uploadFile",
      headers: { "X-Token": "Bearer " + getToken() },
      title: "",
      formLabelWidth: "120px",
      btnLoading: false,
      dialogVisible: false,
      rowId: "",
      form: {
        nodeList: [],
      },

      detailsData: {},

      // 1管理员,2供应商(QC),3(部门)HTC,4部门(QS)
      roleType: null,
      statusOptions: [
        {
          label: '待完成',
          value: 0,
          className: 'spareColor'
        },
        {
          label: '待审核',
          value: 1,
          className: 'repairColor'
        },
        {
          label: '已完成',
          value: 2,
          className: 'functionColor'
        }
      ],
    };
  },

  filters: {
    // 获取状态
    getStatus(value, statusOptions, property) {
      let option = statusOptions.find((item) => {
        return item.value === value
      })
      return option[property]
    },
  },

  methods: {
    //跳转到铸造件管理页面
    handleMaintain(row) {
      this.$router.push({
        path: '/casting/castingForging/contract/index',
        query: {
          row
        }
      })
    },
    checkImageList(imgList) {
      imgList = imgList || []
      let resultImgList = imgList.map((item) => {
        return `${process.env.VUE_APP_IMAGE_API}/${item}`
      })
      return resultImgList
    },

    // 基本信息填写
    handleBasicInfo() {
      this.$refs.basicInfo.init(this.detailsData)
    },

    // 查看文件
    handlePreview(itemFile) {
      // window.open(process.env.VUE_APP_FILE_API + fileName, "_blank");
      let url = process.env.VUE_APP_FILE_API + itemFile.newName
      const x = new XMLHttpRequest()
      x.open('GET', url, true)
      x.responseType = 'blob'
      x.onload = function () {
        const url = window.URL.createObjectURL(x.response)
        const a = document.createElement('a')
        a.href = url
        a.download = itemFile.name
        a.click()
      }
      x.send()
    },

    //初始化
    init(row) {
      this.dialogVisible = true;
      this.rowId = row.id;
      this.roleType = row.roleType;  // 1管理员,2供应商(QC),3(部门)HTC,4部门(QS)
      this.getDetail();
    },

    getDetail() {
      this.btnLoading = true;
      getBmPlanDetails(this.rowId).then((res) => {
        this.detailsData = JSON.parse(JSON.stringify(res.data));
        this.btnLoading = false;
      }).catch(() => {
        this.btnLoading = false;
      });
    },

    getProceduralType(val) {
      switch (val) {
        case 1:
          return "外购（原材料）";
        case 2:
          return "外购（机械）";
        case 3:
          return "外购（焊接）";
        case 4:
          return "一体化";
        case 5:
          return "工序外协";
        case 6:
          return "成套采购";
        case 7:
          return "外协（包工包料）";
        case 8:
          return "外协（带料加工）";
        case 9:
          return "一体化（原材料）";
        case 10:
          return "一体化（精加工）";
        case 11:
          return "一体化（装配）";
      }
    },

    // 1管理员,2供应商(QC),3(部门)HTC,4部门(QS)
    getRole(val) {
      switch (val) {
        case 1:
          return "admin";
        case 2:
          return "qc";
        case 3:
          return "htc";
        case 4:
          return "qs";
        default:
          break;
      }
    },

    showAddBtn(val) {
      if (this.roleType == 2 || this.roleType == 1) {
        if (val.htcStatus != 1) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },

    showTip(val) {
      if (this.roleType == 2) {
        if (
          val.htcId == null ||
          val.heatNo == null ||
          val.processType == null
        ) {
          return true;
        } else {
          return false;
        }
      } else if (this.roleType == 1) {
        if (
          val.htcId == null ||
          val.heatNo == null ||
          val.processType == null
        ) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },

    showSubimtBtn(item, parent) {
      if (this.roleType == 2) {
        if (this.getRole(this.roleType) == item.nodeBy && parent.status == 0) {
          return true;
        } else {
          return false;
        }
      }
    },

    // qc提交
    qcSubmit(val) {
      this.btnLoading = true;
      submitNodeQc(val)
        .then((res) => {
          this.btnLoading = false;
          this.$modal.msgSuccess("操作成功");
          this.$nextTick(() => {
            this.getDetail();
          });
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },

    // htc审核节点
    setNodeAudit(val, rejectState) {
      this.$refs.nodeAudit.init(val, rejectState);
    },

    // 最后节点qc提交
    submitLastQc(val, qcStatus) {
      this.$refs.checkLastQc.init(val, qcStatus)
    },

    // 最后节点htc提交
    submitLastHtc(val, htcStatus) {
      submitLastHtc({
        id: val.id,
        nodeId: val.planNodeId,
        planId: val.planId,
        htcStatus,
      })
        .then((res) => {
          this.$modal.msgSuccess("操作成功");
          this.btnLoading = false;
          this.$nextTick(() => {
            this.getDetail();
          });
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },

    addQs(val, htcStatus) {
      if (htcStatus == 1) {
        this.$refs.checkLastHtc.init(val, htcStatus);
      } else {
        // this.submitLastHtc(val, htcStatus);
        this.$refs.htcReject.init(val)
      }
    },

    // 最后节点qs提交
    submitLastQs(val, qsStatus) {
      submitLastQs({
        id: val.id,
        nodeId: val.planNodeId,
        planId: val.planId,
        qsStatus,
      })
        .then((res) => {
          this.$modal.msgSuccess("操作成功");
          this.btnLoading = false;
          this.$nextTick(() => {
            this.getDetail();
          });
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },

    // 管理员驳回
    adminReject() {
      let length = this.detailsData.planNodeList.length;
      if (length > 0) {
        let data = this.detailsData.planNodeList[length - 1].planNodeLast;
        this.$refs.adminReject.init(data)
      }
    },

    closeDialog() {
      this.$parent.getList();
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep .el-dialog__body {
  padding-bottom: 10px;
}

.repairColor {
  color: #ff9948 !important;
}

.spareColor {
  color: #3db3ff !important;
}

.functionColor {
  color: #0bb143 !important;
}

.tip {
  color: #f56c6c;
}

.upload-box {
  margin: 20px 0;
}

.btn-box {
  margin-bottom: 8px;
  padding-left: 36px;
  display: flex;
  align-items: center;

  .btn-label {
    width: 85px;
  }
}

.dialog-content {
  height: 70vh;
  display: flex;
  justify-content: space-between;

  .content {
    width: 30%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .check-record-box {
      flex: 1;
      overflow-y: scroll;
      padding-top: 14px;
      padding-left: 8px;
    }

    .content-info {
      flex-shrink: 0;

      .grid-content {
        margin-bottom: 14px;
      }
    }

    .audit-log-title {
      flex-shrink: 0;
      font-size: 18px;
      // margin-bottom: 14px;
      color: #1890ff;
    }

    .audit-log-item {
      .log-time {
        font-size: 15px;
      }

      .log-text {
        color: #606266;
        font-size: 12px;
      }
    }
  }

  .block {
    flex: 1;
    padding: 8px;
    overflow-y: scroll;

    // .timeline-item-top {
    //   display: flex;
    //   justify-content: space-between;
    //   background-color: #e3f0ff;
    //   padding: 4px 12px;
    //   border-radius: 4px;
    // }
    .top-btn-box {
      margin-bottom: 12px;
    }

    ::v-deep .el-collapse-item__header {
      height: auto;
      line-height: normal;
      background-color: #e3f0ff;
      border-radius: 4px;
      padding: 4px 12px;

      .top-left {
        flex: 1;

        .code {
          font-size: 14px;
          color: #1890ff;
          margin-left: 5px;
        }

        .file-flag {
          color: #C03639 !important
        }

        .top-content {
          margin-top: 8px;
        }
      }

      .top-right {
        font-size: 12px;
        color: #666;
        text-align: right;
      }

      .top-title {
        font-size: 16px;
        color: #1890ff;
        font-weight: 700;
      }
    }

    ::v-deep .el-collapse-item__content {
      padding-left: 4px;
    }

    // .card-item {
    //   display: flex;
    //   align-items: flex-end;
    //   justify-content: space-between;

    // }

    .item-header {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #f3f6fd;
      padding: 12px;
      border-radius: 4px;

      .item-header-right {
        font-size: 12px;
      }
    }

    .timeline-item-card {
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      margin-top: 8px;
      background-color: #f6f7f9;
      padding: 4px 12px;
      border-radius: 4px;

      .explain-box {
        display: flex;
        align-items: center;

        .explain-label {
          color: #909399;
          width: 100px;
          font-size: 14px;
        }
      }

      .card-left {
        display: flex;
        flex: 1;
        flex-direction: column;

        .card-address {
          font-size: 12px;
          color: #85929b;
        }

        .img-box {
          width: 100%;
          display: flex;
        }

        .file-name {
          padding-left: 10px;
          font-size: 12px;
          color: #409EFF;
          cursor: pointer;
        }
      }

      .card-right {
        margin-top: 10px;
        text-align: end;
      }

      .card-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid #dcdfe6;
        padding-top: 12px;
        margin-top: 8px;

        span {
          flex: 1;
        }

        i {
          flex: 1;
        }
      }
    }
  }
}
</style>
