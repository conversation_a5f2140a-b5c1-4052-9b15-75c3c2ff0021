<template>
  <div>
    <el-dialog :title="title" top="30px" :visible.sync="dialogVisible" append-to-body width="840px">
      <el-form :model="dialogQueryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="搜索" prop="keyword">
          <el-input v-model="dialogQueryParams.keyword" placeholder="请输入资料名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="search">搜索</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="dialogLoading" :data="dialogTableList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column label="资料名称" prop="name" />
        <el-table-column label="是否必填" prop="requiredStatus">
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.requiredStatus" @change="handleRequiredChange(scope.$index, scope.row)">
              <el-radio :label="1">必填</el-radio>
              <el-radio :label="0">非必填</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="资料说明" prop="remarks" />
      </el-table>

      <pagination v-show="dialogTotal > 0" :total="dialogTotal" :page.sync="dialogQueryParams.pageNum"
        :limit.sync="dialogQueryParams.pageSize" @pagination="getDataList" />

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getDataList, addTechnical } from '@/api/dimensionData/technicalIndicators'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      title: "新增资料",
      formLabelWidth: "120px",
      dialogLoading: false,
      btnLoading: false,
      dialogQueryParams: {
        keyword: "",
        pageNum: 1,
        pageSize: 10,
        // year: new Date().getFullYear().toString(),
        // deptNo: undefined
      },
      dialogTotal: 0,
      dialogTableList: [],
      selectedList: [],
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val;
        if (val) {
          this.getDataList();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 重置
    reset() {
      this.dialogQueryParams = {
        keyword: "",
        pageNum: 1,
        pageSize: 10,
        year: new Date().getFullYear().toString(),
        deptNo: undefined
      };
      this.getDataList();
    },
    // 搜索
    search() {
      this.dialogQueryParams.pageNum = 1;
      this.getDataList();
    },
    // 初始化
    init() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.selectedList = [];
        this.getDataList();
      });
    },
    // 获取数据列表
    getDataList() {
      this.dialogLoading = true;
      getDataList(this.dialogQueryParams).then((res) => {
        this.dialogLoading = false;
        this.dialogTableList = res.data.records || [];
        this.dialogTotal = res.data.total || 0;
      }).catch(error => {
        this.dialogLoading = false;
        console.error("获取列表失败", error);
      });
    },
    // 处理必填状态变化
    handleRequiredChange(index, row) {
      console.log('必填状态变化:', index, row.requiredStatus);
      // 强制更新表格数据，确保视图更新
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    // 表格多选
    handleSelectionChange(val) {
      this.selectedList = val.map((item) => {
        return {
          ...item,
          requiredStatus: item.requiredStatus || 0
        };
      });
    },
    // 确定
    confirm() {
      if (this.selectedList.length === 0) {
        this.$message.warning("请至少选择一条数据");
        return;
      }
      const params = {
        id: this.rowId,
        connectList: this.selectedList
      };

      this.btnLoading = true;
      addTechnical(params)
        .then((response) => {
          this.$message.success("添加成功");
          this.handleClose();
          this.$emit('refresh');
        })
        .catch((error) => {
          console.error("添加失败", error);
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false);
    }
  },
};
</script>
<style scoped lang="scss">
.btn-box {
  margin-bottom: 8px;
  padding-left: 36px;
  display: flex;
  align-items: center;

  .btn-label {
    width: 85px;
  }
}

// 修复单选框样式
:deep(.el-radio) {
  margin-right: 15px;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  border-color: #409EFF;
  background: #409EFF;
}

:deep(.el-radio__input.is-checked+.el-radio__label) {
  color: #409EFF;
}
</style>
