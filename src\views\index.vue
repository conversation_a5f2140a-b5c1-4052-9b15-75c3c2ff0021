<template>
  <div>
    <!-- <div class="box" v-if="userType == 1"> -->
    <div class="box" v-if="userType == 1 && !pathArr.includes(routerPath)">
      <el-card class="box-card">
        <div class="box-bigbox">
          <div class="header">
            <div class="header-left" style="margin-left: 30px">
              <img src="../assets/images/超额采购.png" alt="" />
            </div>
            <div class="header-right">
              <span class="header-right-heder">机组总数 </span>
              <span class="header-right-size"> {{ homeList.totalNum }} </span>
            </div>
          </div>

          <div class="header" style="background: rgba(252, 112, 112, 0.1)">
            <div class="header-left" style="color: rgb(252, 112, 112)">A</div>
            <div class="header-right">
              <span class="header-right-heder">A类机组总数</span>
              <span class="header-right-size"> {{ homeList.typeANum }} </span>
            </div>
          </div>

          <div class="header" style="background: rgba(27, 191, 217, 0.1)">
            <div class="header-left" style="color: rgb(27, 191, 217)">B</div>
            <div class="header-right">
              <span class="header-right-heder">提前启动机组总数</span>
              <span class="header-right-size"> {{ homeList.typeBNum }} </span>
            </div>
          </div>

          <div class="header" style="background: rgba(249, 154, 72, 0.1)">
            <div class="header-left" style="color: rgb(249, 154, 72)">C</div>
            <div class="header-right">
              <span class="header-right-heder">在制机组总数</span>
              <span class="header-right-size"> {{ homeList.typeCNum }} </span>
            </div>
          </div>
          <div class="header" style="background: rgba(122, 200, 100, 0.1)">
            <div class="header-left" style="color: rgb(122, 200, 100)">D</div>
            <div class="header-right">
              <span class="header-right-heder">暂停缓机组总数</span>
              <span class="header-right-size"> {{ homeList.typeDNum }} </span>
            </div>
          </div>
        </div>
      </el-card>

      <el-card
        class="box-twoCard"
        v-for="(item, index) in homeList.homeStatisticsList"
        :key="index"
      >
        <div class="box-twoCard-div">
          <div class="box-twoCard-firstdiv">
            <span class="box-twoCard-span"> {{ item.text }} </span>
            <span class="box-twoCard-span box-twoCard-span3">
              {{ item.count }}
            </span>
          </div>

          <div class="box-twoCard-firstdiv">
            <span class="box-twoCard-span1"> 生产计划大纲 </span>
            <span class="box-twoCard-span2"> {{ item.productionPlan }} </span>
          </div>

          <div class="box-twoCard-firstdiv">
            <span class="box-twoCard-span1"> 项目执行计划 </span>
            <span class="box-twoCard-span2">
              {{ item.implementationPlan }}
            </span>
          </div>

          <div class="box-twoCard-firstdiv">
            <span class="box-twoCard-span1"> 技术准备计划 </span>
            <span class="box-twoCard-span2"> {{ item.technicalPlan }} </span>
          </div>

          <div class="box-twoCard-firstdiv">
            <span class="box-twoCard-span1"> 预提计划 </span>
            <span class="box-twoCard-span2"> {{ item.withholdingPlan }} </span>
          </div>

          <div class="box-twoCard-firstdiv">
            <span class="box-twoCard-span1"> 铸锻件成套计划 </span>
            <span class="box-twoCard-span2"> {{ item.castingPlan }} </span>
          </div>
        </div>
      </el-card>
    </div>
    <div class="main" v-else-if="userType == 2 || pathArr.includes(routerPath)">
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="铸锻件协同" name="first" disabled
          >铸锻件协同</el-tab-pane
        >
        <el-tab-pane label="质量协同" name="second" disabled
          >质量协同</el-tab-pane
        >
        <el-tab-pane label="生产外协" name="third">
          <div style="height: 80vh; overflow: auto" v-loading="loading">
            <iframe
              src="http://192.168.1.58:8182/#/bigscreen/preview?code=bigScreen_r1MbLubbOr"
              frameborder="0"
              width="100%"
              height="100%"
              scrolling="auto"
            ></iframe>
            <!-- 统计图标 -->
            <div class="statistics" v-if="false">
              <div class="left">
                <div class="content">
                  <div class="title">完成率统计TOP10</div>
                  <div
                    class="per"
                    v-for="(item, index) in finishTop"
                    :key="index"
                  >
                    <div style="display: flex; align-items: center">
                      <div style="width: 30px; text-align: center">
                        <img
                          v-if="item.ranking === 1"
                          src="@/assets/images/金牌.png"
                        />
                        <img
                          v-if="item.ranking === 2"
                          src="@/assets/images/银牌.png"
                        />
                        <img
                          v-if="item.ranking === 3"
                          src="@/assets/images/铜牌.png"
                        />
                        <span v-if="item.ranking > 3">{{ item.ranking }}</span>
                      </div>
                      <span>{{ item.supplierName }}</span>
                    </div>
                    <el-progress
                      style="width: 50%"
                      :percentage="item.rate"
                      stroke-width="10"
                    ></el-progress>
                  </div>
                </div>
                <div class="content" style="margin-top: 10px">
                  <div class="title">完成率统计BOT10</div>
                  <div
                    class="per"
                    v-for="(item, index) in finishBot"
                    :key="index"
                  >
                    <div style="display: flex; align-items: center">
                      <div style="width: 30px; text-align: center">
                        <span>{{ item.ranking }}</span>
                      </div>
                      <span>{{ item.supplierName }}</span>
                    </div>
                    <el-progress
                      style="width: 50%"
                      :percentage="item.rate"
                      stroke-width="10"
                    ></el-progress>
                  </div>
                </div>
              </div>
              <div class="right">
                <div class="content">
                  <div class="title">报警率统计TOP10</div>
                  <div
                    class="per"
                    v-for="(item, index) in alarmTop"
                    :key="index"
                  >
                    <div style="display: flex; align-items: center">
                      <div style="width: 30px; text-align: center">
                        <span>{{ item.ranking }}</span>
                      </div>
                      <span>{{ item.supplierName }}</span>
                    </div>
                    <el-progress
                      style="width: 50%"
                      :percentage="item.rate"
                      stroke-width="10"
                      color="red"
                    ></el-progress>
                  </div>
                </div>
                <div class="content" style="margin-top: 10px">
                  <div class="title">报警率统计BOT10</div>
                  <div
                    class="per"
                    v-for="(item, index) in alarmBot"
                    :key="index"
                  >
                    <div style="display: flex; align-items: center">
                      <div style="width: 30px; text-align: center">
                        <span>{{ item.ranking }}</span>
                      </div>
                      <span>{{ item.supplierName }}</span>
                    </div>
                    <el-progress
                      style="width: 50%"
                      :percentage="item.rate"
                      stroke-width="10"
                      color="red"
                    ></el-progress>
                  </div>
                </div>
              </div>
            </div>

            <!-- 供应商情况 -->
            <div class="supplier">
              <div class="subTitle">供应商情况</div>
              <el-table :data="tableData" border height="500px" class="tab">
                <el-table-column
                  label="序号"
                  type="index"
                  width="60"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  label="厂商名称"
                  prop="supplierName"
                  align="center"
                  width="200"
                  show-overflow-tooltip
                />
                <el-table-column
                  label="厂商编号"
                  prop="supplierCode"
                  align="center"
                />
                <el-table-column
                  label="生产调度数量"
                  prop="castingNum"
                  align="center"
                />
                <el-table-column
                  label="报警数量"
                  prop="alarmNum"
                  align="center"
                />
                <el-table-column
                  label="进行中数量"
                  prop="normalNum"
                  align="center"
                />
                <el-table-column
                  label="完成数量"
                  prop="finishNum"
                  align="center"
                />
                <el-table-column
                  label="完成率"
                  prop="finishRate"
                  align="center"
                >
                  <template slot-scope="scope">
                    {{ scope.row.finishRate }}%
                  </template>
                </el-table-column>
                <el-table-column
                  label="按期完成率"
                  prop="onTimeFinishRate"
                  align="center"
                >
                  <template slot-scope="scope">
                    {{ scope.row.onTimeFinishRate }}%
                  </template>
                </el-table-column>
                <el-table-column
                  label="按期完成数量"
                  prop="onTimeFinishNum"
                  align="center"
                />
              </el-table>
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.pageNum"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="query.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                style="margin-top: 20px; text-align: right"
              />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { homePage, homePageSup } from "../api/login";
import { getSupplierCompletionList } from "@/api/supplier/productionScheduling";
export default {
  data() {
    return {
      pathArr: ["/chart/vendorStatistics"],
      userType: null,
      homeList: {},
      activeName: "third",
      query: {
        pageSize: 10,
        pageNum: 1,
      },
      routerPath: this.$router.currentRoute.path,
      tableData: [],
      total: 0,
      finishTop: [],
      finishBot: [],
      alarmTop: [],
      alarmBot: [],
      loading: false,
    };
  },
  mounted() {
    this.homePage();
    this.userType =
      this.$store.state.user.userType ||
      JSON.parse(localStorage.getItem("userInfo")).userType;
    this.loading = true;
    Promise.all([this.getStatistics(), this.getTableData()]).finally(() => {
      this.loading = false;
    });
  },
  methods: {
    homePage() {
      homePage().then((res) => {
        this.homeList = res.data;
      });
    },
    // 切换
    handleClick() {},
    getTableData() {
      const params = {
        ...this.query,
      };
      getSupplierCompletionList(params).then((res) => {
        this.tableData = res.data.records;
        this.total = res.data.total;
        console.log(this.tableData, "拿到了");
      });
    },
    handleSizeChange(val) {
      this.query.pageSize = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.query.pageNum = val;
      this.getTableData();
    },
    async getStatistics() {
      const { data } = await homePageSup();
      this.finishTop = data.finishTop;
      this.finishBot = data.finishBot;
      this.alarmTop = data.alarmTop;
      this.alarmBot = data.alarmBot;
    },
  },
};
</script>

<style lang="scss" scoped>
.welcome-box {
  font-size: 24px;
  margin-top: 24px;
}

.box {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.box-bigbox {
  display: flex;
  justify-content: space-around;
}

.box-card {
  display: flex;
  align-items: center;
  width: 98%;
  margin: 20px 0;
}

.box-twoCard {
  display: flex;
  align-items: center;
  width: 98%;
  margin-bottom: 20px;
}

.header {
  width: 200px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ebf5ff;
  border-radius: 3px;
  color: #333333;
}

.header-left img {
  width: 54px;
  height: 56px;
}

.header-left {
  font-weight: bold;
  font-size: 40px;
}

.header-right {
  display: flex;
  flex-direction: column;
  margin-left: 24px;
}

.header-right-heder {
  width: 120px;
  height: 20px;
  font-size: 14px;
}

.header-right-size {
  width: 31px;
  height: 37px;
  font-size: 32px;
  font-weight: bold;
  color: #333333;
  line-height: 37px;
}

.box-twoCard-div {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

// .box-twoCard-firstdiv {
//   // display: flex;
// }
.box-twoCard-firstdiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.box-twoCard-span {
  width: 126px;
  height: 25px;
  font-size: 18px;
  font-weight: 500;
  color: #389aff;
  line-height: 25px;
  text-align: center;
}

.box-twoCard-span1,
.box-twoCard-span2 {
  color: #666;
}

.box-twoCard-span2 {
  font-weight: bold;
  margin-top: 18px;
}

.box-twoCard-span3 {
  margin-top: 14px;
}

// 厂商
.main {
  color: #666666;
  background-color: #f8f8f9;
  width: 100%;
}

.supplier {
  padding: 0 20px;
  background-color: #fff;
  margin-top: 10px;

  .subTitle {
    height: 50px;
    line-height: 50px;
    font-size: 18px;
    color: #333333;
  }
}

.statistics {
  display: flex;
  justify-content: space-between;
  font-size: 14px;

  // background-color: rgb(223, 217, 217);
  // padding: 10px 0;
  .left {
    width: calc(50% - 5px);
    padding: 20px;
    background-color: #fff;
  }

  .right {
    width: calc(50% - 5px);
    padding: 20px;
    background-color: #fff;
  }
}

.content {
  // width: 50%;
  .per {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    img {
      width: 25px;
      height: 25px;
    }
  }

  .title {
    text-align: center;
    margin-bottom: 5px;
    font-size: 18px;
    color: #333333;
  }
}

::v-deep .el-tabs__item.is-active {
  background-color: #fff;
}
</style>
