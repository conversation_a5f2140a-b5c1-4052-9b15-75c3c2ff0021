<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="公告标题" prop="keyword">
                <el-input v-model="queryParams.keyword" placeholder="请输入公告标题" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                    @click="handleDeletes">删除</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="noticeList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="公告标题" prop="title" :show-overflow-tooltip="true" />
            <!-- <el-table-column label="公告内容" prop="description" :show-overflow-tooltip="true" /> -->
            <el-table-column label="创建人" prop="createUser" :show-overflow-tooltip="true" />
            <el-table-column label="公告过期时间" prop="time" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.time, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改
                    </el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.size"
            @pagination="getList" />

        <!-- 添加或修改公告对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-row>
                    <el-col :span="14">
                        <el-form-item label="公告标题">
                            <el-input v-model="form.title" placeholder="请输入公告标题" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="14">
                        <el-form-item label="消息通知">
                            <el-input v-model="form.notice" placeholder="请输入消息通知" />
                        </el-form-item>
                    </el-col>
                   
                    <el-col :span="14">
                        <el-form-item label="到期时间">
                            <el-date-picker v-model="form.time" type="date" value-format="yyyy-MM-dd"
                                placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="内容">
                            <editor v-model="form.description" :min-height="192" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" class="file_col">
                        <el-form-item prop="sbtp" label="上传文件" class="itemIMg">
                            <el-upload class="upload-demo" action="#" :on-remove="handleRemove" :on-error="handleError"
                                :limit="1" :before-upload="beforeUpload" :http-request="onExportFile"
                                v-loading.fullscreen.lock="loadings"
                                element-loading-text="拼命上传中"
                                element-loading-background="rgba(0,0,0,0.1)"
                                :file-list="fileFileList">
                                <el-button icon="el-icon-plus" size="small" type="primary">点击上传</el-button>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
  
<script>
import { getfindAllPage, addAnnouncement, deleteAnnouncementManagement, deletesAnnouncementManagement, deteilfindOne, alterUpdate, uploadFile } from "../../../api/mating/notification";
export default {
    name: "Notice",
    // dicts: ['sys_notice_status', 'sys_notice_type'],
    data() {
        return {
            // 上传遮罩
            loadings: false,
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 公告表格数据
            noticeList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                page: 1,
                size: 10,
                keyword: "",
            },
            fileFileList: [],
            filesArr: [],
            // 表单参数
            form: {
                notice:"",
                announcementId: "",
                createTime: "",
                createUser: "",
                createUserName: "",
                description: "",
                fileId: "",
                name: "",
                htcFile: "",
                time: "",
                title: "",
                updateTime: "",
                updateUserName: "",
            },
            // 表单校验
            rules: {
                title: [
                    { required: true, message: "公告标题不能为空", trigger: "blur" }
                ],
                notice: [
                    { required: true, message: "消息通知不能为空", trigger: "blur" }
                ],
              
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        //文件上传
        onExportFile(file) {
            this.loadings = true
            let formDataFiles = new FormData();
            formDataFiles.append("file", file.file); //文件流
            uploadFile(formDataFiles).then(res => {     
                if (res.code == 1) {
                    this.loadings = false
                    this.$modal.msgSuccess("上传成功");
                    this.form.fileId = res.data.id
                    this.form.name = res.data.name
                }
                else {
                    this.loadings = false
                    this.$modal.msgError("上传失败")
                }


            })
            this.isDisabled = false
            let fileObj = file.file;
            this.filesArr.push(fileObj);

        },
        beforeUpload() {
            this.isDisabled = true
        },
        // 上传失败
        handleError() {
            this.isDisabled = false
        },
        //移除文件
        handleRemove(file) {
            if (!file.raw) {
                for (var i = 0; i < this.dataForm.dataList.length; i++) {
                    if (this.dataForm.dataList[i] == file.obsName) {
                        this.dataForm.dataList.splice(i, 1);
                    }
                }
            } else {
                for (var i = 0; i < this.filesArr.length; i++) {
                    if (this.filesArr[i] == file.raw) {
                        this.filesArr.splice(i, 1);
                    }
                }
            }
        },
        /** 查询公告列表 */
        getList() {
            this.loading = true;
            getfindAllPage(this.queryParams).then(response => {
                this.noticeList = response.data;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();

        },
        // 表单重置
        reset() {
            this.form = {
                title: undefined,
            };
            this.resetForm("form");

        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.page = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.announcementId)
            this.single = selection.length != 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加公告";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            this.open = true;
            this.title = "修改公告";
            deteilfindOne(row.announcementId).then(res => {
                this.form = res.data
            })

        },
        /** 提交按钮 */
        submitForm: function () {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.announcementId != undefined) {
                        alterUpdate(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addAnnouncement(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.announcementId
            this.$modal.confirm('是否确认删除公告编号为"' + ids + '"的数据项？').then(function () {
                return deleteAnnouncementManagement(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        },

        handleDeletes() {
            const announcementIds = this.ids
            this.$modal.confirm('是否确认删除公告编号为"' + announcementIds + '"的数据项？').then(function () {
                return deletesAnnouncementManagement(announcementIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        }

    }
};
</script>
  