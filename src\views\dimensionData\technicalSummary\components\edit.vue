<template>
  <div>
    <el-dialog title="行业影响力评价" :visible.sync="dialogVisible" width="900px" top="30px" :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-select v-if="mode === 'add'" class="dateListSelectStyle" v-model="dateListSelectValue" placeholder="请选择年份"
          @change="dateListSelectChange">
          <el-option v-for="(item, index) in dateList" :key="index" :label="item" :value="item"></el-option>
        </el-select>
        <!-- 左右两列布局 -->
        <el-row :span="12">
          <el-col :span="12">
            <el-form-item label="状态" label-width="100px">
              <el-select v-model="form.auditStatus" disabled>
                <el-option label="审核中" :value="0"></el-option>
                <el-option label="审核通过" :value="1"></el-option>
                <el-option label="被驳回" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原因" label-width="100px">
              <el-input v-model="form.auditRemark" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">

          <!-- 左侧列 -->
          <el-col :span="12">
            <el-form-item label="供方编码" label-width="100px">
              <el-input v-model="form.supplierNo" disabled></el-input>
            </el-form-item>
            <el-form-item label="供方编号" label-width="100px">
              <el-input v-model="form.supplierNo100" disabled></el-input>
            </el-form-item>
            <el-form-item label="供方名称" label-width="100px">
              <el-input v-model="form.supplierName" disabled></el-input>
            </el-form-item>
          </el-col>

          <!-- 右侧列 -->
          <el-col :span="12">
            <el-form-item label="上年度研发投入" prop="researchCost" required>
              <el-input v-model="form.researchCost" placeholder="请输入" class="number-input" :disabled="mode === 'view'">
                <template slot="append">万元</template>
              </el-input>
            </el-form-item>
            <el-form-item label="上年度营业收入" prop="revenueCost" required>
              <el-input v-model="form.revenueCost" placeholder="请输入" class="number-input" :disabled="mode === 'view'">
                <template slot="append">万元</template>
              </el-input>
            </el-form-item>
            <el-form-item label="上年度研发投入占比" class="ratio-item">
              <div class="ratio-content">
                <span>{{ researchProportion }}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 证明文件上传 -->
        <div class="upload-section" v-if="form.deptFileList && form.deptFileList.length > 0">
          <div class="section-title">证明文件</div>
          <el-row :gutter="20">
            <el-col :span="8" v-for="(item, index) in form.deptFileList" :key="index">
              <div class="upload-box">
                <upload :mode="mode" :index="index" :file="mode === 'add' ? [] : [item.htcFile]"
                  @FileSuccess="FileSuccess">
                </upload>
                <div class="upload-label" v-if="index === 0">
                  <div>营业执照</div>
                  <div>(营业执照证明照片)</div>
                </div>
                <div class="upload-label" v-if="index === 1">上年度企业年报中研发投入页截图</div>
                <div class="upload-label" v-if="index === 2">上年度企业年报中营业收入页截图</div>
                <!-- <div class="upload-label">{{ item.dataName }}</div> -->
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 其他证明材料 -->
        <el-form-item label="其他证明材料">
          <el-upload :headers="headers" :action="uploadUrl" :auto-upload="true" :file-list="form.otherFileList"
            ref="otherFileList" multiple :limit="99" :on-success="uploadOtherFileListSuccess"
            :disabled="mode === 'view'">
            <el-button type="primary">添加资料</el-button>
            <div slot="tip" style="color:#1890ff" class="el-upload__tip">注：可提供企业荣誉、优秀项目总结、专利、新闻报道等多方面材料，以此在证公司符合水平。
            </div>
          </el-upload>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button type="link" @click="saveDraft" v-if="mode === 'add'">存为草稿</el-button>
        <el-button type="primary" @click="submitForm" v-if="mode !== 'view'">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import FileUpload from "@/components/modules/fileUpload";
import upload from "./upload.vue";
import { getToken } from "@/utils/auth";
import {
  getTechDataSave,//保存
  getTechDataSupplier,//修改
  getTechDataYear,
  getTechDataFile,
  postTechDataSaveDraft,
  getTechDataOneSupplier,
} from "@/api/dimensionData/rDUpload.js";
export default {
  name: 'IndustryInfluenceDialog',
  components: {
    FileUpload,
    upload,
  },
  data() {
    return {
      dialogVisible: false,
      mode: 'add', // 添加操作模式：add-新增，edit-编辑，view-查看
      // 上传地址
      uploadUrl: process.env.VUE_APP_BASE_API + "/user-server/file/uploadFile", // 替换为实际的上传接口
      // 设置上传请求头
      headers: {
        'X-Token': "Bearer " + getToken()
      },
      form: {
        auditStatus: '',
        supplierNo: '',
        supplierNo100: '',
        supplierName: '',
        researchCost: '',
        revenueCost: '',
        researchProportion: '',
        supplierFileList: [],
        otherFileList: [],
      },
      rules: {
        researchCost: [
          { required: true, message: '请输入上年度研发投入', trigger: 'blur' },
          { type: 'number', message: '必须为数字值', trigger: 'blur', transform: value => Number(value) }
        ],
        revenueCost: [
          { required: true, message: '请输入上年度营业收入', trigger: 'blur' },
          { type: 'number', message: '必须为数字值', trigger: 'blur', transform: value => Number(value) }
        ]
      },
      dateList: [],
      dateListSelectValue: '',
    }
  },
  computed: {
    //占比
    researchProportion() {
      if (!this.form.researchCost || !this.form.revenueCost) return '-'
      return ((this.form.researchCost / this.form.revenueCost) * 100).toFixed(2) + '%'
    }
  },
  methods: {
    show(row, mode) {
      this.dialogVisible = true;
      this.mode = mode; // 添加操作模式
      this.dateList = []
      this.dateListSelectValue = ''
      if (row) {
        //查看或修改获取详情
        getTechDataOneSupplier({ id: row.id }).then(res => {
          this.form = res.data
          this.researchProportion = this.form.researchProportion
        })
      } else {
        //新增或查看草稿
        this.resetForm();
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },
    resetForm() {
      this.form = {
        supplierNo: '',
        supplierNo100: '',
        supplierName: '',
        researchCost: '',
        revenueCost: '',
        researchProportion: '',
        supplierFileList: [],
        otherFileList: [],
      }
      //获取开启年份
      getTechDataYear().then(res => {
        this.dateList = res.data
      })
    },
    //选中开启年份，获取数据或草稿
    dateListSelectChange() {
      getTechDataFile({ year: this.dateListSelectValue }).then(res => {
        this.form = res.data
        this.researchProportion = this.form.researchProportion
        if (this.form.supplierFileList && this.form.supplierFileList.length > 0) {
          this.form.supplierFileList.forEach((item, index) => {
            if (!item.filePath) {
              this.form.supplierFileList[index]['filePath'] = item.htcFile.filePath
            }
          })
        }
        this.$nextTick(() => {
          this.form = JSON.parse(JSON.stringify(this.form))
          this.$refs.form && this.$refs.form.clearValidate();
        });
      })
    },
    //提交
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let otherFileList = []
          //重组其他文件字段
          if (this.form.otherFileList && this.form.otherFileList.length > 0) {
            this.form.otherFileList.forEach((item, index) => {
              if (item.response) {
                otherFileList.push(item.response.data)
              } else {
                otherFileList.push(item)
              }
            });
          }
          let formData = {
            ...this.form
          }
          formData.otherFileList = otherFileList
          //占比字段赋值
          formData.researchProportion = this.researchProportion.split('%')[0]
          const request = (this.mode == 'edit') ? this.updateData(formData) : this.createData(formData);
          request.then(() => {
            this.$message.success(this.mode == 'edit' ? '修改成功' : '添加成功');
            this.dialogVisible = false;
            this.$emit('refresh');
          })
            .catch(() => {
              this.$message.error(this.mode == 'edit' ? '修改失败' : '添加失败');
            });
        }
      });
    },
    createData(formData) {
      return getTechDataSave(formData);
    },
    updateData(formData) {
      return getTechDataSupplier(formData);
    },
    //证明文件子组件调用，对接口所需字段进行赋值
    FileSuccess(file, index) {
      console.log("FileSuccess")
      this.form.deptFileList[index].filePath = file.filePath
      this.form.deptFileList[index].url = file.url
      // this.form.deptFileList[index].id = file.id
      this.form.deptFileList[index].newName = file.newName
      this.form.deptFileList[index].htcFile = file
    },
    //其他证明材料上传
    uploadOtherFileListSuccess(response, file, fileList) {
      this.form.otherFileList = fileList
    },
    //存草稿
    saveDraft() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let otherFileList = []
          if (this.form.otherFileList && this.form.otherFileList.length > 0) {
            this.form.otherFileList.forEach((item, index) => {
              otherFileList.push(item.response.data)
            });
          }
          let formData = {
            ...this.form
          }
          formData.otherFileList = otherFileList
          formData.researchProportion = this.researchProportion.split('%')[0]
          postTechDataSaveDraft(formData).then(res => {
            this.$message.success('保存草稿成功');
            this.dialogVisible = false;
          }).catch(() => {
            this.$message.error('保存草稿失败');
          })
        }
      });
    },
    loadDraft() {
      const draft = localStorage.getItem('rdUploadDraft');
      if (draft) {
        this.form = JSON.parse(draft);
        this.dialogVisible = true;
        this.mode = 'add';
      } else {
        this.$message.info('暂无草稿数据');
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  .el-form-item {
    margin-bottom: 18px;
  }

  :deep(.el-input.is-disabled .el-input__inner) {
    background-color: #f5f7fa;
    color: #606266;
  }

  .number-input {
    width: 180px; // 控制数字输入框的宽度

    :deep(.el-input__inner) {
      text-align: right; // 数字右对齐
    }
  }

  .ratio-item {
    :deep(.el-form-item__content) {
      display: flex;
      align-items: center;
    }
  }

  .ratio-content {
    display: flex;
    align-items: center;
    white-space: nowrap; // 防止换行

    .calculate-btn {
      margin-left: 10px;
      font-size: 12px;
    }
  }
}

:deep(.el-form-item__label) {
  white-space: nowrap; // 防止标签换行
  padding-right: 8px; // 稍微减少右侧padding
}

.upload-section {
  margin: 20px 0;

  .section-title {
    margin-bottom: 15px;
    font-size: 18px;
    color: #489AEE;
  }
}

.upload-box {
  text-align: center;

  .upload-trigger {
    width: 148px;
    height: 148px;
    border: 1px dashed #DCDFE6;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;

    &:hover {
      border-color: #409EFF;
    }

    .el-icon-plus {
      font-size: 28px;
      color: #C0C4CC;
    }

    &.is-disabled {
      cursor: not-allowed;
      background-color: #f5f7fa;
      border-color: #e4e7ed;
      color: #c0c4cc;
    }
  }

  .upload-label {
    margin-top: 8px;
    font-size: 14px;
    color: #606266;
  }
}

.tips {
  font-size: 12px;
  color: #489AEE;
  margin-top: 5px;
}

.dateListSelectStyle {
  margin-bottom: 20px;
  // margin-left: 50px;
}
</style>
