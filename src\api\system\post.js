import request from '@/utils/request'

// 查询岗位列表
export function getUnitList(query) {
  return request({
    url: '/back-server/engineeringUnit/getUnitList',
    method: 'get',
    params: query
  })
}
//导出
export function exportData(query) {
  return request({
    url: '/back-server/engineeringUnit/exportExcelUnit',
    method: 'get',
    params: query,
    responseType: 'blob', // important
  })
}
// 查询岗位详细
export function getPost(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'get'
  })
}

// 新增岗位
export function addPost(data) {
  return request({
    url: '/system/post',
    method: 'post',
    data: data
  })
}

// 修改岗位
export function updatePost(data) {
  return request({
    url: '/system/post',
    method: 'put',
    data: data
  })
}

// 删除岗位
export function delPost(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'delete'
  })
}
