import request from '@/utils/request'
// 新增
export function qtModule(params) {
    return request({
        url: '/mes-server/qt/module',
        method: 'post',
        data: params,
    })
}

// 列表查询
export function modulePage(params) {
    return request({
        url: '/mes-server/qt/module/page',
        method: 'get',
        params: params,
    })
}

// 更改重点关注
export function moduleAttention(query) {
    return request({
        url: `/mes-server/qt/module/${query.id}/attention/${query.attention}`,
        method: 'put'
    })
}

// 删除
export function delModule(id) {
    return request({
        url: `/mes-server/qt/module/${id}`,
        method: 'delete'
    })
}