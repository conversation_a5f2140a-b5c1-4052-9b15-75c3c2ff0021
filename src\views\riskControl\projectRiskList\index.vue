<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="状态" prop="status" label-width="40px">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable>
          <el-option
            v-for="dict in statusList"
            :key="dict.value"
            :label="dict.text"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="风险等级" prop="riskLevel" label-width="80px">
        <el-select
          v-model="queryParams.riskLevel"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="dict in riskLevelList"
            :key="dict.value"
            :label="dict.text"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="搜索" prop="query">
        <el-input
          v-model="queryParams.query"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleImport"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column label="序号" type="index" />
      <el-table-column label="责任方" prop="dutyName" />
      <el-table-column label="责任部门" prop="dutyUserName" />
      <el-table-column label="责任供应商" align="center" width="150">
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            title=""
            width="250"
            trigger="hover"
            :content="dataJoin(scope.row.mesProjectRiskSupplierList,'supplierName')">
            <el-button slot="reference" type="text" v-if="scope.row.mesProjectRiskSupplierList && scope.row.mesProjectRiskSupplierList.length>0">
              <div class="over-text" style="width:150px">{{ dataJoin(scope.row.mesProjectRiskSupplierList,'supplierName') }}</div>
            </el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="涉及产品" prop="productName" />
      <el-table-column label="风险等级" prop="riskLevel">
        <template slot-scope="scope">
          {{ listFind(riskLevelList, scope.row.riskLevel) }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="300"
        label="风险点描述"
        prop="riskDesc"
      >
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            title=""
            width="200"
            trigger="hover"
            :content="scope.row.riskDesc">
            <el-button slot="reference" type="text">
              <div class="over-text">{{ scope.row.riskDesc }}</div>
            </el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createUserName" />
      <el-table-column label="创建时间" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" prop="reviewTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.reviewTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 1">待部门反馈</span>
          <span v-if="scope.row.status == 2">待供应商反馈</span>
          <span v-if="scope.row.status == 3">待审核</span>
          <span v-if="scope.row.status == 4">已完成</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="210"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="showItem(scope.row)"
            >详情</el-button
          >
          <el-button
            v-if="scope.row.sortIndex == 0"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="top(scope.row, 1)"
            >取消置顶</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="top(scope.row, 0)"
            >置顶</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDel(scope.row)"
            >删除</el-button
          >
        </template></el-table-column
      >
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <add-project ref="addProject" @getList="getList"></add-project>

    <feedback ref="feedback" @getList="getList"></feedback>

    <!-- 导入 -->
    <upload-file ref="uploadFile" @getList="getList"></upload-file>
  </div>
</template>

<script>
import {
  getRiskManagePage,
  downloadRisk,
  setSort,
  setDeptStatus,
  setSupplierStatus,
  delRisk,
} from "@/api/riskControl/projectRiskList";
import addProject from "./components/addProject";
import feedback from "./components/feedback";
import uploadFile from "./components/uploadFile";
export default {
  name: "Post",
  components: { addProject, feedback, uploadFile },
  data() {
    return {
      userLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        query: "",
        pageNum: 1,
        pageSize: 10,
        status: undefined, //状态
        riskLevel: undefined, // 风险等级
      },
      riskLevelList: [
        {
          text: "严重",
          value: 0,
        },
        {
          text: "一般",
          value: 1,
        },
        {
          text: "轻微",
          value: 2,
        },
      ],
      statusList: [
        {
          text: "待部门反馈",
          value: 1,
        },
        {
          text: "待供应商反馈",
          value: 2,
        },
        {
          text: "待审核",
          value: 3,
        },
        {
          text: "已完成",
          value: 4,
        },
      ],

      //机组详情
      riskData: {},
      detailLoading: false,
      // 表单参数
      form: {
        checkList: [],
      },
      // 部门人员列表
      dutyUserList: [],

      // 审核弹框
      auditOpen: false,
      auditForm: {
        rejectDesc: "",
        status: null,
      },
      auditData: {},
      auditRole: null,
      auditLoading: false,

      dialogImageUrl: "",
      dialogVisible: false,
      imageList: [],
      fileList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    dataJoin(array,text){//array 循环数组 text 数组中需要拼接的属性
      let newArray = []
      if(array&& array.length >0){
        array.forEach(item => {
          newArray.push(item[text])
        });
        return newArray.join(',')
      }else{
        return ''
      }
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 置顶
    top(row, val) {
      setSort({
        id: row.id,
        val,
      }).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getList();
      });
    },

    listFind(list = [], value) {
      let index = list.findIndex((item) => {
        return item.value == value;
      });
      if (index > -1) {
        return list[index].text;
      }
    },

    // 删除
    handleDel(row) {
      this.$modal
        .confirm("是否确认删除")
        .then(function () {
          return delRisk(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },

    // 导入
    handleImport() {
      this.$refs.uploadFile.init();
    },
    handleExport(){
      // const _this = this;
      // this.$confirm("是否导出数据?", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // }).then(() => {
      //   let params = {
      //     ids: this.ids,
      //     ...this.queryParams,
      //   };
      //   exportData(params).then((res) => {
      //     const url = window.URL.createObjectURL(new Blob([res]));
      //     const link = document.createElement("a");
      //     link.target = "_blank";
      //     link.href = url;
      //     link.setAttribute("download", "部套数据.xlsx");
      //     document.body.appendChild(link);
      //     link.click();
      //   });
      // });
    },

    /** 下载模板操作 */
    importTemplate() {
      downloadRisk().then((response) => {
        const url = window.URL.createObjectURL(new Blob([response]));
        const link = document.createElement("a");
        link.target = "_blank";
        link.href = url;
        link.setAttribute("download", "项目风险控制导入模板.xlsx");
        document.body.appendChild(link);
        link.click();
      });
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      if (response.code === 0) {
        this.$message({
          type: "error",
          message: response.msg,
          duration: 2000,
        });
      } else {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.getList();
      }
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    //创建时间处理
    timeChange() {
      this.queryParam.startTime = this.dateRange[0];
      this.queryParam.endTime = this.dateRange[1];
    },
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getRiskManagePage(this.queryParams)
        .then((response) => {
          this.postList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        dutyName: undefined,
        productName: undefined,
        riskLevel: undefined,
        riskDesc: undefined,
        riskReason: undefined,
        measuresDesc: undefined,
        checkList: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.addProject.init();
    },

    // 项目名称弹窗
    showItem(row) {
      this.$refs.feedback.init(row);
    },

    // 审核
    setAuditOpen(data, status, flag) {
      this.auditOpen = true;
      this.auditForm.status = status;
      this.auditForm.rejectDesc = "";
      this.auditData = data;
      this.auditRole = flag;
    },

    // 审核确定
    auditSubmit() {
      if (this.$refs.auditForm != undefined) {
        this.$refs["auditForm"].validate((valid) => {
          if (valid) {
            this.auditLoading = true;
            if (this.auditRole == "dept") {
              setDeptStatus({
                id: this.riskData.id,
                projectRiskDeptId: this.auditData.id,
                status: this.auditForm.status,
                rejectDesc: this.auditForm.rejectDesc,
              })
                .then((res) => {
                  this.auditLoading = false;
                  this.$modal.msgSuccess("操作成功");
                  this.auditOpen = false;
                  this.detailLoading = true;
                  getProjectRisk(this.riskData.id).then((res) => {
                    this.detailLoading = false;
                    this.riskData = res.data;
                  });
                })
                .catch(() => {
                  this.auditLoading = false;
                });
            } else {
              setSupplierStatus({
                id: this.riskData.id,
                projectRiskSupplierId: this.auditData.id,
                status: this.auditForm.status,
                rejectDesc: this.auditForm.rejectDesc,
              })
                .then((res) => {
                  this.auditLoading = false;
                  this.$modal.msgSuccess("操作成功");
                  this.auditOpen = false;
                  this.detailLoading = true;
                  getProjectRisk(this.riskData.id).then((res) => {
                    this.detailLoading = false;
                    this.riskData = res.data;
                  });
                })
                .catch(() => {
                  this.auditLoading = false;
                });
            }
          }
        });
      } else {
        this.auditLoading = true;
        if (this.auditRole == "dept") {
          setDeptStatus({
            id: this.riskData.id,
            projectRiskDeptId: this.auditData.id,
            status: this.auditForm.status,
            rejectDesc: this.auditForm.rejectDesc,
          })
            .then((res) => {
              this.auditLoading = false;
              this.$modal.msgSuccess("操作成功");
              this.auditOpen = false;
              this.detailLoading = true;
              getProjectRisk(this.riskData.id).then((res) => {
                this.detailLoading = false;
                this.riskData = res.data;
              });
            })
            .catch(() => {
              this.auditLoading = false;
            });
        } else {
          setSupplierStatus({
            id: this.riskData.id,
            projectRiskSupplierId: this.auditData.id,
            status: this.auditForm.status,
            rejectDesc: this.auditForm.rejectDesc,
          })
            .then((res) => {
              this.auditLoading = false;
              this.$modal.msgSuccess("操作成功");
              this.auditOpen = false;
              this.detailLoading = true;
              getProjectRisk(this.riskData.id).then((res) => {
                this.detailLoading = false;
                this.riskData = res.data;
              });
            })
            .catch(() => {
              this.auditLoading = false;
            });
        }
      }
    },

    // 审核取消
    auditCancel() {
      this.auditOpen = false;
    },
  },
};
</script>

<style scoped lang="scss">
.img-box {
  display: flex;
  img {
    cursor: pointer;
    width: 146px;
    height: 146px;
    margin-right: 12px;
    border-radius: 8px;
  }
}
.over-text {
  font-size: 13px;
  width: 290px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.content-label {
  font-weight: 700;
}
::v-deep .el-dialog__body {
  line-height: 36px;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}
.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
::v-deep .el-row--flex {
  margin-left: 22px;
}
// ::v-deep .el-dialog {
//   margin-top: 30vh !important;
// }
.timeline-item-top {
  display: flex;
  justify-content: space-between;
  background-color: #f3f6fd;
  padding: 4px 12px;
  border-radius: 4px;
}
.timeline-item-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  background-color: #f6f7f9;
  padding: 4px 12px;
  border-radius: 4px;
  .card-left {
    display: flex;
    flex-direction: column;
  }
}
.timeline-item-dept {
  display: flex;
  align-items: center;
  margin-top: 8px;

  .card-left {
    width: calc((100% - 10px) / 2);
    background-color: #f6f7f9;
    padding: 4px 12px;
    border-radius: 4px;
    margin-right: 10px;
  }
  .card-left:last-of-type {
    margin-right: 0;
  }
}
</style>
