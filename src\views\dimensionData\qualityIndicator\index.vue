<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="质量" prop="secondType">
        <el-select
          style="width: 100%"
          v-model="queryParams.secondType"
          placeholder="请选择"
        >
          <el-option
            v-for="item in secondCategoryList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="ncr编号" prop="query">
        <el-input
          v-model="queryParams.ncr"
          style="width: 300px"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="搜索" prop="query">
        <el-input
          v-model="queryParams.query"
          style="width: 300px"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div class="mb8 justify-between">
      <el-row :gutter="10" class="item-center">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd()"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" size="mini" @click="exportData()"
            >导出（已选{{ ids.length }}条）</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" size="mini" @click="exportDataAll()"
            >导出全部</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-upload2"
            size="mini"
            @click="handleImp()"
            >导入</el-button
          >
        </el-col>
      </el-row>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </div>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
      max-height="600"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="序号" type="index" width="120">
        <template slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
      </el-table-column>
      <el-table-column label="事件日期" prop="crtTime" width="120" />
      <!-- <el-table-column label="质量指标" prop="type" width="150" /> -->
      <el-table-column label="二级分类" prop="secondType" />
      <el-table-column
        label="分类编号"
        prop="typeNo"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.typeName" placement="top">
            <div>{{ scope.row.typeNo }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column label="招标编号" prop="bidNo" width="120" />
      <el-table-column label="合同编号" prop="contractNo" width="120" /> -->
      <el-table-column label="NCR" prop="ncr" width="120" />
      <el-table-column label="罚分" prop="score" />
      <el-table-column
        label="判罚原因"
        prop="reason"
        show-overflow-tooltip
        width="200"
      />
      <el-table-column label="罚分来源" prop="sourcesData" width="120" />
      <el-table-column label="删除状态" prop="deletedStatus">
        <template slot-scope="scope">
          <el-tag :type="scope.row.deletedStatus === 1 ? 'danger' : 'success'">
            {{ scope.row.deletedStatus === 1 ? "已删除" : "未删除" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="修改历史" prop="updateUserId" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            v-if="scope.row.updateUserId"
            @click="historyHandle(scope.row)"
            >有</el-button
          >
          <div v-else>-</div>
        </template>
      </el-table-column>

      <el-table-column label="QMS编码" prop="supplierNo" width="120" />
      <!-- <el-table-column
        label="集团招采编码"
        prop="supplierNo100"
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <div v-if="!scope.row.supplierNo100">-</div>
          <div v-else>{{ scope.row.supplierNo100 }}</div>
        </template>
      </el-table-column> -->
      <el-table-column label="SAP编码" prop="supplierCode" width="120" />
      <el-table-column
        label="供应商名称"
        prop="responsibleSupplier"
        width="200"
      />
      <el-table-column label="检查部门" prop="deptName" />
      <el-table-column label="检查员" prop="inspector" />
      <el-table-column
        width="180"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleEdit(scope.row, 'view')"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEdit(scope.row, 'edit')"
            v-if="scope.row.deletedStatus === 0"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            v-if="scope.row.deletedStatus === 0"
            @click="handleEdit(scope.row, 'delete')"
            >删除</el-button
          >
        </template></el-table-column
      >
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <add
      ref="addDialog"
      :visible.sync="addDialogVisible"
      @refresh="resetQuery"
    ></add>
    <process-import ref="processImport" @dialog="handleQuery"></process-import>
    <history-dialog :visible.sync="historyDialogVisible" :row="currentRow" />
    <edit-score-dialog
      :visible.sync="editDialogVisible"
      @refresh="resetQuery"
      :row="currentRow"
      :type="currentType"
    />
  </div>
</template>

<script>
import add from "./components/add.vue";
import {
  apiGetPage,
  apiGetSecondCategory,
  exportData,
} from "@/api/dimensionData/qualityIndicator.js";
import ProcessImport from "./components/processImport.vue";
import EditScoreDialog from "./components/edit.vue";
import HistoryDialog from "./components/historyDialog.vue";
export default {
  components: {
    add,
    ProcessImport,
    EditScoreDialog,
    HistoryDialog,
  },
  data() {
    return {
      secondCategoryList: [],
      currentRow: {},
      // 修改历史弹出层
      historyDialogVisible: false,
      // 当前选中行id
      currentId: "",
      // 当前选中行类型
      currentType: "view",
      editDialogVisible: false,
      // 添加弹出层
      addDialogVisible: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ncr: undefined,
        query: undefined, //模块名称
        moduleCode: undefined, //模块编码
        posid: undefined, //令号
        post1: undefined, //机组名称
        powerPl: undefined, //业主名称（电厂）
        startTime: undefined, //开始时间
        endTime: undefined, //结束时间
        status: undefined, //状态
      },
      //机组详情
      posidList: {},
    };
  },
  created() {
    this.getList();
    this.getSecondCategory();
  },
  methods: {
    //导入
    handleImp() {
      this.$refs.processImport.init();
    },
    //导出
    exportData() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请先选择要导出的数据");
        return;
      }
      exportData({ ids: this.ids, ...this.queryParams }).then((res) => {
        const url = window.URL.createObjectURL(new Blob([res]));
        const link = document.createElement("a");
        link.target = "_blank";
        link.href = url;
        link.setAttribute("download", "质量指标.xlsx");
        document.body.appendChild(link);
        link.click();
      });
    },
    exportDataAll() {
      exportData({ ...this.queryParams }).then((res) => {
        const url = window.URL.createObjectURL(new Blob([res]));
        const link = document.createElement("a");
        link.target = "_blank";
        link.href = url;
        link.setAttribute("download", "质量指标.xlsx");
        document.body.appendChild(link);
        link.click();
      });
    },
    //功能开发中弹窗提示
    openBatch() {
      this.$modal.msgError("功能开发中");
    },
    // 获取二级分类
    getSecondCategory() {
      apiGetSecondCategory({ type: 1 }).then((response) => {
        this.secondCategoryList = response.data.map((item) => ({
          label: item,
          value: item,
        }));
      });
    },
    // 获取修改历史
    historyHandle(row) {
      this.historyDialogVisible = true;
      this.currentRow = row;
    },

    // 导入
    handleImp() {
      this.$refs.processImport.init();
    },

    /** 分页查询 */
    getList() {
      this.loading = true;
      if (this.dateRange) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = undefined;
        this.queryParams.endTime = undefined;
      }
      apiGetPage(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetForm() {
      this.dateRange = false;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      };
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.addDialogVisible = true;
    },
    handleEdit(row, type) {
      this.currentRow = row;
      this.currentType = type;
      this.editDialogVisible = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delProductById(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
::v-deep .el-dialog__body {
  line-height: 36px;
}

.item-center {
  display: flex;
  align-items: center;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.font-size-14 {
  font-size: 14px;
}

.grey {
  color: #999;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
</style>
