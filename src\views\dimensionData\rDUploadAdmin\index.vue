<template>
  <div class="app-container">
    <div class="center-container" style="margin-top: 50px;">
      <div class="upload-year-container">
        <div class="upload-year-title">
          <i class="el-icon-date calendar-icon"></i> 研发上传年份设置
        </div>
        <div class="date-picker-container">
          <el-date-picker v-model="year" format="yyyy年" value-format="yyyy" type="year" placeholder="选择研发上传开启年份"
            class="upload-year-picker">
          </el-date-picker>
        </div>
        <div class="divider"></div>
        <div class="upload-year-action">
          <el-button type="primary" @click="openUpload()" :loading="uploadLoading" class="confirm-btn">
            <i class="el-icon-check"></i> 确认
          </el-button>
          <!-- <el-button @click="openUploadPopover = false, year = ''" class="cancel-btn">
            <i class="el-icon-close"></i> 取消
          </el-button> -->
        </div>
      </div>
      <!-- <el-popover placement="bottom" v-model="openUploadPopover" v-if="techDataButtonShow == 0" trigger="click"
        width="400" popper-class="upload-year-popover">
        <div class="upload-year-container">
          <div class="upload-year-title">
            <i class="el-icon-date calendar-icon"></i> 研发上传年份设置
          </div>
          <div class="date-picker-container">
            <el-date-picker v-model="year" format="yyyy年" value-format="yyyy" type="year" placeholder="选择研发上传开启年份"
              class="upload-year-picker">
            </el-date-picker>
          </div>
          <div class="divider"></div>
          <div class="upload-year-action">
            <el-button type="primary" @click="openUpload()" :loading="uploadLoading" class="confirm-btn">
              <i class="el-icon-check"></i> 确认
            </el-button>
            <el-button @click="openUploadPopover = false, year = ''" class="cancel-btn">
              <i class="el-icon-close"></i> 取消
            </el-button>
          </div>
        </div>
        <el-button slot="reference" v-if="techDataButtonShow == 0" type="primary" class="upload-trigger-btn">
          <i class="el-icon-upload2"></i>
          研发上传开启
        </el-button>
      </el-popover> -->
    </div>
  </div>
</template>

<script>
import add from "./components/add.vue";
import edit from "./components/edit.vue";
import {
  getTechDataPageSupplier,
  getDeptTree,
  getTechDataButton,
  getTechDataOpen,
} from "@/api/dimensionData/rDUpload.js";

export default {
  components: {
    add,
    edit,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 查询参数
      dateRange: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: undefined,
        auditStatus: undefined, //状态
        year: undefined, // 年份
        deptNo: undefined, // 部门
        userType: ''
      },
      //部门树
      deptTree: [],
      //机组详情
      posidList: {},
      year: '',//开启研发上传年份
      openUploadPopover: false,
      uploadLoading: false, // 添加上传按钮加载状态
      techDataButtonShow: '',//开启研发上传按钮显示权限
      userInfo: {},
    };
  },
  created() {
    this.queryParams.year = new Date(Date.now()).getFullYear().toString()
    getDeptTree().then(res => {
      this.deptTree = res.data
    })
    this.userInfo = JSON.parse(localStorage.getItem('userInfo'))
    this.queryParams.userType = this.userInfo.userType
    getTechDataButton({ userType: this.userInfo.userType }).then(res => {
      this.techDataButtonShow = res.data
    })
    this.getList();
  },
  methods: {
    viewFiles(files) {
      console.log(files)
    },
    cascaderChange(event) {
      if (event && event.length >= 1) {
        this.queryParams.deptNo = event[event.length - 1]
      } else {
        this.queryParams.deptNo = undefined
      }
    },
    //开启研发上传
    openUpload() {
      if (!this.year) {
        this.$message({
          message: '请选择年份',
          type: 'warning',
          duration: 2000
        });
        return
      }

      this.uploadLoading = true;
      getTechDataOpen({ year: Number(this.year) }).then(res => {
        this.uploadLoading = false;
        this.openUploadPopover = false;
        this.year = '';
        this.$message({
          message: '研发上传已成功开启',
          type: 'success',
          duration: 2000
        });
        // 刷新列表
        this.getList();
      }).catch(() => {
        this.uploadLoading = false;
      });
    },
    /** 分页查询 */
    getList() {
      this.loading = true;
      getTechDataPageSupplier(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    // 处理新增/修改/查看操作
    handleOperation(row, mode) {
      this.$refs.editDialog.show(row, mode);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.addDialog.show(null, 'add');
    },
    /** 查看草稿操作 */
    handleViewDraft() {
      this.$refs.addDialog.loadDraft();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  line-height: 36px;
}

// 研发上传开启功能样式
.center-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.text-center {
  text-align: center;
}

.upload-trigger-btn {
  transition: all 0.3s;
  border-radius: 4px;
  padding: 12px 25px;
  font-size: 16px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  background-color: #409EFF;

  i {
    margin-right: 8px;
    font-size: 18px;
    vertical-align: middle;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%);
    transition: all 0.8s;
  }

  &:hover:before {
    left: 100%;
  }
}

::v-deep .upload-year-popover {
  border-radius: 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  padding: 0;
  overflow: hidden;
  border: none;
  background-color: #fff;

  .el-popover__title {
    margin: 0;
    padding: 0;
  }

  .popper__arrow {
    display: none;
  }
}

.upload-year-container {
  padding: 0;

  .upload-year-title {
    font-size: 18px;
    font-weight: 500;
    color: #303133;
    padding: 20px;
    text-align: center;
    margin-bottom: 0;
    background-color: #f9f9f9;
    border-bottom: 1px solid #f0f0f0;

    .calendar-icon {
      margin-right: 8px;
      color: #409EFF;
      font-size: 20px;
    }
  }

  .date-picker-container {
    padding: 20px 20px 10px;
  }

  .upload-year-picker {
    width: 100%;
  }

  .divider {
    height: 1px;
    background-color: #f0f0f0;
    margin: 10px 0 0 0;
  }

  .upload-year-action {
    display: flex;
    justify-content: center;
    padding: 20px;
    gap: 15px;

    .el-button {
      padding: 12px 25px;
      transition: all 0.3s;
      border-radius: 4px;
      font-size: 15px;
      width: 120px;

      i {
        margin-right: 5px;
      }

      &:hover {
        transform: translateY(-1px);
      }
    }

    .confirm-btn {
      background-color: #409EFF;
    }

    .cancel-btn {
      border-color: #dcdfe6;
      color: #606266;
    }
  }
}

::v-deep .el-date-editor.el-input {
  width: 100%;
}

::v-deep .el-input__inner {
  height: 45px;
  line-height: 45px;
  font-size: 16px;
  border-radius: 4px;
}

::v-deep .el-input__icon {
  line-height: 45px;
}

.view-icon {
  color: #1890FF;
  font-size: 18px;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

.item-center {
  display: flex;
  align-items: center;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.font-size-14 {
  font-size: 14px;
}

.grey {
  color: #999;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
</style>
