import request from '@/utils/request'


// 分页获取列表数据
export function processClassList(query) {
    return request({
        url: `/flowable/workflow/category/list`,
        method:'get',
        params:query
    });
}

// 获取流程分类全部数据
export function processClassListAll() {
  return request({
      url: `/flowable/workflow/category/listAll`,
      method:'get',
  });
}


// 新增
export function addProcessClass(query) {
  return request({
      url: `/flowable/workflow/category`,
      method:'post',
      data:query
  });
}

// 编辑
export function editProcessClass(query) {
  return request({
      url: `/flowable/workflow/category`,
      method:'put',
      data:query
  });
}

// 删除
export function delProcessClass(categoryIds) {
  return request({
    url: `/flowable/workflow/category/${categoryIds}`,
    method:'delete'
  });
}
