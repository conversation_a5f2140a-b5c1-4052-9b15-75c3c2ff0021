<template> 
     <el-dialog
      title="自检材料提交"
      :visible.sync="dialogVisible"
      append-to-body
      width="600px"
    >
        <el-form ref="form" 
        :model="form" 
        :rules="rules">
            <el-form-item
            label="备注说明及NCR编码"
            >
                <el-input
                style="width: 80%"
                v-model="form.qcNcr"
                placeholder="请输入"
                ></el-input>
            </el-form-item>

            <el-form-item
            label="补充文件"
            prop="fileList"
            >
                <upload-file
                :show-file-list="true"
                :file-list="fileList"
                multiple
                :limit="null"
                @file-success="fileSuccess"
                @before-upload="beforeUpload"
                @before-remove="beforeRemove"
                @file-error="fileError"
                >
                    <el-button type="primary" size="small">上传文件</el-button>
                </upload-file>
            </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button
                @click="confirm()"
                type="success"
                size="small"
                icon="el-icon-success"
                plain
                >自检材料完整无误，提交审核</el-button>
            <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { submitLastQc } from "@/api/bigTestExperiment/remoteSupervision.js";
import uploadFile from '@/components/uploadFile/main'
export default {
  components:{uploadFile},

  data(){
    return{
        dialogVisible:false,
        btnLoading:false,
        uploadUrl: process.env.VUE_APP_BASE_API + "/user-server/file/upload",
        fileList:[],
        form:{
            fileList:[],
            qcNcr:''
        },
        rowData:null,
        qcStatus:null,
        rules:{
            fileList:[{ required: true, message: '请上传文件', trigger: 'blur' }]   
        }
    }
  },

  methods:{
    init(rowData,qcStatus){
        let fileList = rowData.qcFileList && rowData.qcFileList.length>0 ? JSON.parse(JSON.stringify(rowData.qcFileList)) : []
        this.fileList = fileList.map((item)=>{
            let row = {
                name:item.name,
                response:{
                    data:item
                }
            }
            return row
        })
        this.form.fileList = fileList
        this.form.qcNcr = rowData.qcNcr
        this.rowData = rowData
        this.qcStatus = qcStatus
        this.dialogVisible = true
        this.btnLoading = false
    },

    // 上传文件前
    beforeUpload(file,callBack){
        this.btnLoading = true
    },

    // 上传成功
    fileSuccess(response,file){
        this.form.fileList.push(response.data)
        this.btnLoading = false
    },

    // 文件上传失败
    fileError(error){
        this.btnLoading = false
    },

    // 删除文件前
    beforeRemove(file,fileList){
        let id = file.response.data.id
        let index = this.form.fileList.findIndex((item)=>{
            return item.id === id
        })
        this.form.fileList.splice(index,1)
    },

    confirm(){
        this.$refs['form'].validate((valid) => {
            if(valid){
                let params = {
                    id: this.rowData.id,
                    nodeId: this.rowData.planNodeId,
                    planId: this.rowData.planId,
                    qcStatus:this.qcStatus,
                    ...this.form
                }
                submitLastQc(params).then((res) => {
                    this.$modal.msgSuccess("操作成功");
                    this.btnLoading = false;
                    this.dialogVisible = false
                    this.$emit('getDetail')
                }).catch(() => {
                    this.btnLoading = false;
                });
            }
        })
        
        
    }
  },

}

</script>

<style scoped lang='less'>
</style>