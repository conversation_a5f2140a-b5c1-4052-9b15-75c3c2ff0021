<template>
  <!-- 授权用户 -->
  <el-dialog title="添加授权人员" :visible.sync="visible" width="800px" top="5vh" append-to-body>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="搜索" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table @row-click="clickRow" ref="table" :data="userList" @selection-change="handleSelectionChange" height="260px">
        <el-table-column type="selection" width="55"></el-table-column>
         <el-table-column label="序号" type="index" width="55"></el-table-column>
        <el-table-column label="人员姓名" prop="name" :show-overflow-tooltip="true" />
        <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
        <el-table-column label="手机" prop="phone" :show-overflow-tooltip="true" />
        <el-table-column label="状态" prop="enable">
          <template slot-scope="scope">
            <span class="zhuang">{{getEnable(scope.row.enable)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建日期" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.page"
        :limit.sync="queryParams.size"
        @pagination="getList"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSelectUser">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getUserListByAppRoleUnList,addAppAuth} from "@/api/system/appUser";
import { unallocatedUserList, authUserSelectAll } from "@/api/system/role";
export default {
  props: {
    // 角色编号
    id: {
      type: [Number, String]
    }
  },
  data() {
    return {
      // 遮罩层
      visible: false,
      // 选中数组值
      userIds: [],
      // 总条数
      total: 0,
      // 未授权用户数据
      userList: [],
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        id: undefined,
        keyword: undefined,
      }
    };
  },
  methods: {
    getEnable(enable){
     switch(enable){
      case 0:
        return '停用'
        break;
        case 1:
          return '正常'
     }
    },
    // 显示弹框
    show() {
      this.resetForm("queryForm");
      this.queryParams.id = this.id;
      this.getList();
      this.visible = true;
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.userIds = selection.map(item => item.id);
    },
    // 查询表数据
    getList() {
      getUserListByAppRoleUnList(this.queryParams).then(res => {
        this.userList = res.data;
        this.total = res.total;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 选择授权用户操作 */
    handleSelectUser() {
      const roleId = this.queryParams.id;
      const appUserIds  = this.userIds.join(",");
      if (appUserIds  == "") {
        this.$modal.msgError("请选择要分配的用户");
        return;
      }
      addAppAuth(roleId, appUserIds).then(res => {
        this.$modal.msgSuccess(res.msg);
        if (res.code === 1) {
          this.visible = false;
          this.$emit("ok");
        }
      });
    }
  }
};
</script>
