import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/user-server/appUser/getListPage',
    method: 'get',
    params: query,
  })
}
//系统角色获取
export function backFindAll() {
  return request({
    url: '/user-server/backRole/findAll',
    method: 'get'
  })
}
//app角色获取
export function appFindAll() {
  return request({
    url: '/user-server/appRole/findAll',
    method: 'get'
  })
}
// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/user-server/system/user/' + parseStrEmpty(userId),
    method: 'get'
  })
}
// 后台角色修改用户状态
export function updateStatus(param) {
  return request({
    url: '/user-server/appUser/updateStatus',
    method: 'get',
    params: param,
  })
}
// 新增用户
export function addUser(data) {
  return request({
    url: '/user-server/appUser/add',
    method: 'post',
    data: data,
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/user-server/appUser/update',
    method: 'post',
    data: data,
  })
}

// 删除用户
export function delUser(ids) {
  return request({
    url: `/user-server/appUser/deletes?ids=${ids}`,
    method: 'get',
  })
}

// 用户密码重置
export function resetUserPwd(id) {
  return request({
    url: `/user-server/appUser/resetPassword?id=${id}`,
    method: 'get',
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/user-server/system/user/changeStatus',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/user-server/system/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/user-server/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(query) {
  return request({
    url: '/user-server/appUser/updatePassword',
    method: 'post',
    params: query
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/user-server/appUser/updateImage',
    method: 'post',
    data: data
  })
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: '/user-server/system/user/authRole/' + userId,
    method: 'get'
  })
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: '/user-server/system/user/authRole',
    method: 'put',
    params: data
  })
}

// 修改供应商基本信息
export function supplierUpdate(query) {
  return request({
    url: '/user-server/supplier/update',
    method: 'post',
    data: query
  })
}

// 修改供应商头像
export function supplierUpdateImage(query) {
  return request({
    url: '/user-server/supplier/updateImage',
    method: 'post',
    data: query
  })
}

// 修改供应商密码
export function supplierUpdatePassword(query) {
  return request({
    url: '/user-server/supplier/updatePassword',
    method: 'get',
    params: query
  })
}
