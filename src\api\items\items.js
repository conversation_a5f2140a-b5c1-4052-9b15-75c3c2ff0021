import request from '@/utils/request'
// 部套列表
export function getDwgListForWeb(param) {
  return request({
    url: '/back-server/implementation/getDwgListForWeb',
    method: 'get',
    params: param,
  })
}
// 机组搜索列表
export function getEngineeringList(param) {
  return request({
    url: '/back-server/implementation/getEngineeringList',
    method: 'get',
    params: param,
  })
}
// 模块列表
export function getModuleList(param) {
  return request({
    url: '/back-server/implementation/getModuleList',
    method: 'get',
    params: param,
  })
}

//导出
export function exportData(query) {
  return request({
    url: '/back-server/implementation/exportDwgListExcel',
    method: 'get',
    params: query,
    responseType: 'blob', // important
  })
}
// 负责部门
export function getArrangeList(param) {
  return request({
    url: '/back-server/implementation/getArrangeList',
    method: 'get',
    params: param,
  })
}
