<template>
  <el-dialog
    title="项目详情"
    :visible.sync="visibleDialog"
    width="760px"
    append-to-body
  >
    <div v-loading="detailLoading">
      <el-row>
        <el-col :span="8"
          ><div class="grid-content bg-purple">
            <span>
              <span class="content-label">责任方： </span
              >{{ riskData.dutyName }}</span
            >
          </div></el-col
        >
        <el-col :span="8"
          ><div class="grid-content bg-purple-light">
            <span
              ><span class="content-label">涉及产品：</span>
              {{ riskData.productName }}</span
            >
          </div></el-col
        >
        <el-col :span="8"
          ><div class="grid-content bg-purple-light">
            <span>
              <span class="content-label">风险登记：</span>
              <span>{{
                listFind(riskLevelList, riskData.riskLevel)
                  ? listFind(riskLevelList, riskData.riskLevel).text
                  : ""
              }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row class="row-bg">
        <el-col :span="24"
          ><div class="grid-content bg-purple">
            <div class="content-label">风险点描述：</div>
            <span>{{ riskData.riskDesc }}</span>
          </div></el-col
        >
        <el-col :span="24"
          ><div class="grid-content bg-purple-light">
            <div class="content-label">原因分析：</div>
            <span>{{ riskData.riskReason }}</span>
          </div></el-col
        >
        <el-col :span="24"
          ><div class="grid-content bg-purple-light">
            <div class="content-label">保证措施：</div>
            <span>{{ riskData.measuresDesc }}</span>
          </div></el-col
        >
      </el-row>

      <div class="block">
        <div class="content-label">反馈内容：</div>
        <el-timeline>
          <el-timeline-item
            color="#1890FF"
            v-if="riskData.mesProjectRiskDeptList"
          >
            <div class="timeline-item-top">
              <span>责任部门反馈</span>
            </div>

            <div>
              <div
                class="timeline-item-dept"
                v-for="(item, index) in riskData.mesProjectRiskDeptList"
                :key="index"
              >
                <div class="card-left">
                  <p>{{ item.dutyDeptName }}（{{ item.dutyUserName }}）</p>
                  <p v-if="item.rejectDesc || item.leaderRejectDesc">
                    驳回原因：{{ item.rejectDesc || item.leaderRejectDesc }}
                  </p>
                  <p v-if="item.userPermission == 1">
                    回复：<el-input
                      type="textarea"
                      :rows="3"
                      placeholder="请输入内容"
                      v-model="item.feedbackDesc"
                    >
                    </el-input>
                  </p>

                  <p v-else>回复：{{ item.feedbackDesc || "待回复" }}</p>

                  <div v-if="item.userPermission == 1">
                    <el-upload
                      :file-list="item.feedbackFile"
                      class="upload-demo"
                      :action="uploadUrlFile"
                      :on-preview="handlePreview"
                      :on-remove="handleRemove"
                      :on-success="handleSuccess"
                      :headers="headers"
                      multiple
                    >
                      <el-button
                        size="small"
                        type="primary"
                        @click="beforeUpload(index)"
                        >上传文件</el-button
                      >
                    </el-upload>
                  </div>

                  <div
                    class="file-box"
                    v-if="item.userPermission != 1 && item.feedbackFile"
                  >
                    <div
                      v-for="(item, index) in item.feedbackFile"
                      :key="index"
                    >
                      <el-button type="text" @click="handlePreview(item)">{{
                        item.name
                      }}</el-button>
                    </div>
                  </div>

                  <div
                    class="btn-box submit-btn"
                    v-if="item.userPermission == 1"
                  >
                  <el-popover
                    placement="top"
                    width="350"
                    v-if="riskData.userRole == 2 && riskData.mesProjectRiskDeptList.length == 1 && riskData.mesProjectRiskSupplierList.length == 0"
                    v-model="visible">
                    <p>您未选择责任部门和责任供应商，请确认是否提交回复？如确认回复，则该风险点完成闭环，后续无法对该项目风险新增责任部门、责任供应商。</p>
                    <div style="text-align: right; margin: 0">
                      <el-button size="mini" type="text" @click="visible = false">取消</el-button>
                      <el-button type="primary" size="small" @click="submitFeedback(item)">确认</el-button>
                    </div>
                    <el-button slot="reference" type="primary" size="small">提交</el-button>
                  </el-popover>

                    <el-button
                      v-else
                      @click="submitFeedback(item)"
                      type="primary"
                      size="small"
                      >提交</el-button
                    >
                  </div>

                  <div
                    class="btn-box"
                    v-if="item.userPermission == 2 && riskData.userRole == 2"
                  >
                    <el-button
                      @click="handlePass(item)"
                      type="success"
                      size="small"
                      icon="el-icon-success"
                      plain
                      >通过</el-button
                    >
                    <el-button
                      @click="handleReject(item)"
                      type="danger"
                      size="small"
                      icon="el-icon-success"
                      plain
                      >驳回</el-button
                    >
                  </div>
                </div>

                <div class="card-left">
                  <p class="user-info">
                    <span
                      >{{ item.dutyUserLeaderDeptName }}（{{
                        item.dutyUserLeaderName
                      }}）</span
                    >
                    <i
                      class="icon el-icon-success"
                      v-if="item.leaderStatus === 1"
                    ></i>
                  </p>
                  <p v-if="item.leaderRejectDesc">
                    驳回原因：{{ item.leaderRejectDesc }}
                  </p>

                  <div
                    class="btn-box"
                    v-if="
                      item.userPermission == 2 &&(riskData.userRole == 3 || riskData.userRole == 1)
                    "
                  >
                    <el-button
                      @click="handlePass(item)"
                      type="success"
                      size="small"
                      icon="el-icon-success"
                      plain
                      >确认</el-button
                    >
                    <!-- <el-button
                      @click="handleReject(item)"
                      type="danger"
                      size="small"
                      icon="el-icon-success"
                      plain
                      >驳回</el-button
                    > -->
                  </div>
                </div>
              </div>

              <div class="add-btn" v-if="riskData.userRole == 2">
                <el-button
                  @click="handleAddDept()"
                  type="primary"
                  size="small"
                  icon="el-icon-plus"
                  plain
                  >新增责任部门</el-button
                >
              </div>
            </div>
          </el-timeline-item>

          <el-timeline-item color="#1890FF" >
            <div class="timeline-item-top">
              <span>厂商反馈</span>
              <!-- <span>已完成</span> -->
              <span></span>
            </div>
            <div class="">
              <div
                v-show="riskData.mesProjectRiskSupplierList"
                class="timeline-item-card"
                v-for="(aItem,aIndex) in riskData.mesProjectRiskSupplierList" :key="aIndex"
              >
                <div class="card-left">
                  <p>{{ aItem.supplierName }}</p>
                  <p
                    v-if="
                      aItem.status == 2 ||
                      aItem.status == 4
                    "
                  >
                    驳回原因：{{ aItem.rejectDesc }}
                  </p>
                  <p>
                    回复：{{ aItem.feedbackDesc }}
                  </p>

                  <!-- <div v-if="imageList" class="img-box">
                      <div v-for="(item, index) in imageList" :key="index">
                        <img
                          :src="item.url"
                          alt=""
                          @click="handlePictureCardPreview(item)"
                        />
                      </div>
                    </div>

                    <el-dialog :visible.sync="imgDialogVisible" append-to-body>
                      <img width="100%" :src="dialogImageUrl" alt="" />
                    </el-dialog> -->

                  <div class="file-box" v-if="fileList">
                    <div v-for="(item, index) in fileList" :key="index">
                      <el-button type="text" @click="handlePreview(item)">{{
                        item.name
                      }}</el-button>
                    </div>
                  </div>
                </div>

                <div
                  class="btn-box"
                  v-if="aItem.userPermission == 2"
                >
                  <el-button
                    @click="handlePass(aItem, true)"
                    type="success"
                    size="small"
                    icon="el-icon-success"
                    plain
                    >通过</el-button
                  >
                  <el-button
                    @click="handleReject(aItem, true)"
                    type="danger"
                    size="small"
                    icon="el-icon-success"
                    plain
                    >驳回</el-button
                  >
                </div>
              </div>

              <div class="add-btn" v-if="riskData.userRole == 2">
                <el-button
                  @click="handleAddSupplier()"
                  type="primary"
                  size="small"
                  icon="el-icon-plus"
                  plain
                  >新增责任供应商</el-button
                >
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <select-dept
      ref="selectDept"
      @getProjectRiskDept="getProjectRiskDept"
    ></select-dept>

    <dept-pass
      ref="deptPass"
      @getProjectRiskDept="getProjectRiskDept"
    ></dept-pass>

    <dept-reject
      ref="deptReject"
      @getProjectRiskDept="getProjectRiskDept"
    ></dept-reject>

    <suppiler-list
      ref="suppilerList"
      @getProjectRiskDept="getProjectRiskDept"
    ></suppiler-list>
  </el-dialog>
</template>

<script>
import {
  getProjectRiskDept,
  addFeedback,
} from "@/api/riskControl/projectRiskListByDept";
import deptPass from "./deptPass";
import deptReject from "./deptReject";
import selectDept from "./selectDept";
import suppilerList from "./suppilerList";
import { getToken } from "@/utils/auth";
export default {
  components: { deptPass, selectDept, deptReject, suppilerList },

  data() {
    return {
      uploadUrlFile:
        process.env.VUE_APP_BASE_API + "/user-server/file/uploadFile",
      headers: { "X-Token": "Bearer " + getToken() },
      detailLoading: false,
      visibleDialog: false,
      dialogImageUrl: "",
      imgDialogVisible: false,
      visible:false,
      riskData: {},
      riskLevelList: [
        {
          text: "严重",
          value: 0,
        },
        {
          text: "一般",
          value: 1,
        },
        {
          text: "轻微",
          value: 2,
        },
      ],
      rowData: null,

      // 文件列表
      fileList: [],
      listIndex: 0,
    };
  },

  methods: {
    init(row) {
      this.fileList = [];
      this.visibleDialog = true;
      this.rowData = row;
      this.getProjectRiskDept();
    },

    // 删除文件
    handleRemove(file, fileList) {
      this.riskData.mesProjectRiskDeptList[this.listIndex].deptFileList =
        fileList;
    },

    // 上传前
    beforeUpload(index) {
      this.listIndex = index;
    },

    // 上传文件成功
    handleSuccess(response, file, fileList) {
      this.riskData.mesProjectRiskDeptList[this.listIndex].deptFileList =
        fileList;
    },

    // 点击文件
    handlePreview(file) {
      window.open(
        process.env.VUE_APP_FILE_API + file.response.data.newName,
        "_blank"
      );
    },

    listFind(list = [], value) {
      let index = list.findIndex((item) => {
        return item.value == value;
      });
      if (index > -1) {
        return list[index];
      }
    },

    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.imgDialogVisible = true;
    },

    // 部门、二级部门领导、厂商审核通过
    handlePass(item, isSuppiler) {
      this.$refs.deptPass.init(this.riskData.id, item, isSuppiler);
    },

    // 部门、二级部门领导、厂商审核驳回
    handleReject(item, isSuppiler) {
      this.$refs.deptReject.init(this.riskData.id, item, isSuppiler);
    },

    // 添加部门
    handleAddDept() {
      this.$refs.selectDept.init(this.riskData.id);
    },

    handleAddSupplier() {
      this.$refs.suppilerList.init(this.riskData.id);
    },

    submitFeedback(item) {
      let feedbackFile = "";
      if (item.deptFileList) {
        feedbackFile = item.deptFileList.map((item) => {
          return {
            name: item.response.data.name,
            newName: item.response.data.newName,
          };
        });
      }
      addFeedback({
        feedbackFile: feedbackFile ? JSON.stringify(feedbackFile) : null,
        feedbackDesc: item.feedbackDesc,
        id: item.id,
      }).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getProjectRiskDept(this.riskData.id);
      });
    },

    getProjectRiskDept() {
      this.detailLoading = true;
      getProjectRiskDept(this.rowData.id)
        .then((res) => {
          this.detailLoading = false;
          this.riskData = res.data;
          //   if (res.data.mesProjectRiskSupplier.feedbackImage) {
          //     let list = JSON.parse(
          //       res.data.mesProjectRiskSupplier.feedbackImage
          //     );
          //     this.imageList = list.map((item) => {
          //       return {
          //         url: process.env.VUE_APP_IMAGE_API + item,
          //         response: {
          //           data: {
          //             newName: item,
          //           },
          //         },
          //       };
          //     });
          //   }
          if (this.riskData.mesProjectRiskDeptList) {
            this.riskData.mesProjectRiskDeptList.forEach((item) => {
              if (item.feedbackFile) {
                let fileAry = JSON.parse(item.feedbackFile);
                item.feedbackFile = fileAry.map((item) => {
                  return {
                    name: item.name,
                    response: {
                      data: {
                        name: item.name,
                        newName: item.newName,
                        url: "/file/",
                      },
                    },
                  };
                });
              } else {
                item.feedbackFile = [];
              }
            });
          }

          if (
            res.data.mesProjectRiskSupplier &&
            res.data.mesProjectRiskSupplier.feedbackFile
          ) {
            let fileAry = JSON.parse(
              res.data.mesProjectRiskSupplier.feedbackFile
            );
            this.fileList = fileAry.map((item) => {
              return {
                name: item.name,
                response: {
                  data: {
                    name: item.name,
                    newName: item.newName,
                    url: "/file/",
                  },
                },
              };
            });
          }
        })
        .catch(() => {
          this.detailLoading = false;
        });
    },
  },
};
</script>

<style scoped lang='scss'>
::v-deep .el-radio__label {
  display: none;
}
.img-box {
  display: flex;
  img {
    cursor: pointer;
    width: 146px;
    height: 146px;
    margin-right: 12px;
    border-radius: 8px;
  }
}
.over-text {
  font-size: 13px;
  width: 290px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.content-label {
  font-weight: 700;
}
::v-deep .el-dialog__body {
  line-height: 36px;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}
.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}
::v-deep .el-row--flex {
  margin-left: 22px;
}
// ::v-deep .el-dialog {
//   margin-top: 30vh !important;
// }
.timeline-item-top {
  display: flex;
  justify-content: space-between;
  background-color: #f3f6fd;
  padding: 4px 12px;
  border-radius: 4px;
}
.input-box {
  margin-top: 8px;
}
.timeline-item-card {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-top: 8px;
  background-color: #f6f7f9;
  padding: 4px 12px;
  border-radius: 4px;
  height: auto;
  box-sizing: border-box;
}
.add-btn {
  margin-top: 8px;
  text-align: right;
}

.submit-btn {
  text-align: end;
}

.timeline-item-dept {
  display: flex;
  margin-top: 8px;
  height: auto;

  .card-left {
    width: calc((100% - 10px) / 2);
    background-color: #f6f7f9;
    padding: 4px 12px;
    border-radius: 4px;
    margin-right: 10px;
    min-height: 80px;
    box-sizing: border-box;
    height: inherit;
    align-items: stretch;
    .user-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .icon {
        font-size: 14px;
        color: #65d299;
      }
    }
  }
  .card-left:last-of-type {
    margin-right: 0;
  }
}
</style>
