import request from "@/utils/request";
// 分页查询
export function apiGetList(params) {
  return request({
    url: "/sd-server/seven/dimension/page",
    method: "get",
    params: params,
  });
}
//获取评级名称数组
export function apiGetRatingNameList() {
  return request({
    url: "/sd-server/supplier/score/name",
    method: "get",
  });
}
//各事件分类
export function apiGetEventCategoryList(params) {
  return request({
    url: "/sd-server/seven/dimension/statistics/type",
    method: "get",
    params: params,
  });
}
//考核事件数量
export function apiGetEventCountList(params) {
  return request({
    url: "/sd-server/seven/dimension/statistics/num",
    method: "get",
    params: params,
  });
}
//供应商获取详情
export function apiGetSupplierDetail(params) {
  return request({
    url: "/sd-server/seven/dimension/one",
    method: "get",
    params: params,
  });
}
//获取罚分详情
export function apiGetPenaltyPointsDetail(params) {
  return request({
    url: "/sd-server/seven/dimension/detail",
    method: "get",
    params: params,
  });
}
//分类编号列表
export function apiGetTypeNoList() {
  return request({
    url: "/sd-server/type/list",
    method: "get",
  });
}
//获取确认状态列表
export function apiGetSubmitStatusList(params) {
  return request({
    url: "/sd-server/supplier/no/typeNo",
    method: "get",
    params,
  });
}
