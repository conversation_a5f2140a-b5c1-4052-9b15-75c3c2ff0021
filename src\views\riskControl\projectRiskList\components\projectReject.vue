<template>
    <el-dialog
      :visible.sync="dialogVisible"
      append-to-body
      title="驳回"
      @close="cancel"
      width="30%"
    >
      <el-form ref="form" :model="form" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="驳回原因"
              prop="rejectDesc"
              :rules="[
                { required: true, message: '请输入驳回原因', trigger: 'blur' },
              ]"
            >
              <el-input v-model="form.rejectDesc" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading"
          >确 定</el-button
        >
        <el-button @click="cancel"
          >取 消</el-button
        >
      </div>
    </el-dialog>
</template>

<script>
import { riskManageStatus } from "@/api/riskControl/projectRiskList";
export default {
  components:{},

  data(){
    return{
      dialogVisible:false,
      btnLoading:false,
      riskDataId:null,
      form:{
        rejectDesc:''
      }
    }
  },

  created(){},

  methods:{
    init(riskDataId){
      this.dialogVisible = true
      this.btnLoading = false,
      this.riskDataId = riskDataId
    },

    // 审核确定
    confirm() {
      this.$refs['form'].validateField('drawNum', valid => {
        if(valid){
          let params = {
            id: this.riskDataId,
            rejectDesc:form.rejectDesc,
            status: 1,
          }
          this.btnLoading = true
          riskManageStatus(params).then((res) => {
            this.$modal.msgSuccess("操作成功");
            this.btnLoading = false;
            this.dialogVisible = false;
            this.$emit('loadData')
          }).catch(()=>{
            this.btnLoading = false
          })
        }
      })
    },

    cancel(){
      this.$refs['form'].resetFields()
      this.dialogVisible = false
    }
  },

}

</script>

<style scoped lang='less'>
</style>