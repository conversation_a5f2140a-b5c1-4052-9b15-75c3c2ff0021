<template>
  <div class="process_category">
    <div class="search_form">
      <el-row type="flex" :gutter="6">
        <el-col :span="4">
          <el-input
            v-model="searchForm.processKey"
            class="format_option"
            size="small"
            placeholder="流程标识"
            clearable
          ></el-input>
        </el-col>

        <el-col :span="4">
          <el-input
            v-model="searchForm.processName"
            class="format_option"
            size="small"
            placeholder="流程名称"
            clearable
          ></el-input>
        </el-col>

        <el-col :span="4">
          <el-select
            v-model="searchForm.category"
            placeholder="请选择流程分类"
            class="format_option"
            clearable
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-col>

        <el-col :span="3">
          <el-select
            v-model="searchForm.state"
            placeholder="请选择状态"
            class="format_option"
            clearable
            filterable
          >
            <el-option
              v-for="item in dicList.deployStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>

        <el-col :span="4">
          <div class="btn_box">
            <el-button
              size="small"
              class="btn search_btn"
              icon="el-icon-search"
              type="primary"
              @click="search(false)"
              >搜索</el-button
            >
            <el-button
              size="small"
              class="btn reset_btn"
              icon="el-icon-refresh"
              @click="search(true)"
              >重置</el-button
            >
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="table_box">
      <el-table :data="tableData" v-loading="loading" style="width: 100%">
        <el-table-column type="index" label="序号" width="50" align="center">
        </el-table-column>

        <el-table-column
          v-for="(item, index) in tableColumn"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :width="item.width"
        >
          <template slot-scope="{ row }">
            <el-tag
              v-if="item.tabStatus"
              :type="row.suspended ? 'danger' : 'success'"
              >{{ row[item.prop] }}</el-tag
            >
            <el-tag v-else-if="item.versionStatus" type="default"
              >v{{ row[item.prop] }}</el-tag
            >
            <el-link
              v-else-if="item.linkstatus"
              type="primary"
              @click="handleProcessView(row)"
              >{{ row[item.prop] }}</el-link
            >
            <div v-else-if="item.checkTime">
              <i class="el-icon-time"></i>
              {{ row[item.prop] | dateTimeFormat }}
            </div>
            <div v-else>{{ row[item.prop] }}</div>
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" align="center" width="150">
          <template slot-scope="{ row }">
            <div class="handle_btn">
              <el-button
                type="text"
                @click="versionMan(row)"
                size="small"
                icon="el-icon-price-tag"
                >版本管理</el-button
              >
              <el-popconfirm title="是否确定删除？" @confirm="handleDel(row)">
                <el-button
                  slot="reference"
                  class="del"
                  type="text"
                  size="small"
                  icon="el-icon-delete"
                  >删除</el-button
                >
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="page_box">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentPage"
          :current-page="page.pageNum"
          :pager-count="5"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
        >
        </el-pagination>
      </div>
    </div>

    <version-man ref="versionMan"></version-man>
    <flow-chart ref="flowChart"></flow-chart>
  </div>
</template>
<script>
import versionMan from "./components/versionMan";
import flowChart from "./components/flowChart.vue";
import { deployList, delModel } from "@/api/processMan/deployMan";
import { processClassListAll } from "@/api/processMan/processClass.js";
import store from "@/store/index";
export default {
  components: {
    versionMan,
    flowChart,
  },
  data() {
    return {
      impRoleVisible: false,
      addRoleVisible: false,
      loading: false,
      searchForm: {
        processKey: "",
        processName: "",
        category: "",
        state: "",
      },
      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      dicList: store.state.dicList,
      typeOptions: [],
      tableData: [],
      tableColumn: [
        {
          prop: "processKey",
          label: "流程标识",
          width: "300",
        },
        {
          prop: "processName",
          label: "流程名称",
          linkstatus: true,
          width: "300",
        },
        {
          prop: "categoryName",
          label: "流程分类",
          width: "200",
        },
        {
          prop: "version",
          label: "流程版本",
          versionStatus: true,
          width: "",
        },
        {
          prop: "suspendedVal",
          label: "状态",
          tabStatus: true,
          width: "100",
        },
        {
          prop: "deploymentTime",
          label: "部署时间",
          checkTime: true,
          width: "200",
        },
      ],
    };
  },
  beforeCreate() {
    processClassListAll()
      .then((res) => {
        let resData = res.data;
        this.typeOptions = resData.map((item) => {
          return {
            categoryName: `${item.categoryName}`,
            id: item.id,
          };
        });
      })
      .catch(() => {});
  },
  created() {
    this.loadData();
  },
  methods: {
    hideDialog(dialog) {
      this.loadData();
    },
    search(reset) {
      if (reset) {
        this.searchForm = {
          processKey: "",
          processName: "",
          category: "",
          state: "",
        };
      }
      this.loadData();
    },

    // 删除/批量删除
    handleDel(row) {
      this.loading = true;
      delModel(row.deploymentId)
        .then((res) => {
          this.$message({
            type: "success",
            message: "操作成功",
            duration: 1500,
          });
          this.loadData();
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 流程图
    handleProcessView(row) {
      this.$refs.flowChart.init(row);
    },

    // 版本管理
    versionMan(row) {
      this.$refs.versionMan.init(row);
    },

    // 获取数据
    loadData() {
      this.loading = true;
      let params = {
        ...this.searchForm,
        ...this.page,
      };
      deployList(params)
        .then((res) => {
          let resData = res.data;
          resData.records.forEach((item) => {
            item.suspendedVal = item.suspended ? "挂起" : "激活";
          });
          this.tableData = resData.records;
          this.page.total = resData.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 更改每页显示条数
    handleSizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.loadData();
    },

    // 选择页数/点击上一页/下一页
    handleCurrentPage(currentPage) {
      this.page.pageNum = currentPage;
      this.loadData();
    },
  },
};
</script>
<style lang="less" scoped>
.process_category {
  padding: 16px 12px 0;
}
.page_box {
  text-align: right;
}
</style>
