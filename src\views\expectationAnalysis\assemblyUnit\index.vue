<template>
    <div class="app-container">
      <el-form
        :model="searchForm"
        ref="searchForm"
        size="small"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="搜索" prop="posid" label-width="40px">
          <el-input
            v-model="searchForm.query"
            placeholder="部套编码/部套名/令号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="机组类别" prop="posid" label-width="80px">
          <el-select v-model="searchForm.modelId" clearable filterable placeholder="机组类别">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery(false)"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="handleQuery(true)"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
  
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            plain
            @click="handleAdd()"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-upload
          action="#"
          class="margin-left"
          accept=".xlsx,.xls"
          :http-request="fileSuccess"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :file-error="fileError"
          >
            <el-button type="warning"
            icon="el-icon-download"
            size="mini"
            plain>导入</el-button>
          </el-upload>
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="loadData"
        ></right-toolbar>
      </el-row>
  
      <el-table
        v-loading="loading"
        :data="tableData"
      >
        <el-table-column label="序号" type="index" align="center" />
        <el-table-column label="机组类别" prop="modelNameList" align="center"/>
        <el-table-column label="部套名称" prop="name" align="center" />
        <el-table-column label="编码规则" prop="partCode" align="center" />

        <el-table-column label="创建时间" prop="crtTime" align="center">
          <template slot-scope="{row}">
            <div>{{row.crtTime | dateFormat}}</div>
          </template>
        </el-table-column>

        <el-table-column label="启用状态" prop="" align="center">
          <template slot-scope="{row}">
            <el-switch
              v-model="row.status"
              @change="changeStatus(row)"
              :active-value="1"
              :inactive-value="0"
              >
            </el-switch>
          </template>
        </el-table-column>

        <el-table-column label="重点关注" prop="" align="center">
          <template slot-scope="{row}">
            <el-switch
              v-model="row.attentionStatus"
              @change="partAttention(row)"
              :active-value="1"
              :inactive-value="0"
              >
            </el-switch>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="{row}">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleInfo(row)"
              >查看</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-copy-document"
              @click="handleAdd(row)"
              >复制</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDel(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
  
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="searchForm.pageNum"
        :limit.sync="searchForm.pageSize"
        @pagination="loadData"
      />
      <check-info ref="checkInfo"></check-info>
      <add-unit ref="addUnit" @hideDialog="loadData"></add-unit>
    </div>
  </template>
  
  <script>

  import checkInfo from './components/checkInfo'
  import addUnit from './components/addUnit.vue';
  import { partPage,delPart,partStatus,partAttention,amountExcel,modelList } from '@/api/expectationAnalysis/assemblyUnit'
  export default {
    components:{checkInfo,addUnit},
    data() {
      return {
        total: 0,
        showSearch:true,
        loading:false,
        tableData:[],
        typeOptions:[],
        searchForm:{
          query:'',
          modelId:'',
          pageSize:10,
          pageNum:1
        },
      };
    },
    created() {
      this.modelList()
      this.loadData()
    },
    methods: {
      handleQuery(flag){
        if(flag){
          this.searchForm.query = ''
          this.searchForm.pageNum = 1
        }
        this.loadData()
      },

      // 上传文件前
      beforeUpload(file,callBack){
        this.loading = true
      },

      // 上传成功
      fileSuccess(file){
        let formData = new FormData()
        formData.append('file',file.file)
        amountExcel(formData).then((res)=>{
          this.$message({
            type:'success',
            message:'操作成功',
            duration:1500
          })
          this.loadData()
        }).catch(()=>{
          this.loading = false
        })

      },

      // 文件上传失败
      fileError(error){
        this.loading = false
      },

      // 新增
      handleAdd(row){
        this.$refs.addUnit.init(row)
      },

      // 详情
      handleInfo(row){
        this.$refs.checkInfo.init(row)
      },

      // 删除
      handleDel(row){
        this.$confirm('删除关联会将项目已关联的期量删除，是否确认？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(()=>{
            this.loading = true
            delPart(row.id).then(()=>{
              this.$message({
                type:'success',
                message:'操作成功',
                duration:1500
              })
              this.loadData()
            }).catch(()=>{
              this.loading = false
            })
        });
      },

      // 更改状态
      changeStatus(row){
        this.loading = true
        let params = {
          status:row.status,
          id:row.id
        }
        partStatus(params).then((res)=>{
          this.$message({
            type:'success',
            message:'操作成功',
            duration:1500
          })
          this.loadData()
        }).catch(()=>{
          this.loading = false
        })
      },
      
      // 更改重点关注
      partAttention(row){
        this.loading = true
        let params = {
          attention:row.attentionStatus,
          id:row.id
        }
        partAttention(params).then((res)=>{
          this.$message({
            type:'success',
            message:'操作成功',
            duration:1500
          })
          this.loadData()
        }).catch(()=>{
          this.loading = false
        })
      },

      // 获取机组下拉
      modelList(){
        modelList().then((res)=>{
            let resData = res.data || []
            this.typeOptions = resData.map((item)=>{
              return {label:item.name,value:item.id}
            })
        })
      },

      loadData(){
        this.loading = true
        let params = {
          ...this.searchForm,
        }
        partPage(params).then((res)=>{
          let {records,total}  = res.data || {}
          this.tableData = records

          this.total = total
          this.loading = false
        }).catch(()=>{
          this.loading = false
        })
      },
    },
  };
  </script>
  
  <style scoped>
  </style>
  