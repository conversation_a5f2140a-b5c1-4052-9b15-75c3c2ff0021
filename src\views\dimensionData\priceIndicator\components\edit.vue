<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="700px" style="padding-bottom: 20px;"
    :before-close="handleClose">
    <div v-loading="loading" class="info-section">
      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">事件日期：</span>
            <span class="info-content">{{ detail.crtTime || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">二级分类：</span>
            <span class="info-content">{{ detail.secondType || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">分类编号：</span>
            <span class="info-content">
              <el-select v-model="detail.typeNo" filterable placeholder="请选择">
                <el-option v-for="(item, index) in typeNoList" :key="index" :label="item.typeNo" :value="item.typeNo">
                  <template #default>
                    <el-tooltip :content="item.typeName" placement="top">
                      <span>{{ item.typeNo }}</span>
                    </el-tooltip>
                  </template>
                </el-option>
              </el-select>
              <!-- <el-tooltip :content="detail.productName" placement="top">
                <span>{{ detail.typeNo || '-' }}</span>
              </el-tooltip> -->
            </span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">招标编号：</span>
            <span class="info-content">{{ detail.bidNo || '-' }}</span>
          </div>
        </el-col>
      </el-row>


      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">供应商编号：</span>
            <span class="info-content">{{ detail.supplierCode || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">供应商编码：</span>
            <span class="info-content">{{ detail.supplierNo || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">供应商名称：</span>
            <span class="info-content">{{ detail.supplierName || '-' }}</span>
          </div>
        </el-col>
      </el-row>


      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">判罚原因：</span>
            <span class="info-content">{{ detail.reason || '-' }}</span>
          </div>
        </el-col>
        <!-- <el-col :span="12">
          <div class="info-item">
            <span class="info-label">主题：</span>
            <span class="info-content">{{ detail.theme || '-' }}</span>
          </div>
        </el-col> -->
      </el-row>


    </div>
    <template v-if="type === 'edit' || type === 'delete'">
      <div class="form-section">
        <div class="form-item">
          <span class="required-label">{{ type === 'delete' ? '删除' : '改分' }}理由</span>
          <el-input type="textarea" v-model="form.updateReason" :rows="4" placeholder="请输入理由" />
        </div>

        <div class="form-item">
          <span class="required-label">证明材料：</span>
          <el-upload class="upload-demo" :action="uploadFileUrl" :headers="headers" :before-upload="beforeUpload"
            :on-success="handleUploadSuccess" :on-error="handleUploadError" :file-list="fileList">
            <el-button size="small" type="primary">添加文件</el-button>
          </el-upload>
        </div>

        <div class="form-item score-section">
          <span>扣分：{{ detail.score || '-' }}分</span>
          <span class="score-divider">变更为：</span>
          <el-input v-model="form.score" class="score-input" @input="handleScoreInput" />
          <span class="score-range">(0-100)</span>
        </div>
      </div>
    </template>

    <template v-if="type === 'view'">
      <div class="form-section">
        <div class="form-item score-section">
          <span>罚分：{{ detail.score || '-' }}分</span>
        </div>
      </div>
    </template>

    <div class="tip-text" v-if="getTipText">
      注：{{ getTipText }}
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ type === 'view' ? '关闭' : '取消' }}</el-button>
      <el-button v-if="type !== 'view'" :type="type === 'delete' ? 'danger' : 'primary'" @click="handleSubmit">
        {{ type === 'delete' ? '删除' : '确定' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { apiUpdate, apiDelete } from '@/api/dimensionData/priceIndicator'
import { getToken } from "@/utils/auth";
import { apiGetSubmitStatusList } from '@/api/appraiseManager/appraiseAll'
import { QualitySecondaryCategory, getQualityTipText } from '@/enums/qualityIndicator'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: [Object],
      required: true
    },
    type: {
      type: String,
      default: 'view',
      validator: function (value) {
        return ['view', 'edit', 'delete'].indexOf(value) !== -1
      }
    }
  },
  data() {
    return {
      typeNoList: [],
      loading: false,
      detail: {},
      form: {
        updateReason: '',
        score: '',
        fileIds: []
      },
      fileList: [],
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/user-server/file/uploadFile", // 上传的图片服务器地址
      headers: {
        "X-Token": "Bearer " + getToken(),
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      const titles = {
        view: '查看',
        edit: '修改罚分',
        delete: '删除'
      }
      return titles[this.type]
    },
    getTipText() {
      return getQualityTipText(this.detail.secondType)
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getDetail()
        this.getAllTypeNo()
      }
    }
  },
  methods: {
    // 获取分类编号
    async getAllTypeNo() {
      const { data } = await apiGetSubmitStatusList({ supplierCode: this.detail.supplierCode })
      this.typeNoList = data
    },
    // 获取详情
    async getDetail() {
      try {
        this.loading = true
        this.detail = this.row
        this.form.score = this.row.score.toString()
      } catch (error) {
        this.$message.error('获取详情失败')
      } finally {
        this.loading = false
      }
    },
    handleScoreInput(value) {
      value = value.replace(/[^\d]/g, '')
      let num = parseInt(value, 10)

      if (value === '') {
        this.form.score = ''
        return
      }

      if (isNaN(num)) {
        num = 0
      } else if (num > 100) {
        num = 100
      }

      this.form.score = num.toString()
    },
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
        return false
      }
      return true
    },
    handleUploadSuccess(response) {
      console.log(response);
      this.form.fileIds.push(response.data.id)
      this.$message.success('上传成功')
    },
    handleUploadError() {
      this.$message.error('上传失败')
    },
    handleClose() {
      this.form = {
        updateReason: '',
        score: '',
        fileIds: []
      }
      this.fileList = []
      this.dialogVisible = false
    },
    async handleSubmit() {
      if (!this.form.score) {
        this.$message.error('请输入变更分数')
        return
      }
      if (this.detail.score.toString() !== this.form.score && !this.form.updateReason) {
        this.$message.error(`请输入${{ edit: '改分', confirm: '确认', batchConfirm: '批量确认', delete: '删除' }[this.type]}理由`)
        return
      }
      // if (this.detail.score.toString() !== this.form.score && this.form.fileIds.length === 0) {
      //   this.$message.error('变更分时请上传证明材料')
      //   return
      // }
      console.log(this.form);

      try {
        if (this.type == 'delete') {
          await apiDelete({
            id: this.row.id,
            ...this.form,
            typeNo: this.detail.typeNo
          })
        } else {
          await apiUpdate({
            id: this.row.id,
            ...this.form,
            typeNo: this.detail.typeNo
          })
        }
        this.$message.success(this.type === 'delete' ? '删除成功' : '修改成功')
        this.$emit('refresh')
        this.handleClose()
      } catch (error) {
        this.$message.error(this.type === 'delete' ? '删除失败' : '修改失败')
      }
    }
  }
}
</script>

<style scoped>
.info-section {
  padding: 10px 20px 0;
}

.el-row {
  margin-bottom: 0 !important;
}

.info-item {
  display: flex;
  line-height: 30px;
}

.info-label {
  width: 110px;
  text-align: right;
  padding-right: 8px;
  color: #606266;
}

.info-label.nowrap {
  white-space: nowrap;
  width: auto;
}

.info-content {
  flex: 1;
  color: #303133;
}

.form-section {
  padding: 0 20px;
  margin-top: 15px;
}

.form-item {
  margin-bottom: 15px;
}

.required-label::before {
  /* content: '*';
  color: #F56C6C; */
  margin-right: 4px;
}

.score-section {
  display: flex;
  align-items: center;
}

.score-divider {
  margin: 0 10px;
}

.score-input {
  width: 80px;
}

.score-range {
  margin-left: 10px;
  color: #909399;
}

.tip-text {
  color: #409EFF;
  font-size: 12px;
  margin: 15px 0;
  line-height: 1.4;
}

/* 覆盖 element-ui 的默认样式 */
:deep(.el-row) {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

:deep(.el-col) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

:deep(.score-input .el-input__inner) {
  padding-right: 8px !important;
}

:deep(.score-input .el-input__suffix) {
  display: none;
}
</style>
