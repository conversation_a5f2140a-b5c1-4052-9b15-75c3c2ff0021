import request from '@/utils/request'
// 分页查询公告
export function getfindAllPage(param) {
    return request({
        url: '/user-server/announcementManagement/findAllPage',
        method: 'get',
        params: param,
    })
}
// 添加公告
export function addAnnouncement(param) {
    return request({
        url: '/user-server/announcementManagement/add',
        method: 'post',
        data: param,
    })
}
//   删除单个公告
export function deleteAnnouncementManagement(ids) {
    return request({
        url: `/user-server/announcementManagement/delete?announcementId=${ids}`,
        method: 'get',
    })
}
// 删除多个公告
export function deletesAnnouncementManagement(ids) {
    return request({
        url: `/user-server/announcementManagement/deletes?announcementIds=${ids}`,
        method: 'get',
       
    })
}
// 查询公告详情
export function deteilfindOne(ids) {
    return request({
        url: `/user-server/announcementManagement/findOne?announcementId=${ids}`,
        method: 'post',
        // data: param,
    })
}
//修改公告信息
export function alterUpdate(param) {
    return request({
        url: '/user-server/announcementManagement/update',
        method: 'post',
        data: param,
    })
}
// 上传文件
export function uploadFile(param) {
    return request({
        url: '/user-server/file/uploadFile',
        method: 'post',
        data: param,
    })
}
