import request from "@/utils/request";
// 供应商统计
export function getSupplierList(data) {
  return request({
    url: `/mes-server/mes/part/supplier/list`,
    method: "get",
    params: data,
  });
}

// 供应商统计汇总
export function getSupplierSummary(params) {
  return request({
    url: "/mes-server/mes/part/supplier/summary",
    method: "get",
    params,
  });
}

// 铸锻件维护频次
export function getMaintenanceScheduleList(params) {
  return request({
    url: "/mes-server/mes/part/supplier/record",
    method: "get",
    params,
  });
}

// 供应商完成情况列表
export function getSupplierCompletionList(params) {
  return request({
    url: "/mes-server/mes/part/casting/supplier",
    method: "get",
    params,
  });
}

//供应商完成情况汇总
export function getSupplierCompletionSummary(params) {
  return request({
    url: "/mes-server/mes/part/casting/summary",
    method: "get",
    params,
  });
}
