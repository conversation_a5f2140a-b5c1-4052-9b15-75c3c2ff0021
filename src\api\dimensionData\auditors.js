import request from "@/utils/request";

// 分页查询新建流程
export function processList(query) {
  return request({
    url: `/sd-server/tech/user`,
    method: "get",
    params: query,
  });
}
//批量新增
export function batchAdd(query) {
  return request({
    url: `/sd-server/tech/user/batch`,
    method: "post",
    data: query,
  });
}
export function apiDelete(query) {
  return request({
    url: `/sd-server/tech/user`,
    method: "delete",
    params: query,
  });
}
