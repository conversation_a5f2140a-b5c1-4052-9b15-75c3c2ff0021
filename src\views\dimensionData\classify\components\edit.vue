<template>
  <el-dialog
    :title="title"
    :visible="dialogVisible"
    @close="handleClose"
    width="70%"
    append-to-body
  >
    <div class="filter-container">
      <!-- <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="所属部门" :key="queryParams.year">
          <el-select v-model="queryParams.department" placeholder="请选择" style="width: 200px" clearable :key="updateTime"
            @clear="handleClearDept">
            <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>

        </el-form-item>
        <el-form-item label="年份">
          <el-date-picker v-model="queryParams.year" type="year" value-format="yyyy" placeholder="选择年份"
            @change="yearChange" style="width: 200px" clearable>
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-input v-model="queryParams.keyword" placeholder="资料名称" style="width: 200px" class="filter-item" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form> -->
    </div>

    <div class="table-container">
      <!-- <div class="table-operations" v-if="type !== 'view'">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </div> -->

      <el-table :data="tableData" style="width: 100%" height="50vh">
        <el-table-column type="index" label="序号" width="50" align="center" />
        <el-table-column prop="typeNo" label="分类编号" />
        <el-table-column prop="typeName" label="产品名称" />

        <el-table-column
          prop="applyStatus"
          label="是否适用"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag :type="scope.row.applyStatus ? 'danger' : 'info'">
              {{ scope.row.applyStatus ? "适用" : "不适用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="200"
          align="center"
          v-if="type !== 'view'"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
              >修改</el-button
            >
            <!-- <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{
        type === "view" ? "关闭" : "取 消"
      }}</el-button>
      <el-button type="primary" @click="handleSubmit" v-if="type !== 'view'"
        >确 定</el-button
      >
    </div>
    <edit-dialog
      :visible.sync="subDialogVisible"
      :title="subDialogTitle"
      :row="formData"
      :type="type"
      @submit="handleDialogSubmit"
    />
    <add-dialog
      ref="addDialog"
      :visible.sync="addDialogVisible"
      :rowId="row.id"
      @refresh="getDataList"
    />
  </el-dialog>
</template>

<script>
import EditDialog from "./editDialog.vue";
import AddDialog from "./add.vue";
import {
  getList,
  addTechnical,
  updateTechnical,
  deleteTechnical,
  apiGetOne,
  apiGetDeptByYear,
  apiUpdate,
} from "@/api/dimensionData/classify";

export default {
  name: "Edit",
  components: {
    AddDialog,
    EditDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "",
    },
    row: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      updateTime: new Date().getTime().toString(),
      dialogVisible: false,
      title: "修改",
      queryParams: {
        department: undefined,
        year: new Date().getFullYear().toString(),
        keyword: "",
      },
      departmentOptions: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      yearOptions: (() => {
        const currentYear = new Date().getFullYear();
        const years = [];
        for (let i = currentYear - 2; i <= currentYear + 2; i++) {
          years.push({
            value: i.toString(),
            label: `${i}年`,
          });
        }
        return years;
      })(),
      tableData: [],
      subDialogVisible: false,
      addDialogVisible: false,
      subDialogTitle: "",
      formData: {
        name: "",
        required: false,
        description: "",
      },
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val;
        if (val) {
          // this.getDeptList()
          this.getDataList();
        }
      },
      immediate: true,
    },
  },
  methods: {
    async getDataList() {
      try {
        const { data } = await apiGetOne({
          id: this.row.id,
          ...this.queryParams,
        });
        this.tableData = data.supplierTypeList;
      } catch (error) {
        console.error("获取列表失败：", error);
      }
    },
    handleSearch() {
      this.getDataList();
    },
    resetQuery() {
      this.queryParams = {
        department: undefined,
        year: new Date().getFullYear().toString(),
        keyword: "",
      };
      this.queryParams.department = undefined;
      this.getDeptList();
      this.getDataList();
    },
    handleAdd() {
      this.addDialogVisible = true;
    },
    handleView(row) {
      this.subDialogTitle = "查看";
      this.formData = { ...row };
      this.subDialogVisible = true;
    },
    handleEdit(row) {
      this.subDialogTitle = "修改";
      this.formData = { ...row };
      this.subDialogVisible = true;
    },
    async handleDelete(row) {
      try {
        await this.$confirm("确认删除该资料?", "提示", {
          type: "warning",
        });
        await deleteTechnical({ stddcId: row.stddcId });
        this.$message.success("删除成功");
        this.getDataList();
      } catch (error) {
        if (error !== "cancel") {
          console.error("删除失败：", error);
          this.$message.error("删除失败");
        }
      }
    },
    async handleDialogSubmit(data) {
      try {
        if (this.subDialogTitle === "新增") {
          await addTechnical(data);
        } else if (this.subDialogTitle === "修改") {
          await apiUpdate({
            ...data,
            id: this.formData.id,
          });
        }
        this.$message.success("保存成功");
        this.subDialogVisible = false;
        this.getDataList();
      } catch (error) {
        console.error("保存失败：", error);
        this.$message.error("保存失败");
      }
    },
    handleSubmit() {
      this.$emit("refresh");
      this.handleClose();
    },
    // 处理对话框关闭
    handleClose() {
      this.$emit("update:visible", false);
    },
    // 获取部门列表
    getDeptList() {
      if (!this.queryParams.year) {
        this.queryParams.year = new Date().getFullYear().toString();
      }
      apiGetDeptByYear({ year: this.queryParams.year })
        .then((response) => {
          if (
            response.data &&
            Array.isArray(response.data) &&
            response.data.length > 0
          ) {
            this.departmentOptions = response.data.map((item) => ({
              value: item.deptNo,
              label: item.deptName,
            }));
          } else {
            this.departmentOptions = [];
          }
          this.queryParams.department = undefined;
        })
        .catch((error) => {
          console.error("获取部门列表失败", error);
          this.departmentOptions = [];
          this.queryParams.department = undefined;
        });
    },
    // 年份变化时触发
    yearChange(val) {
      console.log("年份变化:", val);
      this.updateTime = new Date().getTime().toString();
      // 强制清空已选择的部门
      this.$set(this.queryParams, "department", undefined);
      // 清空部门列表
      this.departmentOptions = [];
      // 获取新的部门列表
      this.$nextTick(() => {
        this.getDeptList();
      });
    },
    // 处理清除部门选择
    handleClearDept() {
      this.queryParams.department = undefined;
      this.getDataList();
    },
  },
};
</script>

<style lang="less" scoped>
.table-operations {
  margin-bottom: 16px;
}
</style>
