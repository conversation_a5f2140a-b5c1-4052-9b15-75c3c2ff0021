<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="日期筛选">
        <el-date-picker v-model="queryParams.year" type="year" placeholder="选择年份" value-format="yyyy"
          class="year-picker" clearable />
      </el-form-item>
      <el-form-item label="搜索" prop="query">
        <el-input v-model="queryParams.query" placeholder="供方编号/供方编码/供方名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="供货情况" prop="query">
        <el-select v-model="queryParams.supplyStatus" placeholder="请选择" clearable>
          <el-option label="全部" value="-1" />
          <el-option label="无供货" value="0" />
          <el-option label="有供货" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>

      </el-form-item>
    </el-form>
    <div class="mb8 justify-between">
      <el-row :gutter="10" class="item-center">
        <el-col :span="1.5">
          <el-button type="primary" size="mini" @click="ratingMaintenance()">评级维护</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-upload2" size="mini" @click="openBatch()">导出全部</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-upload2" size="mini"
            @click="strategicShowDialog()">战略供应商维护</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button icon="el-icon-refresh" size="mini" @click="handleUpdate">更新</el-button>
        </el-col>
      </el-row>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </div>
    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange">
      <el-table-column label="排名" prop="ranking" width="50" align="center">
        <template slot-scope="scope">
          <div v-if="!scope.row.ranking">-</div>
          <div v-else>{{ scope.row.ranking }}</div>
        </template>
      </el-table-column>
      <el-table-column label="QMS编码" prop="supplierNo" width="120" />
      <!-- <el-table-column label="集团招采编码" prop="supplierNo100" width="120" align="center">
        <template slot-scope="scope">
          <div v-if="!scope.row.supplierNo100">-</div>
          <div v-else>{{ scope.row.supplierNo100 }}</div>
        </template>
      </el-table-column> -->
      <el-table-column label="SAP编码" prop="supplierCode" width="120" />
      <el-table-column label="供方名称" prop="supplierName" />
      <el-table-column label="总分" prop="score" />
      <el-table-column label="评级" prop="scoreName" />
      <el-table-column label="年度" prop="yearGroup" width="120" />
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <strategic-supplier-dialog :visible.sync="strategicDialogVisible" @refresh="handleRefresh" />
    <rating-maintenance-dialog :visible.sync="dialogVisible" @refresh="handleRefresh" />
    <year-picker-dialog ref="yearPickerDialog" @confirm="handleYearConfirm" />
  </div>
</template>

<script>
import { apiGetList, apiSyncData } from '@/api/appraiseManager/supplySideAppraise'
import RatingMaintenanceDialog from './components/RatingMaintenanceDialog.vue'
import StrategicSupplierDialog from './components/strategicSupplierDialog.vue'
import YearPickerDialog from './components/YearPickerDialog.vue'
export default {
  components: {
    StrategicSupplierDialog,
    RatingMaintenanceDialog,
    YearPickerDialog
  },
  data() {
    return {
      // 战略供应商弹窗
      strategicDialogVisible: false,
      // 评级维护弹窗
      dialogVisible: false,

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: undefined, //模块名称
        powerPl: undefined, //业主名称（电厂）
        year: new Date().getFullYear().toString(), //年度，默认当前年
        supplyStatus: "-1", //供货情况
      },
      //机组详情
      posidList: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleUpdate() {
      this.$refs.yearPickerDialog.open()
    },
    handleYearConfirm(year) {
      console.log('选择的年份:', year)
      apiSyncData({ year }).then((response) => {
        this.$message.success('同步成功')
        this.getList();
      })
    },
    //功能开发中弹窗提示
    openBatch() {
      this.$modal.msgError('功能开发中');
    },
    // 战略供应商弹窗
    strategicShowDialog() {
      this.strategicDialogVisible = true
    },

    handleRefresh() {
      // 刷新列表数据
      this.resetQuery()
    },
    // 评级维护
    ratingMaintenance() {
      this.dialogVisible = true

    },

    /** 分页查询 */
    getList() {
      this.loading = true;
      apiGetList(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetForm() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        year: new Date().getFullYear().toString(), // 重置时也设为当前年
        supplyStatus: "-1", //供货情况
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },

  },
};
</script>

<style scoped>
::v-deep .el-dialog__body {
  line-height: 36px;
}

.item-center {
  display: flex;
  align-items: center;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.font-size-14 {
  font-size: 14px;
}

.grey {
  color: #999;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}

::v-deep .el-row--flex {
  margin-left: 22px;
}
</style>
