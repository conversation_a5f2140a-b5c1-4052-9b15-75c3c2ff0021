<template>
    <el-dialog
      title="新增"
      :visible.sync="dialogVisible"
      @close="cancel"
      width="50%"
      >
        <div class="container">
        <el-form ref="form" :model="form" label-width="100px" :rules="rules">
            <div class="form-item">
                <el-form-item label="部套名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入"></el-input>
                </el-form-item>
            </div>
            <div class="form-item">
                <el-form-item label="部套编码" prop="partCode">
                    <el-input v-model="form.partCode" placeholder="请输入"></el-input>
                </el-form-item>
            </div>
            
            <div class="def-row">
                <div class="def-col">
                    <el-form-item label="技术准备日期">
                        <el-input v-model.number="form.ytMonth" placeholder="请输入">
                            <span slot="suffix">天</span>
                        </el-input>
                    </el-form-item>
                </div>
                <div class="def-col">
                    <el-form-item label="定标周期" prop="bidMonth">
                        <el-input v-model.number="form.bidMonth" placeholder="请输入">
                            <span slot="suffix">天</span>
                        </el-input>
                    </el-form-item>
                </div>
            </div>

            <div class="def-row">
                <div class="def-col">
                    <el-form-item label="采购周期" prop="produceCycleMonth">
                        <el-input v-model.number="form.produceCycleMonth" placeholder="请输入">
                            <span slot="suffix">天</span>
                        </el-input>
                    </el-form-item>
                </div>
                <div class="def-col">
                    <el-form-item label="生产周期" prop="produceMonth">
                        <el-input v-model.number="form.produceMonth" placeholder="请输入">
                            <span slot="suffix">天</span>
                        </el-input>
                    </el-form-item>
                </div>
            </div>

        </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="btnLoading">确 定</el-button>
        </span>
    </el-dialog>
  </template>
  
<script>
import { qtPart } from '@/api/expectationAnalysis/assemblyUnit'
export default {
    data(){
        return {
            dialogVisible:false,
            btnLoading:false,
            form:{
                name:'',
                partCode:'',
                
                ytMonth:'',
                bidMonth:'',
                produceCycleMonth:'',
                produceMonth:''
            },
            rules:{
                name:[
                    { required: true, message: '请输入部套名称', trigger: 'blur' },   
                ],
                partCode:[
                    { required: true, message: '请输入部套编码', trigger: 'blur' },   
                ],

                ytMonth:[
                    { required: true, message: '请输入技术准备日期', trigger: 'blur' },   
                ],
                bidMonth:[
                    { required: true, message: '请输入定标周期', trigger: 'blur' },   
                ],
                produceCycleMonth:[
                    { required: true, message: '请输入采购周期', trigger: 'blur' },   
                ],
                produceMonth:[
                    { required: true, message: '请输入生产周期', trigger: 'blur' },   
                ],
                
            }
        }
    },

    methods:{
        init(row){
            this.dialogVisible = true
            this.btnLoading = false
            if(row){
                this.form = JSON.parse(JSON.stringify(row))
                delete this.form.id
            }else{
                this.form = {
                    name:'',
                    partCode:'',
                    
                    ytMonth:'',
                    bidMonth:'',
                    produceCycleMonth:'',
                    produceMonth:''
                }
            }
        },

        confirm(){
            this.$refs['form'].validate((valid) => {
                if(valid){
                    this.btnLoading = true
                    let params = {
                        ...this.form
                    }
                    qtPart(params).then((res)=>{
                        this.$message({
                            type:'success',
                            message:'操作成功',
                            duration:1500
                        })
                        this.$emit('hideDialog')
                        this.btnLoading = false
                        this.dialogVisible = false
                    }).catch(()=>{
                        this.btnLoading = false
                    })
                    
                }
            })
        },

        cancel(){
            this.$refs['form'].resetFields()
            this.dialogVisible = false
        }
    }
}
</script>
  
<style scoped>
    ::v-deep.el-divider {
        margin:-1px 0;
    }

    .form-item {
        width:70%;
    }

    .def-row {
        display:flex;
        margin:10px 0;
    }
    .def-row > .def-col{
        width:50%;
        font-size:14px;
        text-align:left;
    }
    .def-row ::v-deep.el-form-item__label {
        color:#409EFF;
    }
    .def-col.text {
        height:36px;
        display:flex;
        flex:1;
        align-items:center;
        padding-left:12px;
        box-sizing:border-box;
    }
    .big-size {
        font-size:18px;
        font-weight:500;
    }
</style>