import request from '@/utils/request'

// 分页查询部署管理列表
export function deployList(query) {
    return request({
        url: `/flowable/workflow/deploy/list`,
        method:'get',
        params:query
    });
}

// 删除
export function delModel(deployIds) {
    return request({
        url: `/flowable/workflow/deploy/${deployIds}`,
        method:'delete',
    });
}


// 读取xml文件
export function getBpmnXml(definitionId) {
    return request({
        url: `/flowable/workflow/deploy/bpmnXml/${definitionId}`,
        method:'get',
    });
}

// 获取版本管理列表
export function publishList(query) {
    return request({
        url: `/flowable/workflow/deploy/publishList`,
        method:'get',
        params:query
    });
}

// 激活/挂起
export function changeStatus(query) {
    return request({
        url: `/flowable/workflow/deploy/${query.state}/changeState/${query.definitionId}`,
        method:'put'
    });
}

