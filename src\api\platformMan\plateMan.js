import request from '@/utils/request'
// 新增板块
export function addPlate(params) {
  return request({
    url: `/system/module`,
    method:'post',
    data: params,
  });
}

// 修改板块
export function editPlate(params) {
  return request({
    url: `/system/module`,
    method:'put',
    data: params,
  });
}

// 板块列表
export function plateList(params) {
  return request({
    url: `/system/module/page`,
    method: "get",
    params: params,
  });
}

export function getPlateList(params) {
  return request({
    url: `/system/module/list`,
    method: "get",
    params: params,
  });
}

// 删除板块
export function delPlate(moduleId) {
  return request({
    url: `/system/module/${moduleId}`,
    method: "delete",
  });
}

// 更改板块状态
export function changePlateStatus(params) {
  return request({
    url: `/system/module/${params.moduleId}/status/${params.status}`,
    method: "put",
  });
}
