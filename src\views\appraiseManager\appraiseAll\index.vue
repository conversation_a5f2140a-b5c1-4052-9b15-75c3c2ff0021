<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="日期筛选">
        <date-range-picker v-model="dateRange" :use-default-date="false" @date-change="onDateChange"
          style="min-width: 260px; max-width: 530px;" value-format="yyyy-MM-dd" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </date-range-picker>
      </el-form-item>
      <el-form-item label="评级" prop="scoreLevel">
        <el-select v-model="queryParams.scoreLevel" placeholder="请选择">
          <el-option v-for="item in ratingRangeList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分类编号" prop="typeNo">
        <el-select style="width: 100%" v-model="queryParams.typeNo" placeholder="请选择" filterable
          :filter-method="filterTypeNo" clearable>
          <el-option v-for="item in filteredEventCategoryList" :key="item.typeNo" :label="item.typeNo"
            :value="item.typeNo">
            <template #default>
              <el-tooltip :content="item.typeName" placement="top">
                <span>{{ item.typeNo }}</span>
              </el-tooltip>
            </template>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供货情况" prop="supplyStatus">
        <el-select style="width: 100%" v-model="queryParams.supplyStatus" placeholder="请选择">
          <el-option v-for="item in optionList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供方名称" prop="query">
        <el-input v-model="queryParams.query" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
      </el-form-item>
    </el-form>
    <div class=" mb-10 flex justify-end">
      <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
    </div>

    <div class="mb8 justify-between">
      <el-row :gutter="10" class="item-center">
        <!-- <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-upload2" size="mini" @click="openBatch()">导出当前</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-upload2" size="mini" @click="openBatch()">导出全部</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-upload2" size="mini" @click="handleImp()">导入</el-button>
        </el-col>-->
        <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-upload2" size="mini" plain :disabled="total == 0" @click="exportList">导出</el-button>
        </el-col>
        <el-col :span="1.5" class="font-size-14 ">
          <span class="grey">当前供应商总数为</span>{{ total }} <span class="grey">家</span>
        </el-col>
      </el-row>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </div>
    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange"
      @sort-change="handleSortChange">
      <el-table-column label="排名" prop="ranking" width="50" />
      <el-table-column label="供方名称" prop="supplierName" width="300" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.row)"> {{ scope.row.supplierName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="总分" prop="totalScore" sortable="custom" />
      <el-table-column label="质量" prop="qualityScore" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.qualityScore }}/<el-button type="text" @click="handlePenaltyPointsDetail(scope.row, 1)">{{
            scope.row.qualityCount }}条</el-button>
        </template>
      </el-table-column>
      <el-table-column label="交货" prop="deliveryScore" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.deliveryScore }}/<el-button type="text" @click="handlePenaltyPointsDetail(scope.row, 2)">{{
            scope.row.deliveryCount }}条</el-button>
        </template>
      </el-table-column>
      <el-table-column label="服务" prop="serveScore" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.serveScore }}/<el-button type="text" @click="handlePenaltyPointsDetail(scope.row, 3)">{{
            scope.row.serveCount }}条</el-button>
        </template>
      </el-table-column>
      <el-table-column label="技术" prop="technologyScore" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.technologyScore }}/<el-button type="text" @click="handlePenaltyPointsDetail(scope.row, 4)">{{
            scope.row.technologyCount }}条</el-button>
        </template>
      </el-table-column>
      <el-table-column label="价格" prop="priceScore" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.priceScore }}/<el-button type="text" @click="handlePenaltyPointsDetail(scope.row, 5)">{{
            scope.row.priceCount }}条</el-button>
        </template>
      </el-table-column>
      <el-table-column label="响标度" prop="bidScore" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.bidScore }}/<el-button type="text" @click="handlePenaltyPointsDetail(scope.row, 6)">{{
            scope.row.bidCount }}条</el-button>
        </template>
      </el-table-column>
      <el-table-column label="供货量" prop="supplyScore" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.supplyScore }}/<el-button type="text" @click="handlePenaltyPointsDetail(scope.row, 7)">{{
            scope.row.supplyCount }}条</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <div class="mt-40 flex justify-between">
      <bar-chart></bar-chart>
      <line-chart></line-chart>
    </div>
    <add ref="addDialog"></add>
    <process-import ref="processImport" @dialog="getData"></process-import>
    <!-- 供应商详情弹窗 -->
    <supplier-detail-dialog :visible.sync="dialogVisible" :row="currentRow" />
    <!-- 罚分弹窗 -->
    <penalty-points-detail-dialog :visible.sync="PenaltyPointsDialogVisible" :row="currentRow" />
  </div>
</template>

<script>
import { apiGetList, apiGetRatingNameList, apiGetTypeNoList } from '@/api/appraiseManager/appraiseAll'
import { exportFile } from '@/utils/gloabUtile';
import BarChart from "./components/BarChart.vue";
import LineChart from "./components/LineChart.vue";
import ProcessImport from "./components/processImport.vue";
import SupplierDetailDialog from './components/SupplierDetailDialog.vue'
import PenaltyPointsDetailDialog from './components/penaltyPointsDetailDialog.vue'
import DateRangePicker from './components/DateRangePicker.vue'

export default {
  components: {
    BarChart,
    LineChart,
    ProcessImport,
    SupplierDetailDialog,
    PenaltyPointsDetailDialog,
    DateRangePicker
  },
  data() {
    return {
      //分类编号列表
      eventCategoryList: [],
      // 评级范围
      ratingRangeList: [],
      //供货情况
      optionList: [
        { label: '全部', value: -1 },
        { label: '无供货', value: 0 },
        { label: '有供货', value: 1 },
      ],
      //供应方名称弹窗
      dialogVisible: false,
      //罚分弹窗
      PenaltyPointsDialogVisible: false,
      //选中行id
      currentRow: "",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: null,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: '', //模块名称
        startTime: null, //开始时间
        endTime: null, //结束时间
        status: null, //状态
        sortType: '', // 排序列
        scoreLevel: null, // 供应商评级
        typeNo: null, // 分类编号
        sort: '', // 排序方向
        supplyStatus: -1, //供货情况
      },
      //机组详情
      posidList: {},
      // 添加过滤后的列表
      filteredEventCategoryList: [], // 添加新的数组用于存储过滤后的分类列表
    };
  },
  created() {
    this.getList();
    this.getRatingNameList();
    this.dateRange = null;
    this.getEventCategoryList();
  },
  watch: {
    dateRange: {
      handler(newVal) {
        console.log('父组件 dateRange 变化:', newVal)
      },
      immediate: true
    }
  },
  methods: {
    getData() {
      this.queryParams.pageNum = 1;
      this.getList();
      this.getRatingNameList();
      this.getEventCategoryList();
    },
    //功能开发中弹窗提示
    openBatch() {
      this.$modal.msgError('功能开发中');
    },
    //获取分类编号列表
    getEventCategoryList() {
      apiGetTypeNoList().then((response) => {
        this.eventCategoryList = response.data;
        this.filteredEventCategoryList = response.data;
      });
    },
    //获取评级名称数组
    getRatingNameList() {
      apiGetRatingNameList().then((response) => {
        this.ratingRangeList = response.data;
      });
    },
    handlePenaltyPointsDetail(row, type) {
      this.currentRow = {
        ...row,
        ...this.queryParams,
        type
      }
      this.PenaltyPointsDialogVisible = true
    },
    handleDetail(row) {
      this.currentRow = {
        ...row,
        ...this.queryParams,
      }
      this.dialogVisible = true
    },

    // 导入
    handleImp() {
      this.$refs.processImport.init();
    },

    /** 分页查询 */
    getList() {
      this.loading = true;
      apiGetList(this.queryParams).then((response) => {
        this.postList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 导出
    exportList() {
      const _this = this
      this.$confirm('是否导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // this.queryParams.ids = this.ids.join(','),
          exportFile('/sd-server/seven/dimension/excel', this.queryParams)
      });
    },
    resetForm() {
      this.dateRange = null
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        supplyStatus: -1, //供货情况
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = false;
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.$refs.addDialog.init(row);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delProductById(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    // 排序触发事件
    handleSortChange(column) {
      if (column.prop) {
        //排序类型,0��分,1质量,2交货,3服务,4技术,5价格,6响标度,7供货量
        this.queryParams.sortType = { 'totalScore': '0', 'qualityScore': '1', 'deliveryScore': '2', 'serveScore': '3', 'technologyScore': '4', 'priceScore': '5', 'bidScore': '6', 'supplyScore': '7' }[column.prop]
        this.queryParams.sort = column.order === 'ascending' ? '1' : '2'
      } else {
        this.queryParams.sortType = ''
        this.queryParams.sort = '1'
      }
      this.getList()
    },
    onDateChange(val) {
      console.log('日期变更事件:', val)
      if (val && val.dateRange) {
        this.queryParams.startTime = val.dateRange[0]
        this.queryParams.endTime = val.dateRange[1]
      } else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
      }
    },
    filterTypeNo(query) {
      if (query !== "") {
        this.filteredEventCategoryList = this.eventCategoryList.filter(item => {
          return item.typeNo.toLowerCase().includes(query.toLowerCase()) ||
            item.typeName.toLowerCase().includes(query.toLowerCase())
        });
      }
      return this.eventCategoryList;
    },
  },
};
</script>

<style scoped>
.mt-40 {
  margin-top: 40px;
}

.mb-10 {
  margin-bottom: 10px;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}

::v-deep .el-dialog__body {
  line-height: 36px;
}

.item-center {
  display: flex;
  align-items: center;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.font-size-14 {
  font-size: 14px;
}

.grey {
  color: #999;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}

::v-deep .el-row--flex {
  margin-left: 22px;
}

/* 添加日期选择器相关样式 */
::v-deep .el-date-editor {
  flex: 1;

  .el-range-separator {
    padding: 0;
  }

  .el-range-input {
    width: 42%;
  }

  .el-range__icon,
  .el-range__close-icon {
    line-height: 24px;
  }
}

::v-deep .el-date-editor--daterange {
  height: 32px;
  line-height: 32px;
}

::v-deep .el-form--inline .el-form-item {
  margin-right: 30px;
  vertical-align: top;
}
</style>
