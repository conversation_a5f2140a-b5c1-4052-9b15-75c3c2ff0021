<template>
    <el-dialog title="导入" 
    :visible.sync="dialogVisible" 
    width="40%" 
    @close="cancel()"
    append-to-body>
        <div class="import-box">
            <el-button class="upload-btn" type="primary" size="small" @click="handleDonwLoad()" icon="el-icon-download">下载模板</el-button>
            <el-upload
            class="margin-left"
            action="#"
            show-file-list
            accept=".xlsx,.xls"
            :file-list="fileList"
            :limit="1"
            :http-request="fileSuccess"
            :before-upload="beforeUpload"
            :file-error="fileError"
            :before-remove="beforeRemove"
            >
                <el-button class="upload-btn" type="primary" size="small" plain :loading="btnLoading" icon="el-icon-upload2">导入</el-button>
            </el-upload>
        </div>

        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="confirm()" :loading="btnLoading">确 定</el-button>
            <el-button @click="cancel()">取 消</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { exportFile } from '@/utils/gloabUtile'
import { planImport } from '@/api/smallTestExperiment/experimentPlan'
export default {
  components:{},

  data(){
    return{
        dialogVisible:false,
        btnLoading:false,
        fileList:[]
    }
  },

  methods:{
    init(){
        this.dialogVisible = true
        this.btnLoading = false
    },
    handleDonwLoad(){
        exportFile('/mes-server/sm/plan/export')
    },
    // 上传文件前
    beforeUpload(file,callBack){
        this.btnLoading = true
    },

    // 上传成功
    fileSuccess(file){
        this.fileList = [file.file]
        this.fileSucess = true
        this.btnLoading = false
    },

    // 文件上传失败
    fileError(error){
        this.btnLoading = false
    },

    // 删除文件前
    beforeRemove(file,fileList){
        this.fileList = []
        this.fileSucess = false
    },

    confirm(){
        if(this.fileList.length === 0){
            this.$message({
                type:'warning',
                message:'请上传文件',
                duration:2000
            })
            return
        }
        let formData = new FormData()
        let file = this.fileList[0]
        formData.append('file',file)
        this.btnLoading = true
        planImport(formData).then((res)=>{
            this.$message({
                type:'success',
                message:'操作成功',
                duration:1500
            })
            this.btnLoading = false
            this.dialogVisible = false
            this.$emit('getList')
        }).catch(()=>{
            this.fileList = []
            this.btnLoading = false
        })
    },

    cancel(){
        this.fileList = []
        this.dialogVisible = false
    }
  },

}

</script>

<style scoped lang='scss'>
.import-box {
    display:flex;
}
.margin-left {
    margin-left:10px;
}
.upload-btn {
    height:36px;
}
</style>