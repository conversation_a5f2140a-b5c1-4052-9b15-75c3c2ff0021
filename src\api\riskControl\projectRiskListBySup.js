import request from '@/utils/request'
// 供应商分页列表
export function getRiskSupplierPage(param) {
    return request({
        url: '/mes-server/project/risk/supplier/page',
        method: 'get',
        params: param,
    })
}

// 详情
export function getProjectRiskSupplier(id) {
    return request({
        url: `/mes-server/project/risk/supplier/${id}`,
        method: 'get',
    })
}

// 反馈
export function addSupplierFeedback(param) {
    return request({
        url: `/mes-server/project/risk/supplier/feedback`,
        method: 'put',
        data: param
    })
}