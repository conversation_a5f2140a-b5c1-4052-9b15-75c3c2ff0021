<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="模块名称" prop="moduleName">
        <el-input
          v-model="queryParams.moduleName"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模块编码" prop="moduleCode">
        <el-input
          v-model="queryParams.moduleCode"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="令号" prop="posid" label-width="40px">
        <el-input
          v-model="queryParams.posid"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="post1">
        <el-input
          v-model="queryParams.post1"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业主名称" prop="powerPl">
        <el-input
          v-model="queryParams.powerPl"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目交期">
        <el-date-picker
          v-model="dateRange"
          @change="timeChange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="status" label-width="40px">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable>
          <el-option
            v-for="dict in studes"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleImp"
          >导出</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column label="序号" type="index" />
      <el-table-column label="模块名称" prop="moduleName" />
      <el-table-column label="模块编码" prop="moduleCode" />
      <el-table-column label="部套数" prop="completed">
        <template slot-scope="scope">
          <a
            @click="toItems(scope.row.posid, scope.row.moduleCode)"
            style="color: #1890ff; cursor: pointer"
            >{{ scope.row.completed }}</a
          >
        </template>
      </el-table-column>
      <el-table-column label="令号" prop="posid" />
      <el-table-column label="项目名称" prop="post1">
        <template slot-scope="scope">
          <a @click="item(scope.row)" style="color: #1890ff; cursor: pointer">{{
            scope.row.post1
          }}</a>
        </template>
      </el-table-column>
      <el-table-column label="板块名称" prop="boardName" />
      <el-table-column label="状态" prop="inShip" />
      <el-table-column label="排期变化" prop="schange" />
      <el-table-column label="合同交期" prop="zps0010" />
      <el-table-column label="项目交期" prop="zps0079" />
      <el-table-column label="状态" prop="alarmStatus">
             <template slot-scope="scope">
          <span
            style="
              font-size: 12px;
              display: inline-block;
              width: 44px;
              height: 28px;
              line-height: 28px;
              border-radius: 3px;
              text-align: center;
            "
            :class="
              scope.row.alarmStatus == 1
                ? 'bao'
                : scope.row.alarmStatus == 2
                ? 'yu'
                : scope.row.alarmStatus == 3
                ? 'jin':'finish'
            "
            >{{ scope.row.alarmStatus == 1
                ? '报警'
                : scope.row.alarmStatus == 2
                ? '预警'
                : scope.row.alarmStatus == 3
                ? '正常':'完成' }}</span
          >
        </template>
      </el-table-column>

      <!-- <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="skip(scope.row)"
            >设为已完成</el-button
          >
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:post:remove']"
          >删除</el-button> -->
        </template></el-table-column
      >
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="岗位名称" prop="postName">
          <el-input v-model="form.postName" placeholder="请输入岗位名称" />
        </el-form-item>
        <el-form-item label="岗位编码" prop="postCode">
          <el-input v-model="form.postCode" placeholder="请输入编码名称" />
        </el-form-item>
        <el-form-item label="岗位顺序" prop="postSort">
          <el-input-number
            v-model="form.postSort"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="岗位状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog> -->
    <!-- 项目名称详情弹框 -->
    <el-dialog
      title="项目详情"
      :visible.sync="itemOpen"
      width="600px"
      append-to-body
    >
      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >机组名称：<span>{{ posidList.post1 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >令号：<span>{{ posidList.posid }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >产品类型：<span>{{ posidList.prodType }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >机组容量：<span>{{ posidList.usr04 }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >合同签订日期：<span>{{ posidList.zps0177 }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >定类：<span>{{ posidList.projDl }}</span></span
            >
          </div></el-col
        >
      </el-row>

      <el-row type="flex" class="row-bg">
        <el-col :span="12"
          ><div class="grid-content bg-purple">
            <span
              >所属电厂：<span>{{ posidList.opowerPl }}</span></span
            >
          </div></el-col
        >
        <el-col :span="12"
          ><div class="grid-content bg-purple-light">
            <span
              >板块名称：<span>{{ posidList.boardName }}</span></span
            >
          </div></el-col
        >
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import {
  getModuleListForWeb,
  getPost,
  delPost,
  addPost,
  updatePost,
  exportData
} from "@/api/ProductionPlanning/Modulemanagement";

export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        page: 1,
        size: 10,
        moduleName: undefined, //模块名称
        moduleCode: undefined, //模块编码
        posid: undefined, //令号
        post1: undefined, //机组名称
        powerPl: undefined, //业主名称（电厂）
        startTime: undefined, //开始时间
        endTime: undefined, //结束时间
        status: undefined, //状态
      },
      //机组详情
      posidList: {},
      // 表单参数
      form: {},
      studes: [
        { label: "正常", value: 0 },
        { label: "报警", value: 1 },
        { label: "预警", value: 2 },
        { label: "完成", value: 3 },
      ],
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" },
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 导出
    handleImp(){
      const _this = this;
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(()=> {
        let params = {
          page:this.queryParams.page,
          size:this.queryParams.size
        }
        exportData(params).then((res) => {
            const url = window.URL.createObjectURL(new Blob([res]));
            const link = document.createElement("a");
            link.target = "_blank";
            link.href = url;
            link.setAttribute("download", "生产计划大纲模块数据.xlsx");
            document.body.appendChild(link);
            link.click();
          }
        );
      });
    },
    
    //创建时间处理
    timeChange() {
      this.queryParam.startTime = this.dateRange[0];
      this.queryParam.endTime = this.dateRange[1];
    },
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getModuleListForWeb(this.queryParams).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    // handleAdd() {
    //   this.reset();
    //   this.open = true;
    //   this.title = "添加岗位";
    // },
    /** 修改按钮操作 */
    // handleUpdate(row) {
    //   this.reset();
    //   const postId = row.postId || this.ids;
    //   getPost(postId).then((response) => {
    //     this.form = response.data;
    //     this.open = true;
    //     this.title = "修改岗位";
    //   });
    // },
    /** 提交按钮 */
    // submitForm: function () {
    //   this.$refs["form"].validate((valid) => {
    //     if (valid) {
    //       if (this.form.postId != undefined) {
    //         updatePost(this.form).then((response) => {
    //           this.$modal.msgSuccess("修改成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       } else {
    //         addPost(this.form).then((response) => {
    //           this.$modal.msgSuccess("新增成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       }
    //     }
    //   });
    // },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    // 项目名称弹窗
    item(row) {
      this.posidList = JSON.parse(JSON.stringify(row));
      
      this.itemOpen = true;
    },
    // 查看部套跳转 需要带参数 跳转到部套管理  根据参数查询
    // toItems(){
    //  this.$router.push("/items/Department");

    // },
    // 部套数跳转  需要带参数 跳转到部套管理  根据参数查询
    toItems(posid, moduleCode) {
      localStorage.setItem("posid", posid);
      localStorage.setItem("moduleCode", moduleCode);
      this.$router.push({
        path: "/items/items/Department/index",
      });
    },
  },
};
</script>

<style  scoped>
::v-deep .el-dialog__body {
  line-height: 36px;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
.jin {
  background: #E8F4FF;
  border: 1px solid rgba(56,154,255,0.4);
  color: #389AFF;
}
.finish {
  background: #E7FAF0;
  border: 1px solid rgba(101,210,153,0);
  color: #65D299;
}
::v-deep .el-row--flex{
  margin-left: 22px;
}
::v-deep .el-dialog{
  margin-top: 30vh !important;
}
</style>
