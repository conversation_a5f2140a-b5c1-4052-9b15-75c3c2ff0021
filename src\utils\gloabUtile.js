import axios from 'axios';
import { getToken } from '@/utils/auth'
import { Message } from 'element-ui';
import { Loading } from 'element-ui';

// 判断是否为JSON对象
function isJSON(str) {
    if (typeof str == 'string') {
        try {
            JSON.parse(str);
            return true;
        } catch (e) {
            return false;
        }
    }
}

// get请求下载文件
export function exportFile(url, query) {
    let token = `Bearer ${getToken()}`
    let loadingInstance = Loading.service();
    axios({
        method: 'get',
        baseURL: process.env.VUE_APP_BASE_API,
        // 超时
        timeout: 300000,
        url,
        params: query,
        responseType: 'blob',
        headers: {
            'X-Token': token,
            'Content-Type': 'application/json'
        },
    }).then((res) => {
        const reader = new FileReader()
        reader.readAsText(res.data, 'utf-8')
        reader.onload = function () {
            // 这里下载所有文件名称统一取后端传来的
            if (res.headers['content-disposition'] && (reader.result && !isJSON(reader.result))) {
                let blob = res.data
                // 创建一个新的url，此url指向新建的Blob对象
                let url = window.webkitURL.createObjectURL(blob)
                let name = window.decodeURI(res.headers['content-disposition'].split('=')[1])
                // 创建a标签，并隐藏改a标签
                let link = document.createElement('a')
                link.style.display = 'none'
                // a标签的href属性指定下载链接
                link.href = url
                //setAttribute() 方法添加指定的属性，并为其赋指定的值。
                link.setAttribute('download', name)
                document.body.appendChild(link)
                link.click()
            } else {
                let result = '请求失败';
                if (isJSON(reader.result)) {
                    const { msg, code } = JSON.parse(reader.result)
                    result = msg || '请求失败'
                }
                Message.error(result)
            }
            loadingInstance.close();
        }

    }).catch(() => {
        loadingInstance.close();
        Message.error('系统错误')
    })
}

