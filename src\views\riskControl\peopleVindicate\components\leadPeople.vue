<template>
    <el-dialog title="选择部门领导" :visible.sync="dialogVisible" width="800px" append-to-body>
        <el-form
          :model="dialogQueryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="搜索" prop="keyword">
            <el-input
              v-model="dialogQueryParams.keyword"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="dialoghHandleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="dialoghHandleQuery"
              >搜索</el-button
            >
          </el-form-item>

        </el-form>

        <el-table
          v-loading="dialogLoading"
          :data="dialogTableList"
          row-key="id"
          @row-click="singleElection"
        >
          <el-table-column type="index" width="30" align="center">
            <template slot-scope="{ row }">
              <el-radio
                class="radio"
                v-model="selectData"
                :label="row.id"
              ></el-radio>
            </template>
          </el-table-column>
          
          <el-table-column label="序号" type="index" align="center" width="50" />
          <el-table-column label="人员姓名" prop="name" align="center" />
          <el-table-column label="所属部门" prop="alarmStatus" align="center" />
          <el-table-column label="手机号" prop="phone" align="center" />
          <el-table-column label="邮箱" prop="email" align="center" />
        </el-table>
  
        <pagination
          v-show="dialogTotal > 0"
          :total="dialogTotal"
          :page.sync="dialogQueryParams.page"
          :limit.sync="dialogQueryParams.size"
          @pagination="getListUser"
        />
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
  </template>
  
  <script>
  import { listUser } from "@/api/system/user.js";
  import { leaderUser } from '@/api/riskControl/peopleVindicate'
  export default {
    components:{},
  
    data(){
      return{
          dialogVisible:false,
          dialogLoading:false,
          dialogTableList:[],
          dialogTotal: 0,
          selectData:'',
          rowData:null,
          dialogQueryParams: {
              keyword: "",
              page: 1,
              size: 10,
              enable: 1,
          },
      }
    },
  
    methods:{
      init(row){
          this.dialogVisible = true
          this.rowData = row
          this.dialogQueryParams.page = 1
          this.selectData = ''
          this.getListUser()
      },

      singleElection(row){
        this.selectData = row.id
      },
  
      getListUser() {
        this.dialogLoading = true;
        listUser(this.dialogQueryParams).then((res) => {
          this.dialogLoading = false;
          this.dialogTableList = res.data;
          this.dialogTotal = res.total;
        });
      },
  
      dialoghHandleQuery() {
        this.dialogQueryParams.page = 1;
        this.getListUser();
      },
  
      submitForm() {
        if(!this.selectData){
          this.$message({
            type:'warning',
            message:'请选择数据',
            duration:2000
          })
          return
        }
        if(this.rowData){
          let params = {
            id:this.rowData.id,
            leaderUserId:this.selectData
          }
          leaderUser(params).then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.dialogVisible = false;
              this.$emit('getList');
            })
            .catch(() => {});
        }else{
          this.$emit('selectData',this.selectData)
          this.dialogVisible = false;
        }
      },
      cancel() {
        this.dialogVisible = false;
      },
    },
  
  }
  
  </script>
  
  <style scoped>
  </style>