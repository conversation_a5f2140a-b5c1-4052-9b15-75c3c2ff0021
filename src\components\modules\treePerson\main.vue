<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :width="width"
    :append-to-body="true"
    @close="cancel"
  >
    <div class="dialog_con" v-loading="initLoading">
      <div class="tree_box">
        <div class="search_form">
          <el-input
            placeholder="请输入部门名称"
            clearable
            v-model="queryParams.departmentName"
          >
          </el-input>
          <el-button icon="el-icon-refresh" @click="resetTree">重置</el-button>
        </div>
        <el-tree
          class="filter-tree scroll_bar"
          :data="treeData"
          :expand-on-click-node="false"
          accordion
          :props="defaultProps"
          @node-click="nodeClick"
          :filter-node-method="filterNode"
          ref="tree"
        >
        </el-tree>
      </div>

      <div class="table_box">
        <div class="search_form">
          <el-row type="flex" :gutter="6">
            <el-col :span="10">
              <el-input
                v-model="queryParams.query"
                class="format_option"
                size="small"
                placeholder="请输入姓名/工号"
                clearable
              ></el-input>
            </el-col>

            <el-col :span="10">
              <div class="btn_box">
                <el-button
                  size="small"
                  class="btn search_btn"
                  icon="el-icon-search"
                  type="primary"
                  @click="search(false)"
                  >搜索</el-button
                >
                <el-button
                  size="small"
                  class="btn reset_btn"
                  icon="el-icon-refresh"
                  @click="search(true)"
                  >重置</el-button
                >
              </div>
            </el-col>
          </el-row>
        </div>

        <el-table
          :data="tableData"
          ref="multipleTable"
          v-loading="tableLoading"
          row-key="id"
          :highlight-current-row="highlightCurrentRow"
          @current-change="singleElection"
          @row-dblclick="dbSingleElection"
          @select-all="handleSelectionChange"
          @select="handleSelectionChange"
          style="width: 100%"
          height="100%"
        >
          <el-table-column
            v-if="highlightCurrentRow"
            type="radio"
            width="30"
            align="center"
          >
            <template slot-scope="{ row }">
              <el-radio
                class="radio"
                v-model="selectData"
                :label="row"
                :key="row.id"
              ></el-radio>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            type="selection"
            width="55"
            :reserve-selection="true"
            align="center"
          >
          </el-table-column>
          <el-table-column
            v-for="(item, index) in config.tableColumn"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            align="center"
            :width="item.width"
          >
            <template slot-scope="{row}">    
              <el-tag v-if="item.tabStatus" :type="row[item.prop] | checkTargetVal(item.options)">{{row[item.prop] | checkDic(item.dicVal)}}</el-tag>
              <div v-else>{{row[item.prop]}}</div>
            </template>
          </el-table-column>
        </el-table>

        <div class="selected_person scroll_bar" v-if="!highlightCurrentRow">
          <el-tag
            v-for="(item, index) in selectData"
            :key="index"
            closable
            @close="closeTab(item)"
          >
            {{ item.name }}
          </el-tag>
        </div>

        <div class="page_box">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentPage"
            @prev-click="handlePage"
            @next-click="handlePage"
            :current-page="page.pageNum"
            :pager-count="5"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="10"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel()">取 消</el-button>
      <el-button type="primary" @click="confirm()">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { deptTree } from "@/api/HBP/organizationChart/organizationalMan";
import { listUser } from "@/api/HBP/authorityMan/userMan";
export default {
  props: {
    // 弹框标题
    title: {
      type: String,
      default() {
        return "候选人员";
      },
    },
    // false多选true单选，默认多选
    highlightCurrentRow: {
      type: Boolean,
      default() {
        return false;
      },
    },
    // 弹框宽度
    width: {
      type: String,
      default() {
        return "70%";
      },
    },
    // tree的规则（必传）
    defaultProps: {
      type: Object,
      required: true,
    },
    // 配置（必传）
    config:{
      type: Object,
      required: true,
    },
    //服务部回款项目/服务项目菜单所需参数
    isAdd:{
      type: Boolean,
      default() {
        return false;
      },
    },
    addPayProjectVisit:{
      type: Boolean,
      default() {
        return false;
      },
    },
    form:{
      type: Object,
      default() {
        return {};
      },
    },
  },
  filter:{
    checkTargetVal(attr,obj){
      if(attr === '' || attr === null || attr === undefined)return '';
      let targetObj = obj.find((item)=>{
          return item.key === attr
      })
      return targetObj.label
    }
  },
  watch: {
    "queryParams.departmentName"(val) {
      this.$refs.tree.filter(val);
    },
    // 监听选中的值将选中的值和table中勾选的数据绑定
    selectData(newVal) {
      if (!this.highlightCurrentRow) {
        if(newVal.length === 0){
          this.$nextTick(()=>{
            this.$refs.multipleTable.clearSelection()
          })
        }
        this.tableData.forEach((item) => {
          let flag = false;
          newVal.forEach((sItem) => {
            if (item.id === sItem.id) {
              flag = true;
            }
          });
          if (!flag) {
            this.$refs.multipleTable.toggleRowSelection(item, false);
          }
        });
      }
    },
  },
  data() {
    return {
      dialogVisible: false,
      tableLoading: false,
      initLoading: false,
      treeData: [],
      tableData: [],
      selectData: [],
      rowData:null,
      queryParams: {
        deptId: "",
        departmentName: "",
        query: "",
      },
      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      //服务部回款项目/服务项目菜单所需参数
      visitCompanyLeader:[],
      visitCompanyLeaderName:'',
      visitDeptLeader:[],
      visitCompanyLeaderName:'',
    };
  },
  methods: {
    init(rowData,addPayProjectVisitType) {//服务部回款项目/服务项目菜单所需参数 addPayProjectVisitType
      this.dialogVisible = true;
      this.rowData = rowData
      // this.selectData = selectData ? JSON.parse(JSON.stringify(selectData)) : []
      this.selectData = [];
      //服务部回款项目/服务项目菜单所需逻辑 start
      if(this.addPayProjectVisit && this.isAdd){
        this.visitCompanyLeader = this.form.visitDateAndPersonList[0].visitCompanyLeader?this.form.visitDateAndPersonList[0].visitCompanyLeader:[]
        this.visitDeptLeader = this.form.visitDateAndPersonList[0].visitDeptLeader?this.form.visitDateAndPersonList[0].visitDeptLeader:[]
        this.visitCompanyLeaderName = this.form.visitDateAndPersonList[0].visitCompanyLeaderName?this.form.visitDateAndPersonList[0].visitCompanyLeaderName.split(','):[]
        this.visitDeptLeaderName = this.form.visitDateAndPersonList[0].visitDeptLeaderName?this.form.visitDateAndPersonList[0].visitDeptLeaderName.split(','):[]
      }else if(this.addPayProjectVisit && !this.isAdd){
        this.visitCompanyLeader = this.form.visitCompanyLeader?this.form.visitCompanyLeader:[]
        this.visitDeptLeader = this.form.visitDeptLeader?this.form.visitDeptLeader:[]
        this.visitCompanyLeaderName = this.form.visitCompanyLeaderName?this.form.visitCompanyLeaderName.split(','):[]
        this.visitDeptLeaderName = this.form.visitDeptLeaderName?this.form.visitDeptLeaderName.split(','):[]
      }
      let arr = []
      if(addPayProjectVisitType && addPayProjectVisitType == 'visitCompanyLeaderName'){
        this.visitCompanyLeader.forEach((item,index)=>{
          let obj = {id:item,name:this.visitCompanyLeaderName[index]}
          arr.push(obj)
        })
      }else if(addPayProjectVisitType && addPayProjectVisitType == 'visitDeptLeaderName'){
        this.visitDeptLeader.forEach((item,index)=>{
          let obj = {id:item,name:this.visitDeptLeaderName[index]}
          arr.push(obj)
        })
      }
      this.selectData = JSON.parse(JSON.stringify(arr))
      //服务部回款项目/服务项目菜单所需逻辑 end
      this.queryParams = {
        deptId: "",
        departmentName: "",
        query: "",
      };
      this.page.pageNum = 1;
      this.initLoadData();
    },
    resetTree() {
      this.queryParams = {
        deptId: "",
        departmentName: "",
        query: "",
      };
      this.initLoadData();
    },
    search(reset) {
      if (reset) {
        this.queryParams.query = "";
        this.$nextTick(() => {
          this.$refs.multipleTable.clearSelection();
          this.selectData = [];
        });
      }
      this.page.pageNum = 1;
      this.loadTable();
    },
    async initLoadData() {
      this.initLoading = true;
      await this.deptTree();
      await this.loadTable();
      this.initLoading = false;
    },
    filterNode(value, data) {
      if (!value) return true;
      return data[this.defaultProps["label"]].indexOf(value) !== -1;
    },

    // 点击树节点
    nodeClick(currentNode) {
      this.queryParams.deptId = currentNode.id;
      this.loadTable();
    },

    // 获取左侧部门树
    async deptTree() {
      await deptTree()
        .then((res) => {
          this.treeData = res.data;
        })
        .catch(() => {
          this.initLoading = false;
        });
    },

    // 根据部门id获取人员
    async loadTable() {
      this.tableLoading = true;
      let params = {
        ...this.page,
        ...this.queryParams,
      };
      await listUser(params)
        .then((res) => {
          let resData = res.data;
          this.tableData = resData.records;

          this.tableData.forEach((item) => {
            let flag = false;
            if (!this.highlightCurrentRow) {
              this.selectData.forEach((sItem) => {
                if (item.id === sItem.id) {
                  flag = true;
                }
              });
              if (!flag) {
                this.$nextTick(() => {
                  this.$refs.multipleTable.toggleRowSelection(item, false);
                });
              }else{//服务部回款项目/服务项目菜单所需逻辑 start
                this.$nextTick(() => {
                  this.$refs.multipleTable.toggleRowSelection(item, true);
                });
              }//服务部回款项目/服务项目菜单所需逻辑 end
            } else {
              if (item.id === this.selectData) {
                this.$nextTick(() => {
                  this.$refs.multipleTable.toggleRowSelection(item, true);
                });
              }
            }
          });
          this.page.total = resData.total;
          this.tableLoading = false;
        }).catch(() => {
          this.tableLoading = false;
        });
    },

    // 多选
    handleSelectionChange(selectData) {
      this.selectData = JSON.parse(JSON.stringify(selectData));
      console.log('selectData',this.selectData)
    },

    // 单击选中行（只适用于单选）
    singleElection(row) {
      if (this.highlightCurrentRow) {
        this.selectData = row;
      }
    },

    // 双击（只适用于单选）
    dbSingleElection(row) {
      if (this.highlightCurrentRow) {
        this.selectData = row;
        this.confirm();
      }
    },

    // 关闭tab
    closeTab(tabItem) {
      let selectData = this.selectData.filter((item) => {
        return tabItem.id !== item.id;
      });
      this.selectData = selectData;
    },

    // 确定
    confirm() {
      if(!this.addPayProjectVisit){ // 服务部 时不做此判断
        if (Array.isArray(this.selectData) && this.selectData.length === 0) {
          this.$message({
            type: "warning",
            message: "请选择数据",
            duration: 1500,
          });
          return;
        }
      }
      this.$emit("selectData", this.selectData,this.rowData);
      this.$refs.multipleTable.clearSelection();
      this.dialogVisible = false;
    },

    // 取消
    cancel() {
      this.$emit("cancel");
      this.$refs.multipleTable.clearSelection();
      this.dialogVisible = false;
    },

    // 更改每页显示条数
    handleSizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.loadTable();
    },

    // 选择页数
    handleCurrentPage(currentPage) {
      this.page.pageNum = currentPage;
      this.loadTable();
    },

    // 点击上一页/下一页
    handlePage(currentPage) {
      this.page.pageNum = currentPage;
      this.loadTable();
    },
  },
};
</script>
<style lang="less" scoped>
.dialog_con {
  display: flex;
  height: 50vh;
  .tree_box {
    width: 300px;
    height: 100%;
    .search_form {
      margin-bottom: 0;
      display: flex;
      .el-button {
        height: 32px;
        margin-left: 5px;
      }
    }
    .el-input {
      margin-bottom: 10px;
    }
    /deep/.el-tree {
      background: none;
      height: calc(100% - 42px);
      overflow-y: scroll;

      // .el-tree-node:focus>.el-tree-node__content {
      //     background:none;
      // }
      // .el-tree-node__content:hover {
      //     background:none;
      // }
      .el-tree-node__content {
        height: 32px;
      }
      .is-current {
        > .el-tree-node__content {
          transition: all 0.15s ease;
          background: #bcd4ed;
          border-left: 2px solid #409eff;
          color: #2664d3;
        }
      }
    }
  }

  .table_box {
    width: calc(100% - 300px);
    height: 100%;
    padding-left: 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .el-table {
      min-height: auto;
      // height: calc(100% - 189px);
      flex: 1;
      /deep/.el-table__body-wrapper {
        .el-table__row {
          cursor: pointer;
        }
      }
      /deep/.el-table__body-wrapper::-webkit-scrollbar{
        max-width: 5px;
        height: 2px;
        background: none;
        border-radius: 10px;
      }
      /deep/.el-table__body-wrapper::-webkit-scrollbar-thumb {
          max-width: 5px;
          background-color: #C1C1C1;
          border-radius: 10px;
      }
    }

    .selected_person {
      max-height: 100px;
      margin-bottom: 10px;
      overflow-y: scroll;
      .el-tag {
        margin-left: 5px;
        margin-top: 5px;
      }
    }

    .page_box {
      height: 32px;
    }
  }
}
</style>
