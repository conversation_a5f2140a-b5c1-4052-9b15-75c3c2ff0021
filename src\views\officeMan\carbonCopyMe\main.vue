<template>
    <div class="to_do_tasksMian">

      <div class="search_form">
        <el-row type="flex" :gutter="6">
          <el-col :span="4">
            <el-input
              v-model="searchForm.processName"
              class="format_option"
              size="small"
              placeholder="请输入流程名称"
              clearable
            ></el-input>
          </el-col>

          <el-col :span="4">
            <el-input
              v-model="searchForm.originatorName"
              class="format_option"
              size="small"
              placeholder="请输入发起人"
              clearable
            ></el-input>
          </el-col>

          <el-col :span="4">
            <div class="btn_box">
              <el-button @click="search(false)" size="small" class="btn search_btn" icon="el-icon-search" type="primary" >搜索</el-button>
              <el-button
                @click="search(true)"
                size="small"
                class="btn reset_btn"
                icon="el-icon-refresh"
                >重置</el-button
              >
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- <div class="operation_btn">
        <el-button
          class="btn add_btn"
          size="small"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd()"
          >新增</el-button
        >
      </div> -->

      <div class="table_box">
          <el-table
          v-loading="loading"
          :data="tableData"
          row-key="id"
          style="width: 100%">

              <el-table-column
                  type="index"
                  label="序号"
                  width="50"
                  align="center">
              </el-table-column>


              <el-table-column
                  v-for="(item,index) in tableColumn"
                  :key="index"
                  :prop="item.prop"
                  :label="item.label"
                  align="center"
                  :width="item.width">
                  <template slot-scope="{row}">
                    <div v-if="item.depTabStatus" class="dep_tab">
                        <span class="text"></span>
                        <!-- <el-tag :type="row.status === 0 ? 'danger' : 'default'">{{ row[item.prop] }}</el-tag> -->
                    </div>
                    <div v-else-if="item.checkTime">
                        <i class="el-icon-time"></i>
                        {{ row[item.prop] | dateTimeFormat }}
                    </div>
                    <div v-else>{{row[item.prop]}}</div>
                  </template>
              </el-table-column>
              <el-table-column
                  fixed="right"
                  label="操作"
                  align="center"
                  width="200">
                  <template slot-scope="{row}">
                      <div class="handle_btn">
                        <el-button type="text" @click="handleInfo(row)" size="small" icon="el-icon-edit-outline">详情</el-button>
                      </div>
                  </template>
              </el-table-column>
          </el-table>

          <div class="page_box">
              <el-pagination
                  background
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentPage"
                  :current-page="page.pageNum"
                  :pager-count="5"
                  :page-sizes="[10, 20,30,40]"
                  :page-size="10"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="page.total">
              </el-pagination>
          </div>
      </div>
      <carbon-copy-me-info ref="carbonCopyMeInfo"></carbon-copy-me-info>
    </div>

  </template>

<script>
import carbonCopyMeInfo from './components/carbonCopyMeInfo'
import {finishedList} from '@/api/officeMan/carbonCopyMe'
export default {
components:{carbonCopyMeInfo},
data() {
    return {
    loading: false,
    statusOptions:[],
    tableData:[],
    categoryOptions: [],
    searchForm: {
        processName:'',
        originatorName:''
    },

    page:{
        pageSize:10,
        pageNum:1,
        total:0
    },


    tableColumn:[
        {
          prop:'id',
          label:'抄送编号',
          width:'300'
        },
        {
          prop:'title',
          label:'标题',
          width:'200'
        },
        {
          prop:'processName',
          label:'流程名称',
          width:'300'
        },
        {
          prop:'originatorName',
          label:'发起人',
          width:''
        },
        {
          prop:'createTime',
          label:'创建时间',
          width:'200'
        },
    ]
    };
},
created() {
    this.loadData();
},
methods: {
    hidDialog() {
    this.loadData();
    },

    // 搜索/重置
    search(reset) {
    if (reset) {
        this.searchForm = {
          processName:'',
          originatorName:''
        };
    }
    this.page.pageNum = 1;
      this.loadData();
    },

    // 详情
    handleInfo(row){
      this.$refs.carbonCopyMeInfo.init(row)
    },

    loadData(){
        let params = {
            ...this.searchForm,
            ...this.page
        }
        this.loading = true
        finishedList(params).then((res)=>{
            let resData = res.data
            this.tableData = resData.records
            this.page.total = resData.total
            this.loading = false
        }).catch(()=>{
            this.loading = false
        })
    },

    // 更改每页显示条数
    handleSizeChange(pageSize){
        this.page.pageSize = pageSize
        this.loadData()
    },

    // 选择页数/点击上一页/下一页
    handleCurrentPage(currentPage){
        this.page.pageNum = currentPage
        this.loadData()
    }

}
};
</script>
<style lang="less">
.to_do_tasksMian {
    padding: 16px 12px 0;
}
</style>
