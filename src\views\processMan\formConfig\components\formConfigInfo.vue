<template>
    <el-dialog title="表单配置详情" :visible.sync="formConfOpen" width="40%" append-to-body>
      <div class="test-form">
        <parser :key="new Date().getTime()"  :form-conf="formConf" />
      </div>
    </el-dialog>
</template>
<script>
import Parser from '@/utils/generator/parser'
import Html from '@/utils/generator/html'
export default {
    components:{Parser,Html},
    data(){
        return {
            formConfOpen:false,
            formConf:null,
        }
    },
    methods:{
        init(row){
            this.formConfOpen = true
          console.log(" JSON.parse(row.content)", JSON.parse(row.content))
            this.formConf = JSON.parse(row.content)
        }
    }
}
</script>
<style lang="less" scoped>

</style>
