import request from '@/utils/request'
// 整部套列表
export function getOrderPage(param) {
  return request({
    url: '/back-server/implementation/getOrderPage',
    method: 'get',
    params: param,
  })
}
// 部套列表
export function getListPage(param) {
    return request({
      url: '/back-server/nodeRecord/getListPage',
      method: 'get',
      params: param,
    })
  }

  // 启动节点管控
export function batchAddNodeList(param) {
  return request({
    url: '/back-server/outsourcingOrder/batchAddNodeList',
    method: 'post',
    data: param,
  })
}

//导出
export function exportData(query) {
  return request({
    url: '/back-server/outsourcingOrder/exportExcelTemplate',
    method: 'get',
    params: query,
    responseType: 'blob', // important
  })
}

//查看节点
export function nodeInfo(orderId) {
  return request({
    url: `/back-server/outsourcingOrder/findNodeListByOrderId?orderId=${orderId}`,
    method: 'get',
  })
}

// 修改节点
export function batchUpdateNode(param) {
  return request({
    url: '/back-server/outsourcingOrder/batchUpdateNode',
    method: 'post',
    data: param,
  })
}

