<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogFormVisible"
      append-to-body
      width="600px"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="80px"
        v-if="rejectState == 0"
      >
        <el-form-item
          label="驳回原因"
          prop="rejectReason"
          :rules="[{ required: true, message: '请输入驳回原因' }]"
        >
          <el-input
            type="textarea"
            :rows="2"
            placeholder="请输入内容"
            v-model="form.rejectReason"
          >
          </el-input>
        </el-form-item>
      </el-form>

      <div v-else>是否确认该环节审批通过？</div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading"
          >确 定</el-button
        >
        <el-button @click="dialogFormVisible = false" :loading="btnLoading"
          >取 消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { auditHtc } from "@/api/smallTestExperiment/experimentPlan.js";
export default {
  data() {
    return {
      personShow: false,
      title: "驳回",
      formLabelWidth: "120px",
      btnLoading: false,
      dialogFormVisible: false,
      form: {
        rejectReason: "",
      },
      rejectState: null,
      rowData: {},
    };
  },
  methods: {
    //重置
    rest() {},
    //初始化
    init(val, rejectState) {
      this.dialogFormVisible = true;
      this.rowData = val;
      this.rejectState = rejectState;
      this.title = rejectState == 1 ? "通过" : "驳回";
      this.form.rejectReason = "";
      if (this.$refs.form) {
        this.$nextTick(() => {
          this.$refs.form.clearValidate();
        });
      }
    },
    // 确定
    confirm() {
      if (this.rejectState == 0) {
        this.$refs["form"].validate((valid) => {
          if (valid) {
            this.btnLoading = true;
            auditHtc({
              ...this.rowData,
              ...this.form,
              rejectState: this.rejectState,
            })
              .then((re) => {
                this.$modal.msgSuccess("操作成功");
                this.btnLoading = false;
                this.dialogFormVisible = false;
                this.$parent.getDetail();
              })
              .catch(() => {
                this.btnLoading = false;
              });
          }
        });
      } else {
        this.btnLoading = true;
        auditHtc({
          ...this.rowData,
          ...this.form,
          rejectState: this.rejectState,
        })
          .then((re) => {
            this.$modal.msgSuccess("操作成功");
            this.btnLoading = false;
            this.dialogFormVisible = false;
            this.$parent.getDetail();
          })
          .catch(() => {
            this.btnLoading = false;
          });
      }
    },
  },
};
</script>
<style scoped lang="scss">
.btn-box {
  margin-bottom: 8px;
  padding-left: 36px;
  display: flex;
  align-items: center;
  .btn-label {
    width: 85px;
  }
}
</style>
