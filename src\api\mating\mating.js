import request from '@/utils/request'
// 常用语分页查询列表
export function getListPage(param) {
  return request({
    url: '/user-server/commonTerms/getListPage',
    method: 'get',
    params: param,
  })
}
// 新增常用语
export function addCommon(param) {
  return request({
    url: '/user-server/commonTerms/add',
    method: 'post',
    params: param,
  })
}
// 修改常用语
export function updateCommon(param) {
  return request({
    url: '/user-server/commonTerms/update',
    method: 'post',
    params: param,
  })
}
// 删除、批量删除常用语
export function deletesCommon(param) {
  return request({
    url: '/user-server/commonTerms/deletes?ids=' + param,
    method: 'delete',
  })
}

//机组日志列表查询
export function findAllPage(param) {
  return request({
    url: '/back-server/unitLog/findAllPage',
    method: 'get',
    params: param,
  })
}
//机组日志新增
export function addFindAllPage(data) {
  return request({
    url: '/back-server/unitLog/add',
    method: 'post',
    params: data
  })
}