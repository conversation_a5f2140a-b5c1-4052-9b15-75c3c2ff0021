<template>
  <div class="app-container">
    <div class="title">正在为 <span>系统管理员</span> 分配授权人员：</div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="用户名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="openSelectUser"
          >添加授权人员</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-circle-close"
          size="mini"
          :disabled="multiple"
          @click="cancelAuthUserAll"
          >批量取消授权</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-close"
          size="mini"
          @click="handleClose"
          >关闭</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="userList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column
        label="人员姓名"
        prop="name"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="邮箱"
        prop="email"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="手机"
        prop="phone"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="状态" prop="enable">
        <template slot-scope="scope">
           <span class="zhuang">{{getEnable(scope.row.enable)}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建日期"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="cancelAuthUser(scope.row)"
            >取消授权</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />
    <select-user ref="select" :id="queryParams.id" @ok="handleQuery" />
  </div>
</template>

<script>
import { getUserListByBackRoleList,cancelAuth} from "@/api/system/appUser";
import { allocatedUserList, authUserCancel, authUserCancelAll } from "@/api/system/role";
import selectUser from "./selectUser";

export default {
  name: "AuthUser",
  components: { selectUser },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中用户组
      userIds: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        id: undefined,
        name: undefined,
        phone: undefined
      }
    };
  },
  created() {
    const id = this.$route.params && this.$route.params.roleId;
    if (id) {
      this.queryParams.id = id;
      this.getList();
    }
  },
  methods: {
       getEnable(enable){
     switch(enable){
      case 0:
        return '停用'
        break;
        case 1:
          return '正常'
     }
    },
    /** 查询授权用户列表 */
    getList() {
      this.loading = true;
      getUserListByBackRoleList(this.queryParams).then(response => {
          this.userList = response.data;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 返回按钮
    handleClose() {
      this.$router.push("/systems/systems/SystemRole/index");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.userIds = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 打开授权用户表弹窗 */
    openSelectUser() {
      this.$refs.select.show();
    },
    /** 取消授权按钮操作 */
    cancelAuthUser(row) {
       let appUserIds=row.id
      const roleId = this.queryParams.id;
      this.$modal.confirm('确认要取消该用户"' + row.name + '"角色吗？').then(function() {
        return cancelAuth({ appUserIds, roleId});
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("取消授权成功");
      }).catch(() => {});
    },
    /** 批量取消授权按钮操作 */
    cancelAuthUserAll(row) {
      const roleId = this.queryParams.id;
    let appUserIds=this.userIds.join(",");
      this.$modal.confirm('是否取消选中用户授权数据项？').then(function() {
        return cancelAuth({ roleId,appUserIds });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("取消授权成功");
      }).catch(() => {});
    }
  }
};
</script>
<style lang="scss" scoped>
.title{
font-size: 14px;
font-weight: 400;
color: #999999;
margin-bottom: 24px;
}
.title span{
  color: #666666;
}
</style>