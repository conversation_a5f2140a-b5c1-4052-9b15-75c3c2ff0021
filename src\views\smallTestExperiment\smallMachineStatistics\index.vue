<template>
  <div class="main">
    <el-form
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="日期" prop="query">
        <el-date-picker
          v-model="time"
          type="daterange"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery(false)"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="handleQuery(true)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div class="chart-container" v-loading="loading">
      <div class="draw-pic-container">
        <p class="title">检验实验计划实时统计</p>
        <div class="draw-pic" id="draw-pic"></div>
      </div>
      <div class="draw-tree-container">
        <div class="progress-title">
          <p class="title">厂商检验实验计划实时统计</p>
        </div>
          <div class="machine-tree" id="machine-tree"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { planStatistics } from "@/api/smallTestExperiment/smallMachineStatistics.js";
export default {
  components: { },

  data() {
    return {
      loading:false,
      supplierList:[],
      time:[],
    };
  },

  mounted(){
    this.$nextTick(()=>{
      // this.loadData()
      this.initChartPic()
      let treeData = {
          labelData:[],
          finishData:[],
          numData:[]
        }
      this.initMachineTree(treeData)
    })
  },

  methods: {
    handleQuery(flag){
      if(flag){
        this.time = []
        this.$nextTick(()=>{
          this.initChartPic()
          let treeData = {
            labelData:[],
            finishData:[],
            numData:[]
          }
          this.initMachineTree(treeData)
        })
      }else{
        this.loadData()
      }
    },

    loadData(){
      if(!this.time || this.time.length==0 ){
        this.$message(
          {
            type:'warning',
            message:'请选择日期',
            duration:2000,
          }
        )
        return
      }
      this.loading = true
      let time = this.time && this.time.length>0 ? this.time : []
      let params = {
        startTime:time[0] || '',
        endTime:time[1] || ''
      }
      planStatistics(params).then((res)=>{
        this.loading = false
        let resData = res.data
        let supplierList = resData.supplierList
        this.supplierList = supplierList
        let [labelData,finishData,numData] = [[],[],[]]
        supplierList.forEach((item)=>{
          labelData.push(item.supplierName)
          finishData.push(item.finishNum)
          numData.push(item.total)
        })
        
        let treeData = {
          labelData,
          finishData,
          numData
        }
        let picData = [
          {value:resData.finishNum,name:'已完成'},
          {value:resData.unFinishNum,name:'进行中'},
        ]
        this.initChartPic(picData)
        this.initMachineTree(treeData)
      }).catch(()=>{
        this.loading = false
      })
    },

    initChartPic(picData=[]){
      let chartDom = document.getElementById('draw-pic');
      let myChart = this.$echarts.init(chartDom);
      let options = {
        tooltip: {
          trigger: "item",
        },
        color:['#F4BC43', '#73A0FB'],
        series: [
          {
            type: "pie",
            radius: ["30%", "60%"],
            
            label: {
              normal: {
                formatter:'{b|{b}}\n{c|{c}}（{d|{d}}%）',
                  fontSize:14,
                  rich: {
                    b: {
                      color: "#333",
                      fontSize:14,
                      lineHeight: 30,
                      align: "center",
                    },
                    c: {
                      color: "#F4BC43",
                      fontSize: 14,
                      lineHeight: 20,
                    },
                    d: {
                      fontSize:14,
                      lineHeight: 20,
                    },
                  },
                }
            },
            data: picData,

            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      }

      myChart.setOption(options);

    },

    // 树状图
    initMachineTree(treeData){
        let chartDom = document.getElementById('machine-tree');
        let height = treeData.finishData.length*50
        chartDom.style.height = height+'px'
        let myChart = this.$echarts.init(chartDom);
        let max = Math.max(...treeData.numData)
        let option = {
            legend: {
                icon:'rect',
                selectedMode:false,
            },
            grid: {
              height:height+'px',
              max:max+100,
              top:'40px',
              left: '30%',
              right: '13%',
              bottom: '14%',
            },
            xAxis: {
                type: 'value',
                show:false,
                splitNumber:100,
                axisTick:{
                    show:false
                },
            },
            yAxis: {
                type: 'category',
                splitLine:{
                    show:false,
                },
                axisLine:{
                    show:false
                },
                axisTick:{
                    show:false
                },
                data: treeData.labelData
            },
            series: [
                {
                    name: '完成数量',
                    type: 'bar',
                    barWidth:20,
                    label: {
                        show: true, // 展示文字
                        position: 'inside', // 文字上方显示
                        color: '#000', // 文字颜色
                        formatter: '{c}' // {c}数据值
                    },
                    itemStyle:{
                        normal:{
                            showBackground:true,
                            color:'#F4BC43',
                            barBorderRadius:[10,10,10,10]
                        },
                    },
                    
                    data: treeData.finishData
                },
                {
                    name: '小机检验试验数量',
                    type: 'bar',
                    z:'-1',
                    barGap:'-100%',
                    label: {
                        show: true, // 展示文字
                        position: 'right', // 文字上方显示
                        color: '#000', // 文字颜色
                        formatter: '{c}' // {c}数据值
                    },
                    itemStyle:{
                        normal:{
                            showBackground:true,
                            color:new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                {
                                    offset: 0,
                                    color: "#73A0FA"
                                }, 
                                {
                                    offset: 1,
                                    color: "#4172FF"
                                }
                            ]),
                            barBorderRadius:[10,10,10,10]
                        },
                    },
                    barWidth:20,
                    data: treeData.numData
                },
            ]
        }
        myChart.setOption(option)
        myChart.resize()
    },

  },
};
</script>

<style scoped lang="scss">
.title {
  text-align:center;
  padding:10px 0;
  box-sizing: border-box;
}
.num-color {
  background:#4172FF;
}
.finish-color {
  background:#F4BC43;
}
.modles-container {
  margin:10px 0;
  display:flex;
  font-size:14px;
  justify-content: center;
  .modles {
    display:flex;
    align-items:center;
    margin-left:100px;
    .model {
      width:25px;
      height:15px;
      margin-right:10px;
    }
  }
  .modles:first-of-type {
    margin-left:0;
  }
}
.main {
  padding: 16px 12px 0;
  box-sizing: border-box;

  .chart-container {
    height:100%;
    display:flex;
    .draw-pic-container {
     width:40%;
     .draw-pic {
        min-height:300px;
      }
    }
    .draw-tree-container {
      width:60%;
      .machine-tree {
        width:100%;
      }
      
    }
  }
}
</style>
