import qs from 'qs'
import request from '@/utils/request'

// 新增
export function addModel(query) {
    return request({
        url: `/flowable/workflow/model`,
        method:'post',
        data:query
    });
}

// 编辑
export function editModel(query) {
    return request({
        url: `/flowable/workflow/model`,
        method:'put',
        data:query
    });
}


// 流程模型列表
export function queryModelList(query) {
    return request({
        url: `/flowable/workflow/model/list`,
        method:'get',
        params:query
    });
}

// 删除
export function delModel(modelId) {
    return request({
        url: `/flowable/workflow/model/${modelId}`,
        method:'delete',
    });
}

// 读取xml文件
export function getBpmnXml(modelId) {
    return request({
        url: `/flowable/workflow/model/bpmnXml/${modelId}`,
        method:'get'
    });
}

// 保存流程模型
export function saveModel(query) {
    return request({
        url: `/flowable/workflow/model/save`,
        method:'post',
        headers:{
            'Content-Type':'application/x-www-form-urlencoded'
        },
        data:qs.stringify(query)
    });
}

// 部署流程模型
export function deployModel(modelId) {
    return request({
        url: `/flowable/workflow/model/deploy?modelId=${modelId}`,
        method:'post',
    });
}


// 获取历史模型
export function historyList(query) {
    return request({
        url: `/flowable/workflow/model/historyList`,
        method:'get',
        params:query
    });
}


// 设为最新
export function latest(modelId) {
    return request({
        url: `/flowable/workflow/model/latest?modelId=${modelId}`,
        method:'post',
    });
}


