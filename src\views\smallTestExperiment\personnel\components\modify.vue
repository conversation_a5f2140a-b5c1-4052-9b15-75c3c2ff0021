<template>
    <div>
      <el-dialog
        title="修改"
        :visible.sync="dialogFormVisible"
        append-to-body
        width="840px"
      >
        <el-form
          :model="dialogQueryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="搜索" prop="keyword">
            <el-input
              v-model="dialogQueryParams.keyword"
              placeholder="请输入姓名"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="search"
              >搜索</el-button
            >
          </el-form-item>
        </el-form>
  
        <el-table
          row-key="id"
          ref="elTable"
          v-loading="dialogLoading"
          :data="dialogTableList"
        > 
          <el-table-column
            v-if="this.rowData"
            type="radio"
            width="30"
            align="center"
          >
            <template slot-scope="{ row }">
              <el-radio
                class="radio"
                v-model="selectedList"
                :label="row"
                :key="row.id"
              ></el-radio>
            </template>
          </el-table-column>
  
          <el-table-column label="序号" type="index" />
          <el-table-column label="人员姓名" prop="name" />
          <el-table-column label="所属部门" prop="alarmStatus" />
          <el-table-column label="手机号" prop="phone" />
          <el-table-column label="邮箱" prop="email" />
        </el-table>
  
        <pagination
          v-show="dialogTotal > 0"
          :total="dialogTotal"
          :page.sync="dialogQueryParams.page"
          :limit.sync="dialogQueryParams.size"
          @pagination="getListUser"
        />
  
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="confirm">确 定</el-button>
          <el-button @click="dialogFormVisible = false">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  <script>
  import { listUser } from "@/api/system/user.js";
  import { modifySmUser } from "@/api/smallTestExperiment/personnel.js";
  export default {
    data() {
      return {
        personShow: false,
        formLabelWidth: "120px",
        dialogLoading: false,
        btnLoading: false,
        dialogFormVisible: false,
        dialogQueryParams: {
          keyword: "",
          page: 1,
          size: 10,
        },
        rowData:null,
        dialogTotal: 0,
        dialogTableList: [],
        selectedList: null,
      };
    },

    methods: {
      //重置
      rest() {},
      search() {
        this.dialogQueryParams.page = 1;
        this.getListUser();
      },
      //初始化
      init(row) {
        this.rowData = row
        this.dialogFormVisible = true;
        this.selectedList = null;
        this.getListUser();
      },
  
      getListUser() {
        this.dialogLoading = true;
        listUser(this.dialogQueryParams).then((res) => {
          this.dialogLoading = false;
          this.dialogTableList = res.data;
          this.dialogTotal = res.total;
        });
      },

      // 确定
      confirm() {
        if(!this.selectedList){
            this.$message({
              type:'warning',
              message:'请选择数据',
              duration:2000
            })
            return
        }
        let params = {
            name:this.selectedList.name,
            userId:this.selectedList.id,
            id:this.rowData.id
        }
        modifySmUser(params).then((res)=>{
            this.$modal.msgSuccess("操作成功");
            this.dialogFormVisible = false;
            this.$parent.getList();
        })
      },
    },
  };
  </script>
  <style scoped lang="scss">
  .btn-box {
    margin-bottom: 8px;
    padding-left: 36px;
    display: flex;
    align-items: center;
    .btn-label {
      width: 85px;
    }
  }
  </style>
  