import request from '@/utils/request'
// 新增供应商
export function addSupplier(data) {
  return request({
    url: '/user-server/supplier/add',
    method: 'post',
    data,
  })
}
// 修改供应商
export function updateSupplier(data) {
  return request({
    url: '/user-server/supplier/update',
    method: 'post',
    data,
  })
}
// 删除供应商
export function deleteSupplier(query) {
  return request({
    url: `/user-server/supplier/delete`,
    method: 'get',
    params:query,
  })
}
// 批量删除供应商
export function deletesSupplier(query) {
  return request({
    url: `/user-server/supplier/deletes?ids=${query}`,
    method: 'get',
  })
}
// 查询供应商列表
export function getListPage(query) {
  return request({
    url: url+'/user-server/supplier/getListPage',
    method: 'get',
    params: query,
    
  })
}
