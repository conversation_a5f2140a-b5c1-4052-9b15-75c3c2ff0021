<template>
    <div class="process_model">
      <div class="search_form">
        <el-row type="flex" :gutter="6">
          <el-col :span="4">
            <el-input
              v-model="searchForm.modelKey"
              class="format_option"
              size="small"
              placeholder="请输入模型标识"
              clearable
            ></el-input>
          </el-col>
          <el-col :span="4">
            <el-input
              v-model="searchForm.modelName"
              class="format_option"
              size="small"
              placeholder="请输入模型名称"
              clearable
            ></el-input>
          </el-col>
          <el-col :span="3">
            <el-select v-model="searchForm.category" size="small" placeholder="流程分类" clearable>
                <el-option
                v-for="item in typeOptions"
                :key="item.id"
                :label="item.categoryName"
                :value="item.id"
                >
                </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <div class="btn_box">
              <el-button @click="search(false)" size="small" class="btn search_btn" icon="el-icon-search" type="primary" >搜索</el-button>
              <el-button
                @click="search(true)"
                size="small"
                class="btn reset_btn"
                icon="el-icon-refresh"
                >重置</el-button
              >
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="operation_btn">
        <el-button
          class="btn add_btn"
          size="small"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd()"
          >新增</el-button
        >
      </div>

      <div class="table_box">
          <el-table
          v-loading="loading"
          :data="tableData"
          row-key="id"
          style="width: 100%">

              <el-table-column
                  type="index"
                  label="序号"
                  width="50"
                  align="center">
              </el-table-column>


              <el-table-column
                  v-for="(item,index) in tableColumn"
                  :key="index"
                  :prop="item.prop"
                  :label="item.label"
                  align="center"
                  :width="item.width">
                  <template slot-scope="{row}">
                      <el-link v-if="item.linkstatus" type="primary" @click="handleProcessView(row)">{{row[item.prop]}}</el-link>
                      <el-tag
                        v-else-if="item.versionStatus"
                        type="default"
                        >v{{ row[item.prop] }}</el-tag
                      >
                      <div v-else-if="item.checkTime">
                          <i class="el-icon-time"></i>
                          {{ row[item.prop] | dateTimeFormat }}
                      </div>
                      <div v-else>{{row[item.prop]}}</div>
                  </template>
              </el-table-column>
              <el-table-column
                  fixed="right"
                  label="操作"
                  align="center"
                  width="250">
                  <template slot-scope="{row}">
                      <div class="handle_btn">
                          <el-button type="text" @click="handleAdd(row)" size="small" icon="el-icon-edit">修改</el-button>

                          <el-button type="text" @click="handleDesign(row)" size="small" icon="el-icon-brush">设计</el-button>

                          <el-popconfirm
                            title="是否确定部署？"
                            @confirm="handleDeploy(row)"
                            >
                            <el-button type="text" slot="reference" size="small" icon="el-icon-video-play">部署</el-button>
                          </el-popconfirm>

                          <el-dropdown class="table_dropdown">
                            <el-button type="text" size="small" icon="el-icon-d-arrow-right">更多</el-button>
                              <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item
                                  icon="el-icon-view"
                                  @click.native="handleProcessView(row)"
                                >流程图</el-dropdown-item>
                                <el-dropdown-item
                                  icon="el-icon-price-tag"
                                  @click.native="handleHistory(row)"
                                >历史</el-dropdown-item>
                                <el-dropdown-item
                                  class="del"
                                  icon="el-icon-delete"
                                  @click.native="handleDel(row)"
                                >删除</el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>

                      </div>
                  </template>
              </el-table-column>
          </el-table>

          <div class="page_box">
              <el-pagination
                  background
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentPage"
                  :current-page="page.pageNum"
                  :pager-count="5"
                  :page-sizes="[10, 20,30,40]"
                  :page-size="10"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="page.total">
              </el-pagination>
          </div>
      </div>


      <add-process-model ref="addProcessModel" @hideDialog="hideDialog"></add-process-model>
      <flow-chart ref="flowChart"></flow-chart>
      <history-model ref="historyModel"></history-model>
      <design-drawing ref="designDrawing" @hideDialog="hideDialog"></design-drawing>
    </div>

  </template>

  <script>
  import addProcessModel from './components/addProcessModel.vue';
  import flowChart from './components/flowChart.vue';
  import historyModel from './components/historyModel.vue';
  import designDrawing from './components/designDrawing.vue'
  import { queryModelList,delModel,deployModel } from '@/api/processMan/processModel.js'
  import {processClassListAll} from '@/api/processMan/processClass.js'

  export default {
    components: {
      designDrawing,
      historyModel,
      flowChart,
      addProcessModel
    },
    data() {
      return {
        loading: false,
        typeOptions:[],
        tableData:[],
        searchForm:{
          category:'',
          modelKey:'',
          modelName:''
        },

        page:{
            pageSize:10,
            pageNum:1,
            total:0
        },

        // 表单校验
        rules: {
          modelKey: [
            { required: true, message: "模型标识不能为空", trigger: "blur" }
          ],
          modelName: [
            { required: true, message: "模型名称不能为空", trigger: "blur" }
          ],
          category: [
            { required: true, message: "请选择类型", trigger: "change" }
          ],
        },

        tableColumn:[
          {
            prop:'modelKey',
            label:'模型标识',
            width:'200'
          },
          {
            prop:'modelName',
            label:'模型名称',
            linkstatus:true,
            width:'200'
          },
          {
            prop:'categoryName',
            label:'流程分类',
            width:'150'
          },
          {
            prop:'version',
            label:'模型版本',
            versionStatus:true,
            width:'100'
          },
          {
            prop:'description',
            label:'描述',
            width:''
          },
          {
            prop:'createTime',
            label:'创建时间',
            checkTime:true,
            width:'200'
          },
        ]
      };
    },
    beforeCreate(){
      processClassListAll().then((res)=>{
          let resData = res.data
          this.typeOptions = resData.map((item)=>{
              return {categoryName:`${item.moduleName}-${item.categoryName}`,id:item.id}
          })
      }).catch(()=>{
      })
    },
    created() {
      this.loadData();
    },
    methods: {
      hideDialog() {
        this.loadData();
      },
      // 搜索/重置
      search(reset) {
        if (reset) {
          this.searchForm = {
            category:'',
            modelKey:'',
            modelName:''
          };
        }
        this.page.pageNum = 1;
        this.loadData();
      },

      // 点击删除弹框
      handleDel(row){
        this.$confirm('是否确定删除该数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delFn(row)
        })
      },

      // 删除
      delFn(row){
        this.loading = true
        delModel(row.modelId).then((res)=>{
          this.$message({
            type:'success',
            message:'操作',
            duration:1500
          })
          this.loadData()
        }).catch(()=>{
          this.loading = false
        })
      },

      // 历史
      handleHistory(row){
        this.$refs.historyModel.init(row)
      },

      // 流程图
      handleProcessView(row){
        this.$refs.flowChart.init(row)
      },

      // 新增/修改
      handleAdd(row){
        this.$refs.addProcessModel.init(row)
      },
      // 设计
      handleDesign(row){
        this.$refs.designDrawing.init(row)
      },
      // 部署
      handleDeploy(row){
        this.loading = true
        deployModel(row.modelId).then((res)=>{
          this.$message({
            type:'success',
            message:'操作成功',
            duration:1500
          })
          this.loading = false
        }).catch(()=>{
          this.loading = false
        })
      },

      loadData(){
        this.loading = true
        let params = {
          ...this.page,
          ...this.searchForm
        }
        queryModelList(params).then((res)=>{
          let resData = res.data;
          this.tableData = resData.records
          this.page.total = resData.total
          this.loading = false
        }).catch(()=>{
          this.loading = false
        })
      },

      // 更改每页显示条数
      handleSizeChange(pageSize){
          this.page.pageSize = pageSize
          this.loadData()
      },

      // 选择页数/点击上一页/下一页
      handleCurrentPage(currentPage){
          this.page.pageNum = currentPage
          this.loadData()
      }

    }
  };
</script>
<style lang="less">
.process_model {
    padding: 16px 12px 0;
}
</style>
