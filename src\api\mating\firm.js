import request from '@/utils/request'
// 考察记录 分页查询
export function getListPage(param) {
  return request({
    url: '/user-server/inspectionRecord/getListPage',
    method: 'get',
    params: param,
  })
}
// 考察记录 列表详情
export function getRecordBySupplierId(param) {
  return request({
    url: '/user-server//inspectionRecord/getRecordBySupplierId',
    method: 'get',
    params: param,
  })
}
// 考察记录 查询考察记录详情
export function getRecordDetail(param) {
  return request({
    url: '/user-server/inspectionRecord/getRecordDetail',
    method: 'get',
    params: param,
  })
}
// 考察记录 查询列表
export function getSupplierRecordList(param) {
  return request({
    url: '/user-server/inspectionRecord/getSupplierRecordList',
    method: 'get',
    params: param,
  })
}