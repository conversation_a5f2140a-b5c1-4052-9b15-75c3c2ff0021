<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default  {
  name:  'App',
    metaInfo() {
        return {
            title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
            titleTemplate: title => {
                return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
            }
        }
    }
}
</script>
<style lang="scss">
.zhuang{
width: 44px;
height: 28px;
background: #E8F4FF;
border-radius: 3px;
border: 1px solid rgba(56,154,255,0.4);
font-size: 12px;
color:#389AFF;
padding: 3px 5px;
}
</style>
