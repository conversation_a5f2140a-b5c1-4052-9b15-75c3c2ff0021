<template>
  <!-- 修改节点 -->
  <el-dialog title="修改节点" :visible.sync="startVisible" width="40vw" append-to-body>
    <div class="block" v-loading="startControlLoading">
      <el-table class="start_control_table" :data="nodeList" style="width: 100%">
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column prop="nodeName" label="节点" align="left">
          <template slot-scope="{ row }">
            <div>{{ row.nodeName }}</div>
          </template>
        </el-table-column>

        <el-table-column prop="planTime" label="计划时间" align="left">
          <template slot-scope="{ row }">
            <el-date-picker v-model="row.planTime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              placeholder="选择日期" :disabled="row.finishStatus == 1 ? true : false">
            </el-date-picker>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="startControl">确定</el-button>
      <el-button @click="closeStart">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  getCastingNodePage,
  exportData,
  findAllNodeByCastingId,
  findAllNodeByCastingIdForUpdate,
  batchUpdateNodePlanTime,
} from "@/api/cast/nodeControl";
export default {
  name: 'UpdateNode',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      startVisible: false,
      startControlLoading: false,
      nodeList: [],
    }
  },
  watch: {
    visible(newVal) {
      this.startVisible = newVal
      if (newVal) {
        this.getData()
      }
    },
    startVisible(newVal) {
      if (!newVal) {
        this.closeStart()
      }
    }
  },
  methods: {
    // 确认修改
    startControl() {
      this.startControlLoading = true
      batchUpdateNodePlanTime(this.nodeList).then((res) => {
        this.startControlLoading = false
        this.closeStart()
      }).catch(() => {
        this.startControlLoading = false
      });
    },
    getData() {
      findAllNodeByCastingIdForUpdate({ castingId: this.id }).then(
        (res) => {
          this.nodeList = res.data;
        }
      );
    },
    closeStart() {
      this.startVisible = false
      this.$emit('update:visible', false)
      this.$emit('refresh')
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  line-height: 30px;
}

.node {
  display: flex;
  flex-direction: column;
}

.font16 {
  font-size: 16px;
}

.fontweight {
  font-weight: 700;
}

.btn_box {
  margin-bottom: 10px;
}

.data_box {
  margin-bottom: 10px;

  .el-date-editor {
    margin-right: 10px;
  }
}

::v-deep .el-card.is-always-shadow {
  box-shadow: none;
  border: none;
  background: #f3f7fd;
  border-radius: 5px;
}
</style>
