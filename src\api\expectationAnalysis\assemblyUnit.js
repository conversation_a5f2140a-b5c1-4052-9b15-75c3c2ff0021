import request from '@/utils/request'
// 新增
export function qtPart(param) {
    return request({
        url: '/mes-server/qt/part',
        method: 'post',
        data: param,
    })
}

// 列表查询
export function partPage(param) {
    return request({
        url: '/mes-server/qt/part/page',
        method: 'get',
        params: param,
    })
}

// 机组类别
export function modelList() {
    return request({
        url: '/mes-server/qt/model/list',
        method: 'get'
    })
}

// 导入
export function amountExcel(query) {
    return request({
        url: '/mes-server/tq/amount/excel',
        method: 'post',
        data: query,
        responseType: 'blob', // important
    })
}

// 删除
export function delPart(id) {
    return request({
        url: `/mes-server/qt/part/${id}`,
        method: 'delete',
    })
}

// 更改状态
export function partStatus(query) {
    return request({
        url: `/mes-server/qt/part/${query.id}/status/${query.status}`,
        method: 'put',
    })
}

// 更改重点关注
export function partAttention(query) {
    return request({
        url: `/mes-server/qt/part/${query.id}/attention/${query.attention}`,
        method: 'put',
    })
}