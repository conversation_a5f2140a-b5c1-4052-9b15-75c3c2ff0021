<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column label="序号" type="index" />
      <el-table-column label="模块状态" prop="alarmName">
        <template slot-scope="scope">
          <span
            style="
              font-size: 12px;
              display: inline-block;
              width: 44px;
              height: 28px;
              line-height: 28px;
              border-radius: 3px;
              text-align: center;
            "
            :class="
              scope.row.alarmType == 0
                ? 'yu'
                : scope.row.alarmType == 1
                ? 'bao'
                : ''
            "
            >{{ scope.row.alarmName }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="拖期天数" prop="days" />
      <el-table-column label="拖期比例" prop="triggerValue">
        <template slot-scope="scope">
          <span>{{ scope.row.triggerValue }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="推送领导" prop="postSort">
        <template slot-scope="scope">
          <span v-for="(item, index) in scope.row.userList" :key="index"
            >{{ item.name }}
            <span
              style="font-size: 20px; padding-top: 3px"
              v-if="index != scope.row.userList.length - 1"
              >/</span
            >
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-setting"
            @click="handleUpdate(scope.row)"
            >配置</el-button
          >
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:post:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="560px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="状态" prop="alarmName">
         
          <span
            style="
              font-size: 12px;
              display: inline-block;
              width: 44px;
              height: 28px;
              line-height: 28px;
              border-radius: 3px;
              text-align: center;
            "
            :class="
              form.alarmName == '预警'
                ? 'yu'
                : form.alarmName == '报警'
                ? 'bao'
                : ''
            "
            >{{ form.alarmName }}</span
          >
      
        </el-form-item>

        <el-form-item label="拖期比例" prop="triggerValue" style="width: 70%">
          <el-input v-model="form.triggerValue" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="推送领导" prop="postName">
          <el-checkbox
            :indeterminate="isIndeterminate1"
            v-model="checkAll1"
            @change="handleCheckAllChange1"
            >全选</el-checkbox
          >
          <div class="checkbox">
            <el-checkbox-group
              v-model="userIds"
              @change="handleCheckedCitiesChange1"
              class="checkboxSon"
            >
              <el-checkbox
                v-for="(user, index) in cities"
                :label="user.id"
                :key="index"
                >{{ user.label }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
        </el-form-item>
        <el-divider>
          <span class="cross">以下设置状态持续一定天数,推送某些直系领导</span>
        </el-divider>
        <el-form-item label="状态天数阀值" prop="days" style="width: 70%">
          <el-input v-model="form.days" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="推送领导" prop="postName">
          <el-checkbox
            :indeterminate="isIndeterminate2"
            v-model="checkAll2"
            @change="handleCheckAllChange2"
            >全选</el-checkbox
          >
          <div class="checkbox">
            <el-checkbox-group
              v-model="appUserIds"
              @change="handleCheckedCitiesChange2"
              class="checkboxSon"
            >
              <el-checkbox
                v-for="city in cities"
                :label="city.id"
                :key="city"
                >{{ city.label }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
const cityOptions = ["上海", "北京", "广州", "深圳"];
import {
  getAlarmList,
  getPushUserList,
  updateAlarm,
  addPost,
  updatePost,
} from "@/api/alarmList";

export default {
  name: "Post",
  // dicts: ["sys_normal_disable"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        moduleType: 1,
      },
      // 表单参数
      form: {
        id: undefined,
        userIds: [],
        appUserIds: [],
        days: undefined,
        triggerValue: undefined,
      },
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" },
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" },
        ],
      },
      checkAll1: false,
      checkAll2: false,
      cities: [],
      userIds: [],
      appUserIds: [],
      isIndeterminate1: true,
      isIndeterminate2: true,
    };
  },
  created() {
    this.getList();
    this.getPushUserList();
  },
  methods: {
    handleCheckAllChange1(val) {
      let user = [];
      this.cities.forEach((e) => {
        user.push(e.id);
      });
      this.userIds = val ? user : [];
      this.isIndeterminate1 = false;
    },
    handleCheckedCitiesChange1(value) {
      let checkedCount = value.length;
      this.checkAll1 = checkedCount === this.cities.length;
      this.isIndeterminate1 =
        checkedCount > 0 && checkedCount < this.cities.length;
    },
    handleCheckAllChange2(val) {
      let user = [];
      this.cities.forEach((e) => {
        user.push(e.id);
      });
      this.appUserIds = val ? user : [];
      this.isIndeterminate2 = false;
    },
    handleCheckedCitiesChange2(value) {
      let checkedCount = value.length;
      this.checkAll2 = checkedCount === this.cities.length;
      this.isIndeterminate2 =
        checkedCount > 0 && checkedCount < this.cities.length;
    },

    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      getAlarmList(this.queryParams).then((response) => {
        this.postList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    //推送列表
    getPushUserList() {
      getPushUserList({ moduleType: 1 }).then((response) => {
        response.data.forEach((element) => {
          this.cities.push({
            id: element.id,
            label: element.name,
          });
        });
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        appUserIds: [], //天数触发推送用户
        days: undefined, //天数触发值
        triggerValue: undefined, //百分比触发值
        id: undefined,
        userIds: [], //百分比触发推送用户
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let that = this;
      that.userIds = [];
      that.appUserIds = [];
      that.form.id = row.id;
      that.form.alarmName = row.alarmName;
      that.form.days = row.days;
      that.form.triggerValue = row.triggerValue;
      row.userList.forEach((e) => {
        that.userIds.push(e.id);
      });
      row.appUserList.forEach((e) => {
        that.appUserIds.push(e.id);
      });
      that.open = true;
      that.title = "配置";
      // });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.form.days = this.form.days * 1;
      this.form.userIds = this.userIds;
      this.form.appUserIds = this.appUserIds;
      updateAlarm(this.form).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.open = false;
        this.getList();
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog{
  margin-top: 10vh !important;
}
.state {
  margin-top: 3px;
  width: 44px;
height: 28px;
display: flex;
justify-content: center;
align-items: center;
background: #FDEDED;
border-radius: 3px;
border: 1px solid rgba(228,121,133,0.4);
font-weight: 400;
color: #E47985;
font-size: 12px;
}
.checkbox {
  border: 1px solid #eeeeee;
  width: 210px;
  height: 200px;
    overflow: hidden;
  overflow-y: scroll;
}
.checkboxSon {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}
.cross {
  font-size: 12px;
}
::v-deep .el-divider__text {
  width: 284px;
}
.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}
.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}
</style>
