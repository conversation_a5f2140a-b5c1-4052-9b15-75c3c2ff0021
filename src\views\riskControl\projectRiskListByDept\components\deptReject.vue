<template>
    <el-dialog
      :visible.sync="dialogVisible"
      width="40%"
      title="驳回"
      append-to-body
      @close="cancel"
    >
      <el-form ref="form" :model="form" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="驳回原因"
              prop="rejectDesc"
              :rules="[
                { required: true, message: '请输入驳回原因', trigger: 'blur' },
              ]"
            >
              <el-input 
              v-model="form.rejectDesc" 
              type="textarea"
              :rows="5"
              placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading"
          >确 定</el-button
        >
        <el-button @click="cancel"
          >取 消</el-button
        >
      </div>
    </el-dialog>
</template>

<script>
import { setDeptStatus,setSupplierStatus } from '@/api/riskControl/projectRiskListByDept'
export default {
    components:{},

    data(){
        return{
            dialogVisible:false,
            btnLoading:false,
            rowData:null,
            riskDataId:null,
            isSuppiler:null,
            form:{
                rejectDesc:''
            }
        }
    },

    methods:{
        init(riskDataId,row,isSuppiler){
            this.form.rejectDesc = ''
            this.dialogVisible = true
            this.btnLoading = false,
            this.isSuppiler = isSuppiler
            this.riskDataId = riskDataId
            this.rowData = row
        },
        // 审核确定
        confirm() {
            this.$refs['form'].validate((valid) => {
                if(valid){
                  if(this.isSuppiler){
                    let params = {
                        id: this.riskDataId,
                        projectRiskSupplierId: this.rowData.id,
                        status: 2,
                        rejectDesc:this.form.rejectDesc
                    }
                    this.btnLoading = true
                    setSupplierStatus(params).then((res) => {
                        this.$modal.msgSuccess("操作成功");
                        this.btnLoading = false;
                        this.dialogVisible = false;
                        this.$emit('getProjectRiskDept')
                    })
                  }else{
                    let params = {
                        id: this.riskDataId,
                        projectRiskDeptId: this.rowData.id,
                        status: 2,
                        rejectDesc:this.form.rejectDesc
                    }
                    this.btnLoading = true
                    setDeptStatus(params).then((res) => {
                        this.$modal.msgSuccess("操作成功");
                        this.btnLoading = false;
                        this.dialogVisible = false;
                        this.$emit('getProjectRiskDept')
                    })
                  }
                }
            })
                
        },

        cancel(){
            this.$refs['form'].resetFields()
            this.dialogVisible = false
        }
    },

}

</script>

<style scoped lang='less'>
</style>