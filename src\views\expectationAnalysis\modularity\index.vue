<template>
  <div class="app-container">
    <el-form
      :model="searchForm"
      ref="searchForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="搜索" prop="posid" label-width="40px">
        <el-input
          v-model="searchForm.query"
          placeholder="模块名/令号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery(false)"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="handleQuery(true)"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          plain
          @click="handleAdd()"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="loadData"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" />
      <el-table-column label="机组类别" prop="modelNameList" align="center" />
      <el-table-column label="模块名称" prop="name" />
      <el-table-column label="部套数" prop="partNum" />
      <el-table-column label="创建时间" prop="crtTime">
        <template slot-scope="{ row }">
          <div>{{ row.crtTime | dateFormat }}</div>
        </template>
      </el-table-column>

      <el-table-column label="重点关注" prop="">
        <template slot-scope="{ row }">
          <el-switch
            @change="changeStatus(row)"
            v-model="row.attentionStatus"
            :active-value="1"
            :inactive-value="0"
          >
          </el-switch>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleInfo(row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-copy-document"
            @click="handleAdd(row)"
            >复制</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDel(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="searchForm.pageNum"
      :limit.sync="searchForm.pageSize"
      @pagination="loadData"
    />
    <check-info ref="checkInfo"></check-info>
    <add-unit ref="addUnit" @hideDialog="loadData"></add-unit>
  </div>
</template>

<script>
import checkInfo from "./components/checkInfo";
import addUnit from "./components/addUnit.vue";
import {
  modulePage,
  moduleAttention,
  delModule,
} from "@/api/expectationAnalysis/modularity";
export default {
  components: { checkInfo, addUnit },
  data() {
    return {
      total: 0,
      showSearch: true,
      loading: false,
      tableData: [],
      searchForm: {
        query: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.loadData();
  },

  methods: {
    handleQuery(flag) {
      if (flag) {
        this.searchForm.query = "";
        this.searchForm.pageNum = 1;
      }
      this.loadData();
    },

    // 新增
    handleAdd(row) {
      this.$refs.addUnit.init(row);
    },

    // 详情
    handleInfo(row) {
      this.$refs.checkInfo.init(row);
    },

    // 删除
    handleDel(row) {
      this.$confirm("删除关联会将项目已关联的期量删除，是否确认？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(() => {
          this.loading = false;
          delModule(row.id).then((res) => {
            this.$message({
              type: "success",
              message: "操作成功",
              duration: 1500,
            });
            this.loadData();
          });
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 更改重点关注
    changeStatus(row) {
      this.loading = true;
      let params = {
        attention: row.attentionStatus,
        id: row.id,
      };
      moduleAttention(params)
        .then((res) => {
          this.$message({
            type: "success",
            message: "操作成功",
            duration: 1500,
          });
          this.loadData();
        })
        .catch(() => {
          this.loading = false;
        });
    },

    loadData() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      modulePage(params)
        .then((res) => {
          let { records, total } = res.data || {};
          this.tableData = records;

          this.total = total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped></style>
