<template>
  <div>
    <el-dialog title="行业影响力评价" :visible.sync="dialogVisible" width="1000px" top="30px" style="padding-bottom: 20px"
      :close-on-click-modal="false">
      <div style="display: flex">
        <div>
          <!-- 基本信息展示区域 -->
          <el-row class="info-grid" gutter="20">
            <el-col :span="12" class="info-item">
              <span class="label">供方编码：</span>
              <span class="value">{{ form.supplierCode }}</span>
            </el-col>
            <el-col :span="12" class="info-item right">
              <span class="label required">上年度研发投入：</span>
              <span class="value">{{ form.rdInput }}</span>
              <span class="unit">万元</span>
            </el-col>
            <el-col :span="12" class="info-item">
              <span class="label">供方编号：</span>
              <span class="value">{{ form.supplierNo }}</span>
            </el-col>
            <el-col :span="12" class="info-item right">
              <span class="label required">上年度营业收入：</span>
              <span class="value">{{ form.revenueCost }}</span>
              <span class="unit">万元</span>
            </el-col>
            <el-col :span="12" class="info-item">
              <span class="label">供方名称：</span>
              <span class="value">{{ form.supplierName }}</span>
            </el-col>
            <el-col :span="12" class="info-item right">
              <span class="label">上年度研发投入占比：</span>
              <span class="value">{{ rdInputRatio || "-" }}</span>
            </el-col>
            <el-col :span="12" class="info-item right">
              <span class="label">分类编号：</span>
              <span class="value">{{ form.typeNo || "-" }}</span>
            </el-col>
          </el-row>

          <!-- 批量审核选择区域 -->
          <div class="batch-audit-section" v-if="form.batchAuditList && form.batchAuditList.length > 0">
            <div class="section-title">批量审核选择</div>
            <el-checkbox-group v-model="selectedIds" class="batch-checkbox-group">
              <el-checkbox v-for="item in form.batchAuditList" :key="item.id" :label="item.id"
                class="batch-checkbox-item">
                {{ item.typeNo }}
              </el-checkbox>
            </el-checkbox-group>
          </div>

          <!-- 证明文件展示区域 -->
          <div class="cert-section">
            <div class="section-title">证明文件</div>
            <el-row :gutter="20" class="cert-container">
              <el-col :span="8" v-for="(item, index) in certItems" :key="index" class="cert-item">
                <!-- 图片文件预览 -->
                <el-image v-if="isImageFile(item.url)" :src="item.url" :alt="item.name" class="cert-img"
                  :preview-src-list="[item.url]">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <!-- 非图片文件预览 -->
                <div v-else class="cert-file" @click="previewFile(item.url)">
                  <div class="file-icon">
                    <i class="el-icon-document"></i>
                  </div>
                  <div>下载文件</div>
                </div>
                <div class="upload-label" v-if="item">
                  <div>{{ item.dataName }}</div>
                  <div>{{ item.remarks }}</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 其他证明材料展示区域 -->
          <div class="other-materials">
            <div class="section-title">其他证明材料</div>
            <el-upload action="#" :file-list="form.otherMaterials" :disabled="true" :show-file-list="true"
              :on-preview="handlePreview">
            </el-upload>
          </div>

          <!-- 其他证明材料展示区域后添加 -->
          <div class="score-section">
            <el-row :gutter="20">
              <el-col :span="24" v-if="type === 2 || type === 3">
                <div class="score-item">
                  <span class="label">研发投入得分：</span>
                  <el-input-number v-model="form.rdScore" :min="0" :max="100" size="small"
                    :disabled="form.rdScoreNotApplicable">
                  </el-input-number>
                  <span class="unit">分</span>
                  <el-checkbox v-model="form.rdScoreNotApplicable" class="not-applicable"
                    @change="handleNotApplicableChange">
                    不适用
                  </el-checkbox>
                </div>
              </el-col>
              <el-col :span="24" v-if="type === 2 || type === 3">
                <div class="score-item">
                  <span class="label">行业影响力得分：</span>
                  <el-input v-model="form.industryScore" placeholder="0-100整数" size="small" class="score-input"
                    @input="handleScoreChange">
                  </el-input>
                  <span class="unit">分</span>

                  <span class="level-tip" v-if="industryLevel">{{
                    industryLevel
                    }}</span>
                </div>
              </el-col>
              <el-col :span="24" v-if="type === 3">
                <div>
                  <div class="label" style="margin-bottom: 10px">
                    改分理由：
                  </div>
                  <el-input style="width: 100%; margin-bottom: 10px" v-model="form.remarks" type="textarea"
                    size="small">
                  </el-input>
                </div>
              </el-col>
              <el-col :span="24" v-if="type === 3">
                <div class="score-item">
                  <span class="label">证明材料：</span>
                  <el-upload :headers="headers" :action="uploadUrl" :auto-upload="true" :file-list="form.fileList"
                    ref="otherFileList" multiple :limit="99" :on-success="onUpload">
                    <el-button type="primary">添加文件</el-button>
                  </el-upload>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 底部按钮 -->
          <div slot="footer" class="dialog-footer">
            <el-button type="danger" v-if="type === 2" @click="showRejectReason">驳回</el-button>
            <el-button type="primary" v-if="type === 2" @click="onAgree">同意</el-button>
            <el-button type="primary" v-if="type === 3" @click="submitForm">修改</el-button>
            <el-button @click="dialogVisible = false">取消</el-button>
          </div>
        </div>
        <div class="records">
          <div style="color: #409eff; margin: 20px; font-size: 18px">
            修改记录
          </div>
          <div style="height: 60vh; overflow-y: auto">
            <el-timeline>
              <el-timeline-item v-for="(activity, index) in records" :key="index" color="#409eff">
                <div style="font-size: 16px">
                  {{ activity.createTime.substring(0, 10) }}
                </div>
                <div style="margin: 10px 0">
                  <span>{{ activity.createName }}</span>&emsp;
                  <span>{{
                    activity.type == 1
                      ? "提交"
                      : activity.type == 2
                        ? "驳回"
                        : activity.type == 3
                          ? "修改"
                          : activity.type == 3
                            ? "通过"
                            : "转办"
                  }}</span>
                </div>
                <div v-if="activity.remarks">
                  原因&emsp;&emsp;{{ activity.remarks }}
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 驳回理由输入框 -->
    <el-dialog title="填写驳回理由" :visible.sync="rejectDialogVisible" width="500px">
      <el-input type="textarea" v-model="rejectReason" placeholder="请输入驳回理由" rows="4">
      </el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rejectDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitRejectReason">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSupplierInfo,
  apiAudit,
  apiUpdate,
} from "@/api/dimensionData/nodeManagement.js";
import { getToken } from "@/utils/auth";
export default {
  name: "IndustryInfluenceDialog",
  data() {
    return {
      headers: {
        "X-Token": "Bearer " + getToken(),
      },
      uploadUrl: process.env.VUE_APP_BASE_API + "/user-server/file/uploadFile", // 替换为实际的上传接口
      imgUrl: process.env.VUE_APP_FILE_API, // 替换为实际的上传接口
      dialogVisible: false,
      rejectDialogVisible: false,
      rejectReason: "",
      isEdit: false,
      form: {
        supplierCode: "",
        supplierNo: "",
        supplierName: "",
        rdInput: "",
        revenueCost: "",
        businessLicense: null,
        rdReport: null,
        revenueReport: null,
        otherMaterials: [
          {
            name: "2024年度企业年报-审计提供.pdf",
            url: "https://example.com/path/to/file1.pdf",
          },
          {
            name: "实用新型发明专利.pdf",
            url: "https://example.com/path/to/file2.pdf",
          },
          {
            name: "德川电厂项目总结.ppt",
            url: "https://example.com/path/to/file3.ppt",
          },
          {
            name: "公司简介.docx",
            url: "https://example.com/path/to/file4.docx",
          },
        ],
        rdScore: 0,
        rdScoreNotApplicable: true,
        industryScore: "",
        businessLicenseUrl: "",
        rdReportUrl: "",
        revenueReportUrl: "",
        batchAuditList: [],
      },
      selectedIds: [], // 选中的ID数组
      rules: {
        rdInput: [
          { required: true, message: "请输入上年度研发投入", trigger: "blur" },
          {
            type: "number",
            message: "必须为数字值",
            trigger: "blur",
            transform: (value) => Number(value),
          },
        ],
        revenueCost: [
          { required: true, message: "请输入上年度营业收入", trigger: "blur" },
          {
            type: "number",
            message: "必须为数字值",
            trigger: "blur",
            transform: (value) => Number(value),
          },
        ],
      },
      industryLevel: "",
      certItems: [
        {
          name: "营业执照",
          url: "https://img.freepik.com/free-vector/business-license-concept-illustration_114360-7887.jpg",
        },
        {
          name: "上年度企业年报中研发投入页截图",
          url: "https://img.freepik.com/free-vector/business-report-concept-illustration_114360-7887.jpg",
        },
        {
          name: "上年度企业年报中营业收入页截图",
          url: "https://img.freepik.com/free-vector/financial-report-concept-illustration_114360-7887.jpg",
        },
      ],
      records: [], //修改记录
      type: 1,
      id: 0,
    };
  },
  computed: {
    rdInputRatio() {
      console.log("密级", this.form.secrecyStatus);
      if (this.form.secrecyStatus == 0) {
        if (!this.form.rdInput || !this.form.revenueCost) return "-";
        return (
          ((this.form.rdInput / this.form.revenueCost) * 100).toFixed(2) + "%"
        );
      } else if (this.form.secrecyStatus == 1) {
        return this.form.researchProportion + "%";
      }
      return "-";
    },
  },
  methods: {
    async show(row) {
      this.dialogVisible = true;
      this.isEdit = !!row;

      if (row) {
        try {
          // 显示加载中
          const loading = this.$loading({
            lock: true,
            text: "加载中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          // 调用真实接口获取详情
          const response = await getSupplierInfo(row.id);
          console.log("技术审核详情接口返回数据:", response);

          if (response.data) {
            const detail = response.data;
            this.records = response.data.auditRecordList;
            console.log("获取记录", this.records);

            // 设置表单数据
            this.form = {
              ...this.form,
              typeNo: detail.typeNo,
              secrecyStatus: detail.secrecyStatus,
              researchProportion: detail.researchProportion || "",
              supplierCode: detail.supplierCode || "",
              supplierNo: detail.supplierNo || "",
              supplierName: detail.supplierName || "",
              rdInput: detail.researchCost || "", // 研发投入
              revenueCost: detail.revenueCost || "", // 营业收入
              rdScore: detail.researchScore || 0, // 研发投入分数
              rdScoreNotApplicable: true,
              industryScore: detail.effectScore || "", // 行业影响力得分
              batchAuditList: detail.batchAuditList || [], // 批量审核列表
              // 处理附件列表
              otherMaterials:
                detail.otherFileList?.map((e) => ({
                  ...e,
                  url: process.env.VUE_APP_FILE_API + e.newName,
                })) || [],
            };

            // 如果有证书图片URL，可以根据实际情况处理
            // 这里需要根据实际接口返回的数据结构调整
            // this.updateCertItems(detail);
            this.certItems = detail.deptFileList?.map((e) => ({
              ...e,
              name: e.dataName,
              url: process.env.VUE_APP_FILE_API + e.htcFile?.newName,
            }));
            // 触发行业得分提示计算
            this.handleScoreChange(this.form.industryScore);
          } else {
            this.$message.error(
              "获取详情失败：" + (response.msg || "接口返回错误")
            );
          }

          loading.close();
        } catch (error) {
          this.$message.error("获取详情失败：" + (error.message || "未知错误"));
          loading.close();
          this.dialogVisible = false;
        }
      } else {
        this.resetForm();
      }
    },

    // 处理附件列表
    processAttachments(detail) {
      let attachments = [];

      // 处理 deptFileList (证明文件)
      if (detail.deptFileList && detail.deptFileList.length > 0) {
        attachments = attachments.concat(
          detail.deptFileList.map((file) => {
            // 如果存在 htcFile 对象，则使用其中的信息
            if (file.htcFile) {
              return {
                name: file.htcFile.name || file.htcFile.newName || "未命名文件",
                url: file.htcFile.url || "",
              };
            }
            return {
              name: file.dataName || "未命名文件",
              url: "",
            };
          })
        );
      }

      // 处理 fileList
      if (detail.fileList && detail.fileList.length > 0) {
        attachments = attachments.concat(
          detail.fileList.map((file) => ({
            name: file.name || "未命名文件",
            url: file.url || "",
          }))
        );
      }

      // 处理 appFileList
      if (detail.appFileList && detail.appFileList.length > 0) {
        attachments = attachments.concat(
          detail.appFileList.map((file) => ({
            name: file.name || "未命名文件",
            url: file.url || "",
          }))
        );
      }

      // 如果没有附件，返回默认的附件列表
      return attachments.length > 0 ? attachments : this.form.otherMaterials;
    },

    // 更新证书图片
    updateCertItems(detail) {
      // 处理 deptFileList 中的证明文件
      if (detail.deptFileList && detail.deptFileList.length > 0) {
        detail.deptFileList.forEach((file) => {
          if (file.htcFile && file.htcFile.url) {
            // 根据文件名或类型判断证书类型
            const fileName = file.htcFile.name || file.htcFile.newName || "";
            const dataName = file.dataName || "";

            if (
              fileName.includes("营业执照") ||
              dataName.includes("营业执照")
            ) {
              this.certItems[0].url = file.htcFile.url;
            } else if (
              fileName.includes("研发投入") ||
              dataName.includes("研发投入")
            ) {
              this.certItems[1].url = file.htcFile.url;
            } else if (
              fileName.includes("营业收入") ||
              dataName.includes("营业收入")
            ) {
              this.certItems[2].url = file.htcFile.url;
            }
          }
        });
      }

      // 备用：如果 fileList 中有特定名称的文件，将其设置为证书图片
      if (detail.fileList && detail.fileList.length > 0) {
        detail.fileList.forEach((file) => {
          if (file.name && file.name.includes("营业执照")) {
            this.certItems[0].url = file.url || this.certItems[0].url;
          } else if (file.name && file.name.includes("研发投入")) {
            this.certItems[1].url = file.url || this.certItems[1].url;
          } else if (file.name && file.name.includes("营业收入")) {
            this.certItems[2].url = file.url || this.certItems[2].url;
          }
        });
      }
    },
    resetForm() {
      this.form = {
        supplierCode: "",
        supplierNo: "",
        supplierName: "",
        rdInput: "",
        revenueCost: "",
        businessLicense: null,
        rdReport: null,
        revenueReport: null,
        otherMaterials: [],
        rdScore: 0,
        rdScoreNotApplicable: true,
        industryScore: "",
        businessLicenseUrl: "",
        rdReportUrl: "",
        revenueReportUrl: "",
      };
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },
    submitForm() {
      if (this.type === 3) {
        // 执行修改
        const params = {
          effectScore: this.form.industryScore,
          researchScore: this.form.rdScore,
          fileList: this.form.fileList || [],
          remarks: this.form.remarks,
          id: this.id,
        };
        apiUpdate(params).then((res) => {
          this.$message.success("修改成功");
          this.dialogVisible = false;
          this.$emit("refresh");
        });
      } else {
        this.dialogVisible = false;
      }

      // this.$refs.form.validate((valid) => {
      //   if (valid) {
      //     const formData = new FormData();
      //     Object.keys(this.form).forEach((key) => {
      //       if (
      //         typeof this.form[key] === "string" ||
      //         typeof this.form[key] === "number"
      //       ) {
      //         formData.append(key, this.form[key]);
      //       }
      //     });

      //     if (this.form.businessLicense) {
      //       formData.append("businessLicense", this.form.businessLicense.raw);
      //     }
      //     if (this.form.rdReport) {
      //       formData.append("rdReport", this.form.rdReport.raw);
      //     }
      //     if (this.form.revenueReport) {
      //       formData.append("revenueReport", this.form.revenueReport.raw);
      //     }

      //     // const request = this.isEdit
      //     //   ? this.updateData(formData)
      //     //   : this.createData(formData);

      //     // request
      //     //   .then(() => {
      //     //     this.$message.success(this.isEdit ? "修改成功" : "添加成功");
      //     //     this.dialogVisible = false;
      //     //     this.$emit("refresh");
      //     //   })
      //     //   .catch(() => {
      //     //     this.$message.error(this.isEdit ? "修改失败" : "添加失败");
      //     //   });
      //   }
      // });
    },
    createData(formData) {
      return Promise.resolve();
    },
    updateData(formData) {
      return Promise.resolve();
    },
    handleBusinessLicense(file) {
      this.form.businessLicense = file;
    },
    handleRdReport(file) {
      this.form.rdReport = file;
    },
    handleRevenueReport(file) {
      this.form.revenueReport = file;
    },
    addMaterial() {
      // 实现添加其他材料的逻辑
    },
    calculateIndustryLevel() {
      // 实现计算行业优秀水平的逻辑
      this.$message.info("计算行业优秀水平");
    },
    showScoreDetails() {
      // 显示分数详情
    },
    showScoreReason() {
      // 显示驳回原因
    },
    handlePreview(file) {
      console.log("预览文件:", file); // 调试用

      if (!file.url) {
        this.$message.warning("文件链接不存在");
        return;
      }

      try {
        // 方法1：使用 window.open
        const newWindow = window.open(file.url, "_blank");

        // 确保新窗口被成功打开
        if (newWindow === null) {
          this.$message.error("浏览器阻止了新窗口打开，请检查浏览器设置");
          return;
        }

        // 方法2：如果方法1不起作用，可以尝试创建一个临时链接
        // const link = document.createElement('a');
        // link.href = file.url;
        // link.target = '_blank';
        // link.rel = 'noopener noreferrer';
        // document.body.appendChild(link);
        // link.click();
        // document.body.removeChild(link);
      } catch (error) {
        console.error("打开文件失败:", error);
        this.$message.error("打开文件失败，请稍后重试");
      }
    },
    showRejectReason() {
      this.rejectDialogVisible = true;
    },
    async submitRejectReason() {
      if (!this.rejectReason) {
        this.$message.error("请填写驳回理由");
        return;
      }

      try {
        // 准备提交的数据
        const submitData = {
          applyStatus: this.form.rdScoreNotApplicable ? 1 : 0,
          auditRemark: this.rejectReason,
          auditStatus: 2,
          effectScore: this.form.industryScore,
          researchScore: this.form.rdScore,
          id: this.id,
        };

        // 模拟接口调用
        // await this.mockSubmitApi(submitData);
        await apiAudit(submitData);
        // 提交成功后的处理
        this.$message.success("驳回成功");

        // 关闭所有弹窗
        this.rejectDialogVisible = false;
        this.dialogVisible = false;

        // 清空表单数据
        this.resetForm();

        // 触发父组件刷新列表
        this.$emit("refresh");
      } catch (error) {
        this.$message.error("提交失败：" + error.message);
      }
    },

    // 模拟接口调用
    mockSubmitApi(data) {
      return new Promise((resolve, reject) => {
        console.log("提交的数据：", data);
        // 模拟接口延迟 1 秒
        setTimeout(() => {
          // 模拟成功
          resolve({ code: 200, message: "提交成功" });

          // 模拟失败示例
          // reject(new Error('网络错误'));
        }, 1000);
      });
    },

    // 重置表单
    resetForm() {
      this.form = {
        supplierCode: "",
        supplierNo: "",
        supplierName: "",
        rdInput: "",
        revenueCost: "",
        businessLicense: null,
        rdReport: null,
        revenueReport: null,
        otherMaterials: [],
        rdScore: 0,
        rdScoreNotApplicable: true,
        industryScore: "",
        businessLicenseUrl: "",
        rdReportUrl: "",
        revenueReportUrl: "",
      };
      this.rejectReason = "";
      this.industryLevel = "";
    },
    handleScoreChange(value) {
      const score = parseInt(value);
      if (!isNaN(score)) {
        if (score >= 90 && score <= 100) {
          this.industryLevel = "行业顶尖水平";
        } else if (score >= 80 && score <= 89) {
          this.industryLevel = "行业优秀水平";
        } else if (score >= 60 && score <= 79) {
          this.industryLevel = "行业平均水平";
        } else if (score >= 0 && score <= 59) {
          this.industryLevel = "低于行业平均水平";
        } else {
          this.industryLevel = "";
        }
      } else {
        this.industryLevel = "";
      }
    },
    handleNotApplicableChange(checked) {
      if (checked) {
        // 勾选"不适用"时，自动设置为满分100分
        this.form.rdScore = 100;
      } else {
        // 取消勾选时，重置为0分
        this.form.rdScore = 0;
      }
    },

    // 同意审核
    async onAgree() {
      try {
        // 准备提交的数据
        const submitData = {
          applyStatus: this.form.rdScoreNotApplicable ? 1 : 0,
          auditRemark: "",
          auditStatus: 1,
          effectScore: this.form.industryScore,
          researchScore: this.form.rdScore,
          id: this.id,
          ids: this.selectedIds, // 选中的ID数组
        };
        await apiAudit(submitData);
        // 提交成功后的处理
        this.$message.success("已审批！");
        this.dialogVisible = false;

        // 清空表单数据
        this.selectedIds = []; // 重置选中的ID数组
        this.resetForm();

        // 触发父组件刷新列表
        this.$emit("refresh");
      } catch (error) {
        this.$message.error("提交失败：" + error.message);
      }
    },
    onUpload(response, file, fileList) {
      this.form.fileList = fileList;
    },
    // 判断是否为图片文件
    isImageFile(fileName) {
      if (!fileName) return false;
      const imageExtensions = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".bmp",
        ".webp",
        ".svg",
      ];
      const extension = fileName
        .toLowerCase()
        .substring(fileName.lastIndexOf("."));
      return imageExtensions.includes(extension);
    },
    // 下载非图片文件
    previewFile(url) {
      if (!url) {
        this.$message.warning("文件链接不存在");
        return;
      }

      try {
        // 从 URL 中提取文件名
        const urlParts = url.split("/");
        const lastPart = urlParts[urlParts.length - 1];
        let filename = "download";
        if (lastPart && lastPart.includes(".")) {
          filename = lastPart.split("?")[0]; // 去除查询参数
        }

        // 在URL后面添加下载参数，强制下载
        const downloadUrl =
          url + (url.includes("?") ? "&" : "?") + "download=1&attachment=true";

        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = filename;

        // 设置下载属性强制下载
        link.setAttribute("download", filename);
        link.style.display = "none";

        // 添加到DOM并触发点击
        document.body.appendChild(link);
        link.click();

        // 清理DOM
        setTimeout(() => {
          document.body.removeChild(link);
        }, 100);

        this.$message.success("文件下载开始");

        // 如果上面的方法不起作用，尝试备选方案
        setTimeout(() => {
          // 检查是否成功下载，如果没有，使用iframe方式
          const iframe = document.createElement("iframe");
          iframe.style.display = "none";
          iframe.src = downloadUrl;
          document.body.appendChild(iframe);

          setTimeout(() => {
            document.body.removeChild(iframe);
          }, 1000);
        }, 500);
      } catch (error) {
        console.error("下载文件失败:", error);
        // 最后的备选方案：使用window.open
        window.open(url, "_blank");
        this.$message.info("文件已在新窗口中打开");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.info-grid {
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  &.right {
    justify-content: flex-start;
  }

  .label {
    color: #606266;
    min-width: 120px;

    &.required::before {
      content: "*";
      color: #f56c6c;
      margin-right: 4px;
    }
  }

  .value {
    color: #303133;
  }

  .unit {
    color: #606266;
    margin-left: 4px;
  }
}

.batch-audit-section {
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;

  .section-title {
    color: #303133;
    font-size: 14px;
    margin-bottom: 15px;
  }

  .batch-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;

    .batch-checkbox-item {
      margin-right: 0;
      margin-bottom: 10px;

      :deep(.el-checkbox__label) {
        color: #606266;
        font-size: 14px;
      }

      :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: #409eff;
        border-color: #409eff;
      }
    }
  }
}

.cert-section {
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;

  .section-title {
    color: #303133;
    font-size: 14px;
    margin-bottom: 15px;
  }

  .cert-container {
    display: flex;
    flex-wrap: wrap;
  }

  .cert-item {
    text-align: center;

    .cert-img {
      width: 120px !important;
      height: 120px !important;
      border: 1px solid #dcdfe6;
      cursor: pointer;

      :deep(.el-image__inner) {
        transition: all 0.3s;

        &:hover {
          transform: scale(1.05);
        }
      }

      :deep(.image-slot) {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: #f5f7fa;
        color: #909399;
        font-size: 30px;
      }
    }

    .cert-file {
      width: 120px;
      height: 120px;
      border: 1px solid #dcdfe6;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: #f5f7fa;
      transition: all 0.3s;
      margin: 0 auto;

      &:hover {
        background: #e6f7ff;
        border-color: #409eff;
        transform: scale(1.05);
      }

      .file-icon {
        font-size: 40px;
        color: #909399;
        margin-bottom: 8px;
      }

      // 下载文字样式
      >div:last-child {
        font-size: 12px;
        color: #606266;
        text-align: center;
        margin-top: 4px;
      }

      .file-name {
        font-size: 12px;
        color: #606266;
        text-align: center;
        word-break: break-all;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }

    .cert-name {
      margin-top: 8px;
      font-size: 14px;
      color: #606266;
      width: 120px;
      margin: 8px auto 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    // 文件名称标签居中样式
    .upload-label {
      margin-top: 8px;
      text-align: center;
      width: 120px;
      margin-left: auto;
      margin-right: auto;

      div {
        font-size: 12px;
        color: #606266;
        line-height: 1.4;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;

        &:first-child {
          font-weight: 500;
          color: #303133;
        }
      }
    }
  }
}

.other-materials {
  padding: 20px 0;

  .section-title {
    color: #303133;
    font-size: 14px;
    // margin-bottom: 15px;
  }

  // :deep(.el-upload-list) {
  //   .el-upload-list__item {

  //     .el-upload-list__item-status-label,
  //     .el-icon-close {
  //       display: none !important;
  //     }

  //     &:hover {
  //       cursor: pointer;
  //       color: #409EFF;
  //     }
  //   }
  // }
}

.score-section {
  padding: 20px 0;
  border-top: 1px solid #ebeef5;

  .score-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .label {
      color: #606266;
      margin-right: 8px;
    }

    .unit {
      color: #606266;
      margin-left: 4px;
    }

    .not-applicable {
      margin-left: 15px;
    }

    .score-input {
      width: 120px;
    }

    .level-link {
      margin-left: 15px;
      font-size: 13px;
    }

    .level-tip {
      margin-left: 10px;
      color: #409eff;
      font-size: 13px;
    }
  }
}

:deep(.el-input-number) {
  width: 120px;
}

:deep(.el-dialog__body) {
  padding: 20px !important;
}

.records {
  background-color: rgb(246, 246, 246);
  width: 350px;
  padding: 0 30px;
}
</style>
