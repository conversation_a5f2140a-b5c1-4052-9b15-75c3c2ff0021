<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年份">
        <el-date-picker v-model="queryParams.year" style="width: 200px" type="year" value-format="yyyy"
          placeholder="选择年" @change="yearChange" :clearable="false"></el-date-picker>
      </el-form-item>
      <el-form-item label="部门" prop="deptNo">
        <el-select style="width: 200px" :key="queryParams.year" v-model="queryParams.deptNo" placeholder="请选择" clearable
          @change="handleDeptChange" @clear="handleClearDept" :clearable="true">
          <el-option v-for="item in secondCategoryList" :key="item.deptNo" :label="item.deptName"
            :value="item.deptNo">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="搜索" prop="query">
        <el-input v-model="queryParams.query" style="width: 300px" placeholder="请输入" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>

    </el-form>
    <div class="mb8 justify-between">
      <el-row :gutter="10" class="item-center">
      </el-row>
    </div>

    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange" max-height="600">
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column label="序号" type="index" width="120">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="部门" prop="deptName" width="120" />
      <el-table-column label="适用内容" prop="supplierTypeList" width="400">
        <template slot-scope="scope">
          <el-tooltip placement="top" width="200">
            <div slot="content" class="multiline-tooltip">
              <div v-for="item in scope.row.supplierTypeList" :key="item">{{ item }} </div>
            </div>
            <div class="multiline-content">
              <div v-for="item in scope.row.supplierTypeList" :key="item">{{ item }} </div>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作时间" prop="updateTime" width="200" />
      <el-table-column label="年份" prop="year" />
      <el-table-column width="180" label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleEdit(scope.row, 'view')">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row, 'edit')">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <add ref="addDialog" :visible.sync="addDialogVisible" @refresh="resetQuery"></add>
    <process-import ref="processImport" @dialog="handleQuery"></process-import>
    <history-dialog :visible.sync="historyDialogVisible" :row="currentRow" />
    <edit-dialog :visible.sync="editDialogVisible" :type="currentType" :row="currentRow" @refresh="getList" />
  </div>
</template>

<script>
import add from "./components/add.vue";
import {
  apiGetPage,
  apiGetSecondCategory,
  exportData,
  apiGetDeptByYear
} from "@/api/dimensionData/classify.js";
import ProcessImport from "./components/processImport.vue";
import EditScoreDialog from "./components/edit.vue";
import EditDialog from './components/edit.vue'

import HistoryDialog from "./components/historyDialog.vue";
export default {
  components: {
    EditDialog,
    add,
    ProcessImport,
    EditScoreDialog,
    HistoryDialog
  },
  data() {
    return {
      secondCategoryList: [],
      currentRow: {},
      // 修改历史弹出层
      historyDialogVisible: false,
      // 当前选中行id
      currentId: '',
      // 当前选中行类型
      currentType: 'view',
      editDialogVisible: false,
      // 添加弹出层
      addDialogVisible: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 项目名称显示弹出层
      itemOpen: false,
      // 查询参数
      dateRange: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        query: undefined, //模块名称

        year: new Date().getFullYear().toString(), // 默认为今年
        deptNo: undefined, // 部门
      },
      //机组详情
      posidList: {},
    };
  },
  created() {
    this.getList();
    this.getDeptList(); // 获取部门列表
  },
  methods: {
    //导入
    handleImp() {
      this.$refs.processImport.init();
    },
    //导出
    exportData() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请先选择要导出的数据");
        return;
      }
      exportData({ ids: this.ids, ...this.queryParams }).then((res) => {
        const url = window.URL.createObjectURL(new Blob([res]));
        const link = document.createElement("a");
        link.target = "_blank";
        link.href = url;
        link.setAttribute("download", "质量指标.xlsx");
        document.body.appendChild(link);
        link.click();
      });
    },
    exportDataAll() {
      exportData({ ...this.queryParams }).then((res) => {
        const url = window.URL.createObjectURL(new Blob([res]));
        const link = document.createElement("a");
        link.target = "_blank";
        link.href = url;
        link.setAttribute("download", "质量指标.xlsx");
        document.body.appendChild(link);
        link.click();
      });
    },
    //功能开发中弹窗提示
    openBatch() {
      this.$modal.msgError('功能开发中');
    },
    // 获取部门列表
    getDeptList() {
      if (!this.queryParams.year) {
        this.queryParams.year = new Date().getFullYear().toString();
      }
      apiGetDeptByYear({ year: this.queryParams.year }).then((response) => {
        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          this.secondCategoryList = response.data;
        } else {
          this.secondCategoryList = [];
        }
        this.queryParams.deptNo = undefined;
      }).catch(error => {
        console.error("获取部门列表失败", error);
        this.secondCategoryList = [];
        this.queryParams.deptNo = undefined;

      });
    },
    // 获取修改历史
    historyHandle(row) {
      this.historyDialogVisible = true
      this.currentRow = row
    },

    // 导入
    handleImp() {
      this.$refs.processImport.init();
    },

    /** 分页查询 */
    getList() {
      this.loading = true;
      if (this.dateRange) {
        this.queryParams.startTime = this.dateRange[0]
        this.queryParams.endTime = this.dateRange[1]
      } else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
      }
      apiGetPage(this.queryParams).then((response) => {
        this.postList = response.data.records.map((item) => {
          return {
            ...item,
            supplierTypeList: item.supplierTypeList.map((item) => item.id + ' ' + { 0: '不适用', 1: '适用' }[item.applyStatus] + '')
          };
        });
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetForm() {
      this.dateRange = false
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        query: undefined,
        year: new Date().getFullYear().toString(), // 默认为今年
        deptNo: undefined, // 清空部门选择
      };
      this.getDeptList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.addDialog.init()
    },
    handleEdit(row, type) {
      this.currentRow = row
      this.currentType = type
      this.editDialogVisible = true
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delProductById(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    yearChange(val) {
      // 清空已选择的部门
      this.queryParams.deptNo = undefined;
      // 清空部门列表
      this.secondCategoryList = [];
      // 获取新的部门列表
      this.getDeptList();
    },
    // 处理清除部门选择
    handleClearDept() {
      this.queryParams.deptNo = undefined;
      this.getList();
    },
    // 处理部门选择变化
    handleDeptChange(val) {
      // 如果选择了部门，更新查询参数并获取列表
      if (val) {
        this.queryParams.deptNo = val;
        this.getList();
      }
    },
  },
};
</script>

<style scoped>
.multiline-tooltip {
  display: flex;
  width: 230px;
  flex-wrap: wrap;
  justify-content: space-between;
}

::v-deep .el-dialog__body {
  line-height: 36px;
}

.item-center {
  display: flex;
  align-items: center;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.font-size-14 {
  font-size: 14px;
}

.grey {
  color: #999;
}

.yu {
  background: #fef8e6;
  border: 1px solid rgba(250, 193, 22, 0.4);
  color: #fac116;
}

.bao {
  background: #fdeded;
  border: 1px solid rgba(228, 121, 133, 0.4);
  color: #e47985;
}

.jin {
  background: #e8f4ff;
  border: 1px solid rgba(56, 154, 255, 0.4);
  color: #389aff;
}

.finish {
  background: #e7faf0;
  border: 1px solid rgba(101, 210, 153, 0);
  color: #65d299;
}

/* 多行内容样式 */
.multiline-content {
  white-space: pre-line;
  /* 保留换行符并允许文本换行 */
  word-break: break-all;
  /* 允许在任意字符间换行 */
  line-height: 1.5;
  /* 行高 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

<style>
/* 全局样式，使tooltip内容保留换行 */
.multiline-tooltip .el-tooltip__popper {
  white-space: pre-line;
  max-width: 400px;
  word-break: break-all;
  line-height: 1.5;
}
</style>
